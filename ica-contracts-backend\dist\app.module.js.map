{"version": 3, "file": "app.module.js", "sourceRoot": "/", "sources": ["app.module.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAAwC;AACxC,2CAA8C;AAC9C,+CAAkD;AAElD,oDAAgD;AAChD,mEAA+D;AAC/D,kHAA4G;AAC5G,qEAAiE;AACjE,iFAA6E;AAC7E,+DAA2D;AAC3D,qEAAiE;AACjE,iFAA4E;AAC5E,6FAAwF;AACxF,4DAAwD;AACxD,kEAA8D;AAC9D,kEAA8D;AAC9D,uIAAiI;AACjI,wEAAoE;AACpE,kEAA8D;AAC9D,+DAA2D;AAC3D,0FAAqF;AACrF,kEAA8D;AAC9D,wEAAoE;AACpE,wEAAoE;AACpE,qFAAiF;AACjF,+DAA2D;AAC3D,yDAAqD;AACrD,oFAA+E;AAC/E,wEAAoE;AACpE,mEAA+D;AAC/D,2EAAsE;AACtE,8EAA0E;AAC1E,yDAAqD;AACrD,6FAAuF;AACvF,qEAAiE;AACjE,qEAAiE;AACjE,qEAAiE;AACjE,0EAAqE;AACrE,0DAAsD;AACtD,uFAAmF;AACnF,8EAAyE;AACzE,+DAA2D;AAiDpD,IAAM,SAAS,GAAf,MAAM,SAAS;CAAG,CAAA;AAAZ,8BAAS;oBAAT,SAAS;IAhDrB,IAAA,eAAM,EAAC;QACN,OAAO,EAAE;YACP,qBAAY,CAAC,OAAO,CAAC;gBACnB,QAAQ,EAAE,IAAI;aACf,CAAC;YACF,4BAAY;YACZ,8BAAa;YACb,wBAAU;YACV,0BAAW;YACX,mCAAe;YACf,4BAAY;YACZ,wBAAU;YACV,gCAAc;YACd,oCAAgB;YAChB,8BAAa;YACb,8BAAa;YACb,0BAAW;YACX,sBAAS;YACT,4BAAY;YACZ,0DAA0B;YAC1B,4BAAY;YACZ,sBAAS;YACT,kCAAe;YACf,yBAAc,CAAC,OAAO,EAAE;YACxB,qCAAgB;YAChB,iCAAc;YACd,gCAAc;YACd,gCAAc;YACd,6CAAoB;YACpB,uCAAiB;YACjB,gCAAc;YACd,4BAAY;YACZ,8BAAa;YACb,wEAAiC;YACjC,8BAAa;YACb,0BAAW;YACX,2CAAmB;YACnB,wCAAkB;YAClB,sCAAiB;YACjB,4BAAY;YACZ,mCAAe;YACf,4CAAmB;YACnB,0CAAmB;YACnB,0BAAW;SACZ;QACD,SAAS,EAAE,EAAE;QACb,WAAW,EAAE,EAAE;KAChB,CAAC;GACW,SAAS,CAAG", "sourcesContent": ["import { Module } from '@nestjs/common';\r\nimport { ConfigModule } from '@nestjs/config';\r\nimport { ScheduleModule } from '@nestjs/schedule';\r\n\r\nimport { ApisModule } from './apis/apis.module';\r\nimport { MessagingModule } from './messaging/messaging.module';\r\nimport { AccountTransferLimitModule } from './modules/account-transfer-limit/account-transfer-limit.module';\r\nimport { AccountModule } from './modules/account/account.module';\r\nimport { AcquisitionModule } from './modules/acquisition/acquisition.module';\r\nimport { AdminModule } from './modules/admin/admin.module';\r\nimport { AdvisorModule } from './modules/advisor/advisor.module';\r\nimport { AppVersionModule } from './modules/app-version/app-version.module';\r\nimport { AuthBackofficeModule } from './modules/auth-backoffice/auth-backoffice.module';\r\nimport { AuthModule } from './modules/auth/auth.module';\r\nimport { BoletoModule } from './modules/boleto/boleto.module';\r\nimport { BrokerModule } from './modules/broker/broker.module';\r\nimport { ContractLifecycleMonitoringModule } from './modules/contract-lifecycle-monitoring/contract-lifecycle-monitoring.module';\r\nimport { ContractModule } from './modules/contract/contract.module';\r\nimport { DeviceModule } from './modules/device/device.module';\r\nimport { GoalsModule } from './modules/goals/goals.module';\r\nimport { IncomePaymentModule } from './modules/income-payment/income-payment.module';\r\nimport { IncomeModule } from './modules/income/income.module';\r\nimport { InvestorModule } from './modules/investor/investor.module';\r\nimport { LocationModule } from './modules/location/location.module';\r\nimport { NotificationModule } from './modules/notifications/notification.module';\r\nimport { OwnerModule } from './modules/owner/owner.module';\r\nimport { PixModule } from './modules/pix/pix.module';\r\nimport { PreRegisterModule } from './modules/pre-register/pre-register.module';\r\nimport { RechargeModule } from './modules/recharge/recharge.module';\r\nimport { ReportModule } from './modules/reports/report.module';\r\nimport { StatistcModule } from './modules/statistic/statistic.module';\r\nimport { SuperAdminModule } from './modules/superadmin/superadmin.module';\r\nimport { TedModule } from './modules/ted/ted.module';\r\nimport { TwoFactorAuthModule } from './modules/two-factor-auth/two-factor-auth.module';\r\nimport { UploadsModule } from './modules/uploads/uploads.module';\r\nimport { WalletsModule } from './modules/wallets/wallets.module';\r\nimport { WebhookModule } from './modules/webhook/webhook.module';\r\nimport { BrHolidayModule } from './shared/modules/br-holiday.module';\r\nimport { SharedModule } from './shared/shared.module';\r\nimport { IncomeReportsModule } from './modules/income-report/income-report.module';\r\nimport { ClientAppModule } from './modules/client-app/client-app.module';\r\nimport { AuditModule } from './modules/audit/audit.module';\r\n@Module({\r\n  imports: [\r\n    ConfigModule.forRoot({\r\n      isGlobal: true,\r\n    }),\r\n    SharedModule,\r\n    AccountModule,\r\n    AuthModule,\r\n    OwnerModule,\r\n    ClientAppModule,\r\n    BrokerModule,\r\n    ApisModule,\r\n    InvestorModule,\r\n    SuperAdminModule,\r\n    WebhookModule,\r\n    AdvisorModule,\r\n    AdminModule,\r\n    PixModule,\r\n    DeviceModule,\r\n    AccountTransferLimitModule,\r\n    BoletoModule,\r\n    TedModule,\r\n    MessagingModule,\r\n    ScheduleModule.forRoot(),\r\n    AppVersionModule,\r\n    StatistcModule,\r\n    LocationModule,\r\n    RechargeModule,\r\n    AuthBackofficeModule,\r\n    PreRegisterModule,\r\n    ContractModule,\r\n    IncomeModule,\r\n    WalletsModule,\r\n    ContractLifecycleMonitoringModule,\r\n    UploadsModule,\r\n    GoalsModule,\r\n    IncomePaymentModule,\r\n    NotificationModule,\r\n    AcquisitionModule,\r\n    ReportModule,\r\n    BrHolidayModule,\r\n    TwoFactorAuthModule,\r\n    IncomeReportsModule,\r\n    AuditModule \r\n  ],\r\n  providers: [],\r\n  controllers: [],\r\n})\r\nexport class AppModule {}\r\n"]}