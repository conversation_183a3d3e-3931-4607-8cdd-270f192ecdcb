"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IConsultAdminAccountExtract = void 0;
const class_validator_1 = require("class-validator");
const transaction_movement_type_enum_1 = require("../../../shared/enums/transaction-movement-type.enum");
class IConsultAdminAccountExtract {
}
exports.IConsultAdminAccountExtract = IConsultAdminAccountExtract;
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.IsISO8601)({ strict: true }, { message: 'A data de início deve estar no formato ISO 8601' }),
    __metadata("design:type", String)
], IConsultAdminAccountExtract.prototype, "DateFrom", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.IsISO8601)({ strict: true }, { message: 'A data de início deve estar no formato ISO 8601' }),
    __metadata("design:type", String)
], IConsultAdminAccountExtract.prototype, "DateTo", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], IConsultAdminAccountExtract.prototype, "LimitPerPage", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], IConsultAdminAccountExtract.prototype, "Page", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(transaction_movement_type_enum_1.TransactionMovementTypeEnum),
    __metadata("design:type", String)
], IConsultAdminAccountExtract.prototype, "transactionType", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], IConsultAdminAccountExtract.prototype, "accountId", void 0);
//# sourceMappingURL=consult-admin-account-extract.dto.js.map