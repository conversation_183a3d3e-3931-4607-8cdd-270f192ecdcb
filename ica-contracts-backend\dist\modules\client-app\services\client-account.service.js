"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClientAccountService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const addendum_entity_1 = require("../../../shared/database/typeorm/entities/addendum.entity");
const contract_entity_1 = require("../../../shared/database/typeorm/entities/contract.entity");
const income_payment_scheduled_entity_1 = require("../../../shared/database/typeorm/entities/income-payment-scheduled.entity");
const owner_role_relation_entity_1 = require("../../../shared/database/typeorm/entities/owner-role-relation.entity");
const contract_status_enum_1 = require("../../../shared/enums/contract-status.enum");
const typeorm_2 = require("typeorm");
let ClientAccountService = class ClientAccountService {
    constructor(ownerRoleRelationRepository, contractRepository, incomePaymentScheduledRepository, formatDate) {
        this.ownerRoleRelationRepository = ownerRoleRelationRepository;
        this.contractRepository = contractRepository;
        this.incomePaymentScheduledRepository = incomePaymentScheduledRepository;
        this.formatDate = formatDate;
    }
    async getAccountInfo(userRequestId) {
        const userProfile = await this.ownerRoleRelationRepository.findOne({
            where: [
                { ownerId: userRequestId },
                { businessId: userRequestId },
                { id: userRequestId },
            ],
            relations: {
                owner: true,
                business: true,
            },
        });
        const userId = userProfile.id;
        console.log(userId);
        const contracts = await this.contractRepository.find({
            where: {
                investor: { id: userId },
                status: contract_status_enum_1.ContractStatusEnum.ACTIVE,
            },
            relations: {
                signataries: true,
                addendum: { addendumFiles: { file: true } },
            },
            order: {
                createdAt: 'ASC',
            },
        });
        if (!contracts || contracts.length === 0) {
            return {
                name: '',
                yield: 0,
                nextPayment: '',
                contractDate: '',
                totalInvested: 0,
                paymentAmount: 0,
                contracts: [],
            };
        }
        const name = userProfile?.owner?.name || userProfile?.business?.companyName || '';
        const clientContracts = contracts.map((contract) => {
            const investmentValue = contract.signataries[0]?.investmentValue || 0;
            return {
                id: contract.id,
                type: 'starter',
                date: this.formatDate(contract.startContract),
                invested: investmentValue,
                contract: contract.contractPdf || '',
            };
        });
        const additiveContracts = contracts.flatMap((contract) => contract.addendum
            .filter((addendum) => addendum.status === addendum_entity_1.AddendumStatus.FULLY_SIGNED)
            .filter((addendum) => addendum.addendumFiles && addendum.addendumFiles.length > 0)
            .map((addendum) => ({
            id: addendum.id,
            type: 'additive',
            date: this.formatDate(addendum.applicationDate),
            invested: addendum.value,
            contract: addendum.addendumFiles[0]?.file?.url || '',
        })));
        const allContracts = [...clientContracts, ...additiveContracts];
        const totalInvested = allContracts.reduce((total, contract) => total + contract.invested, 0);
        const starterContract = clientContracts[0];
        const contractDate = starterContract ? starterContract.date : '';
        const today = new Date();
        const nextPaymentScheduled = await this.incomePaymentScheduledRepository
            .createQueryBuilder('payment')
            .innerJoinAndSelect('payment.contract', 'contract')
            .innerJoinAndSelect('contract.investor', 'investor')
            .where('investor.id = :userId', { userId })
            .andWhere('payment.scheduledDate >= :today', { today })
            .orderBy('payment.scheduledDate', 'ASC')
            .getOne();
        const nextPayment = nextPaymentScheduled
            ? this.formatDate(nextPaymentScheduled.scheduledDate)
            : '';
        const yieldPercentage = contracts[contracts.length - 1]?.signataries[0]?.investmentYield || 0;
        const paymentAmount = totalInvested * (yieldPercentage / 100);
        return {
            name,
            yield: yieldPercentage,
            nextPayment,
            contractDate,
            totalInvested,
            paymentAmount,
            contracts: allContracts,
        };
    }
};
exports.ClientAccountService = ClientAccountService;
exports.ClientAccountService = ClientAccountService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(owner_role_relation_entity_1.OwnerRoleRelationEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(contract_entity_1.ContractEntity)),
    __param(2, (0, typeorm_1.InjectRepository)(income_payment_scheduled_entity_1.IncomePaymentScheduledEntity)),
    __param(3, (0, common_1.Inject)('FORMAT_DATE')),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository, Function])
], ClientAccountService);
//# sourceMappingURL=client-account.service.js.map