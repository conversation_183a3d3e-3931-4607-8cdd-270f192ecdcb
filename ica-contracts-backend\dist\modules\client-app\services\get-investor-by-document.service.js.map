{"version": 3, "file": "get-investor-by-document.service.js", "sourceRoot": "/", "sources": ["modules/client-app/services/get-investor-by-document.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA,2CAIwB;AACxB,6CAAmD;AACnD,+FAAsF;AACtF,yFAAgF;AAChF,iEAAwD;AACxD,qCAAqC;AAIrC,qEAAgE;AAGzD,IAAM,4BAA4B,GAAlC,MAAM,4BAA4B;IACvC,YAEU,OAAgC,EAEhC,UAAsC,EACtC,oBAA0C;QAH1C,YAAO,GAAP,OAAO,CAAyB;QAEhC,eAAU,GAAV,UAAU,CAA4B;QACtC,yBAAoB,GAApB,oBAAoB,CAAsB;IACjD,CAAC;IAEJ,KAAK,CAAC,OAAO,CACX,IAA8B,EAC9B,MAAc;QAGd,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,mBAAmB,CAAC;QAE1E,IAAI,MAAM,KAAK,WAAW,EAAE,CAAC;YAC3B,MAAM,IAAI,8BAAqB,CAAC,uBAAuB,CAAC,CAAC;QAC3D,CAAC;QAED,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;QAC1B,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAC9C,IAAI,MAAc,CAAC;QAEnB,IAAI,YAAY,KAAK,KAAK,EAAE,CAAC;YAC3B,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;gBACvC,SAAS,EAAE,CAAC,mBAAmB,EAAE,wBAAwB,CAAC;gBAC1D,KAAK,EAAE;oBACL,GAAG,EAAE,QAAQ;iBACd;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,MAAM,IAAI,0BAAiB,CAAC,2BAA2B,CAAC,CAAC;YAC3D,CAAC;YAGD,MAAM,eAAe,GAAG,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAClD,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAS,CAAC,QAAQ,CACxD,CAAC;YAEF,IAAI,CAAC,eAAe,EAAE,CAAC;gBACrB,MAAM,IAAI,8BAAqB,CAC7B,2CAA2C,CAC5C,CAAC;YACJ,CAAC;YAED,MAAM,GAAG,KAAK,CAAC,iBAAiB,CAAC,IAAI,CACnC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAS,CAAC,QAAQ,CACxD,EAAE,EAAE,CAAC;QACR,CAAC;aAAM,IAAI,YAAY,KAAK,MAAM,EAAE,CAAC;YACnC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;gBAC7C,SAAS,EAAE,CAAC,mBAAmB,EAAE,wBAAwB,CAAC;gBAC1D,KAAK,EAAE;oBACL,IAAI,EAAE,QAAQ;iBACf;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,0BAAiB,CAAC,2BAA2B,CAAC,CAAC;YAC3D,CAAC;YAGD,MAAM,eAAe,GAAG,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CACrD,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAS,CAAC,QAAQ,CACxD,CAAC;YAEF,IAAI,CAAC,eAAe,EAAE,CAAC;gBACrB,MAAM,IAAI,8BAAqB,CAC7B,2CAA2C,CAC5C,CAAC;YACJ,CAAC;YAED,MAAM,GAAG,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CACtC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAS,CAAC,QAAQ,CACxD,EAAE,EAAE,CAAC;QACR,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,0BAAiB,CAAC,4BAA4B,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,0BAAiB,CAAC,iCAAiC,CAAC,CAAC;QACjE,CAAC;QAGD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAE3E,OAAO;YACL,OAAO,EAAE,WAAW;SACrB,CAAC;IACJ,CAAC;IAEO,SAAS,CAAC,QAAgB;QAChC,IAAI,QAAQ,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACzB,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;CACF,CAAA;AAlGY,oEAA4B;uCAA5B,4BAA4B;IADxC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,0BAAW,CAAC,CAAA;IAE7B,WAAA,IAAA,0BAAgB,EAAC,gCAAc,CAAC,CAAA;qCADhB,oBAAU;QAEP,oBAAU;QACA,6CAAoB;GANzC,4BAA4B,CAkGxC", "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unsafe-enum-comparison */\r\nimport {\r\n  Injectable,\r\n  NotFoundException,\r\n  UnauthorizedException,\r\n} from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { BusinessEntity } from 'src/shared/database/typeorm/entities/business.entity';\r\nimport { OwnerEntity } from 'src/shared/database/typeorm/entities/owner.entity';\r\nimport { RolesEnum } from 'src/shared/enums/roles.enum';\r\nimport { Repository } from 'typeorm';\r\n\r\nimport { GetInvestorByDocumentDto } from '../dto/get-investor-by-document.dto';\r\nimport { IClientLoginResponse } from '../responses/client-login.response';\r\nimport { ClientAccountService } from './client-account.service';\r\n\r\n@Injectable()\r\nexport class GetInvestorByDocumentService {\r\n  constructor(\r\n    @InjectRepository(OwnerEntity)\r\n    private ownerDb: Repository<OwnerEntity>,\r\n    @InjectRepository(BusinessEntity)\r\n    private businessDb: Repository<BusinessEntity>,\r\n    private clientAccountService: ClientAccountService,\r\n  ) {}\r\n\r\n  async perform(\r\n    data: GetInvestorByDocumentDto,\r\n    apiKey: string,\r\n  ): Promise<Omit<IClientLoginResponse, 'accessToken' | 'refreshToken'>> {\r\n    // Verificar API key (você deve substituir isso com a chave real que você deseja usar)\r\n    const validApiKey = process.env.CLIENT_APP_API_KEY || 'icainvest-api-key';\r\n\r\n    if (apiKey !== validApiKey) {\r\n      throw new UnauthorizedException('Chave de API inválida');\r\n    }\r\n\r\n    const { document } = data;\r\n    const typeDocument = this.cpfOrCnpj(document);\r\n    let userId: string;\r\n\r\n    if (typeDocument === 'cpf') {\r\n      const owner = await this.ownerDb.findOne({\r\n        relations: ['ownerRoleRelation', 'ownerRoleRelation.role'],\r\n        where: {\r\n          cpf: document,\r\n        },\r\n      });\r\n\r\n      if (!owner) {\r\n        throw new NotFoundException('Investidor não encontrado');\r\n      }\r\n\r\n      // Verificar se o proprietário tem role de investidor\r\n      const hasInvestorRole = owner.ownerRoleRelation.some(\r\n        (relation) => relation.role.name === RolesEnum.INVESTOR,\r\n      );\r\n\r\n      if (!hasInvestorRole) {\r\n        throw new UnauthorizedException(\r\n          'Acesso permitido apenas para investidores',\r\n        );\r\n      }\r\n\r\n      userId = owner.ownerRoleRelation.find(\r\n        (relation) => relation.role.name === RolesEnum.INVESTOR,\r\n      )?.id;\r\n    } else if (typeDocument === 'cnpj') {\r\n      const business = await this.businessDb.findOne({\r\n        relations: ['ownerRoleRelation', 'ownerRoleRelation.role'],\r\n        where: {\r\n          cnpj: document,\r\n        },\r\n      });\r\n\r\n      if (!business) {\r\n        throw new NotFoundException('Investidor não encontrado');\r\n      }\r\n\r\n      // Verificar se o negócio tem role de investidor\r\n      const hasInvestorRole = business.ownerRoleRelation.some(\r\n        (relation) => relation.role.name === RolesEnum.INVESTOR,\r\n      );\r\n\r\n      if (!hasInvestorRole) {\r\n        throw new UnauthorizedException(\r\n          'Acesso permitido apenas para investidores',\r\n        );\r\n      }\r\n\r\n      userId = business.ownerRoleRelation.find(\r\n        (relation) => relation.role.name === RolesEnum.INVESTOR,\r\n      )?.id;\r\n    } else {\r\n      throw new NotFoundException('Tipo de documento inválido');\r\n    }\r\n\r\n    if (!userId) {\r\n      throw new NotFoundException('ID de investidor não encontrado');\r\n    }\r\n\r\n    // Buscar informações da conta do cliente\r\n    const accountInfo = await this.clientAccountService.getAccountInfo(userId);\r\n\r\n    return {\r\n      account: accountInfo,\r\n    };\r\n  }\r\n\r\n  private cpfOrCnpj(document: string): string {\r\n    if (document.length > 11) {\r\n      return 'cnpj';\r\n    }\r\n    return 'cpf';\r\n  }\r\n}\r\n"]}