{"version": 3, "file": "create-contract-manual-response.dto.js", "sourceRoot": "/", "sources": ["modules/contract/dto/create-contract-manual-response.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,6CAA8C;AAE9C,MAAa,8BAA8B;CAK1C;AALD,wEAKC;AAHC;IADC,IAAA,qBAAW,GAAE;;kEACK;AAEnB;IADC,IAAA,qBAAW,GAAE;;8DACC", "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\n\r\nexport class CreateExistingContractResponse {\r\n  @ApiProperty()\r\n  contractId: string;\r\n  @ApiProperty()\r\n  status: string;\r\n}"]}