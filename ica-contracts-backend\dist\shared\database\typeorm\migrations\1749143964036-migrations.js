"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1749143964036 = void 0;
class Migrations1749143964036 {
    constructor() {
        this.name = 'Migrations1749143964036';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "contract_deletion" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "contract_id" uuid NOT NULL, "deleted_by_id" uuid NOT NULL, "reason" text NOT NULL, "deleted_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_c2540b8ae09ef4084ad579c41b4" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "contract_deletion" ADD CONSTRAINT "FK_6709416e6166af82ffb1a094fc1" FOREIGN KEY ("contract_id") REFERENCES "contract"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract_deletion" ADD CONSTRAINT "FK_f7a4a72bfa952689750c9db0399" FOREIGN KEY ("deleted_by_id") REFERENCES "owner_role_relation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "contract_deletion" DROP CONSTRAINT "FK_f7a4a72bfa952689750c9db0399"`);
        await queryRunner.query(`ALTER TABLE "contract_deletion" DROP CONSTRAINT "FK_6709416e6166af82ffb1a094fc1"`);
        await queryRunner.query(`DROP TABLE "contract_deletion"`);
    }
}
exports.Migrations1749143964036 = Migrations1749143964036;
//# sourceMappingURL=1749143964036-migrations.js.map