{"version": 3, "file": "1728568803915-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1728568803915-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAiBnC,CAAC;IAfQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,oQAAoQ,CACrQ,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,mMAAmM,CACpM,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,0FAA0F,CAC3F,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,wCAAwC,CAAC,CAAC;IACpE,CAAC;CACF;AAlBD,0DAkBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1728568803915 implements MigrationInterface {\r\n  name = 'Migrations1728568803915';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"contract_event_attachment\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"file_url\" text NOT NULL, \"file_name\" character varying(255) NOT NULL, \"contract_event_id\" uuid NOT NULL, CONSTRAINT \"PK_23b9222cd3d55cf4f3688b8baa1\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract_event_attachment\" ADD CONSTRAINT \"FK_fa9466d0f494941cf4bc83d3cc1\" FOREIGN KEY (\"contract_event_id\") REFERENCES \"contract_event\"(\"id\") ON DELETE CASCADE ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: Query<PERSON>unner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract_event_attachment\" DROP CONSTRAINT \"FK_fa9466d0f494941cf4bc83d3cc1\"`,\r\n    );\r\n    await queryRunner.query(`DROP TABLE \"contract_event_attachment\"`);\r\n  }\r\n}\r\n"]}