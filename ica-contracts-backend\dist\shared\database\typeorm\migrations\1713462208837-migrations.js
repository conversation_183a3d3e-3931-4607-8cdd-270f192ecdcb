"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations************* = void 0;
class Migrations************* {
    constructor() {
        this.name = 'Migrations*************';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "favorite_pix" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "account_id" uuid, "account_document" character varying NOT NULL, "name" character varying NOT NULL, "account_number" character varying NOT NULL, "account_branch" character varying NOT NULL, "account_type" character varying NOT NULL, "account_bank" character varying NOT NULL, "alias" character varying, "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, CONSTRAINT "PK_8036547b3ab2e4af317048ff597" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "favorite_pix" ADD CONSTRAINT "FK_9986c36d5cfcf84bb0f072b62ea" FOREIGN KEY ("account_id") REFERENCES "account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "favorite_pix" DROP CONSTRAINT "FK_9986c36d5cfcf84bb0f072b62ea"`);
        await queryRunner.query(`DROP TABLE "favorite_pix"`);
    }
}
exports.Migrations************* = Migrations*************;
//# sourceMappingURL=*************-migrations.js.map