"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1709918663925 = void 0;
class Migrations1709918663925 {
    constructor() {
        this.name = 'Migrations1709918663925';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "address" ALTER COLUMN "complement" DROP NOT NULL`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "address" ALTER COLUMN "complement" SET NOT NULL`);
    }
}
exports.Migrations1709918663925 = Migrations1709918663925;
//# sourceMappingURL=1709918663925-migrations.js.map