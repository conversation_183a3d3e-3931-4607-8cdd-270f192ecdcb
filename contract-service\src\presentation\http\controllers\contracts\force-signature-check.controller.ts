import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/presentation/http/protocols'
import type { HttpRequest, HttpResponse } from '@/presentation/http/protocols'
import { badRequest, ok, serverError } from '@/presentation/http/helpers'
import type { ProcessAuditSignatureUseCase } from '@/application/usecases/process-audit-signature/process-audit-signature.usecase'
import type { ProcessInvestorSignatureUseCase } from '@/application/usecases/process-investor-signature/process-investor-signature.usecase'

interface ForceSignatureCheckRequest {
  contractId: string
}

export class ForceSignatureCheckController implements IController {
  constructor(
    private readonly processAuditSignatureUseCase: ProcessAuditSignatureUseCase,
    private readonly processInvestorSignatureUseCase: ProcessInvestorSignatureUseCase
  ) {}

  async handle(request: HttpRequest<any>): Promise<HttpResponse> {
    try {
      const { contractId } = request.params as ForceSignatureCheckRequest

      if (!contractId) {
        return badRequest({ error: 'Contract ID is required' })
      }

      // Primeiro tenta processar como assinatura de investidor
      const investorResult = await this.processInvestorSignatureUseCase.execute({
        id: contractId,
        status: undefined as any
      })

      if (investorResult.isLeft()) {
        // Se falhou, tenta processar como assinatura de auditoria
        const auditResult = await this.processAuditSignatureUseCase.execute({
          id: contractId,
          status: undefined as any
        })

        if (auditResult.isLeft()) {
          return serverError(auditResult.value)
        }

        return ok({
          contractId,
          status: auditResult.value,
          message: 'Signature status checked and updated successfully'
        })
      }

      return ok({
        contractId,
        status: investorResult.value,
        message: 'Signature status checked and updated successfully'
      })
    } catch (error) {
      return serverError(error as Error)
    }
  }
}
