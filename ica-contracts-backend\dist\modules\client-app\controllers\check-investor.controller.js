"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CheckInvestorController = void 0;
const common_1 = require("@nestjs/common");
const check_investor_dto_1 = require("../dto/check-investor.dto");
const check_investor_service_1 = require("../services/check-investor.service");
let CheckInvestorController = class CheckInvestorController {
    constructor(checkInvestorService) {
        this.checkInvestorService = checkInvestorService;
    }
    async checkInvestor(body) {
        try {
            const result = await this.checkInvestorService.perform(body);
            return result;
        }
        catch (error) {
            throw new common_1.HttpException({
                status: common_1.HttpStatus.BAD_REQUEST,
            }, common_1.HttpStatus.BAD_REQUEST, {
                cause: error,
            });
        }
    }
};
exports.CheckInvestorController = CheckInvestorController;
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [check_investor_dto_1.CheckInvestorDto]),
    __metadata("design:returntype", Promise)
], CheckInvestorController.prototype, "checkInvestor", null);
exports.CheckInvestorController = CheckInvestorController = __decorate([
    (0, common_1.Controller)('client/check-investor'),
    __metadata("design:paramtypes", [check_investor_service_1.CheckInvestorService])
], CheckInvestorController);
//# sourceMappingURL=check-investor.controller.js.map