{"version": 3, "file": "1713450040509-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1713450040509-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAWnC,CAAC;IATQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,0VAA0V,CAC3V,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;IACtD,CAAC;CACF;AAZD,0DAYC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1713450040509 implements MigrationInterface {\r\n  name = 'Migrations1713450040509';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"app_version\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"android_version\" character varying NOT NULL DEFAULT '0', \"ios_version\" character varying NOT NULL DEFAULT '0', \"created_at\" TIMESTAMP NOT NULL DEFAULT now(), \"updated_at\" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT \"PK_f2573b981a7eac664875e7483ac\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(`DROP TABLE \"app_version\"`);\r\n  }\r\n}\r\n"]}