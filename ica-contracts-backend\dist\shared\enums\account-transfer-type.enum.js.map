{"version": 3, "file": "account-transfer-type.enum.js", "sourceRoot": "/", "sources": ["shared/enums/account-transfer-type.enum.ts"], "names": [], "mappings": ";;;AAAA,IAAY,uBAKX;AALD,WAAY,uBAAuB;IACjC,oCAAW,CAAA;IACX,oCAAW,CAAA;IACX,oCAAW,CAAA;IACX,oCAAW,CAAA;AACb,CAAC,EALW,uBAAuB,uCAAvB,uBAAuB,QAKlC", "sourcesContent": ["export enum AccountTransferTypeEnum {\r\n  'CC' = 'CC',\r\n  'CI' = 'CI',\r\n  'PG' = 'PG',\r\n  'PP' = 'PP',\r\n}\r\n"]}