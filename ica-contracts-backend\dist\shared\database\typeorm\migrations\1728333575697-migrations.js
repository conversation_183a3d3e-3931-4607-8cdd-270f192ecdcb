"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1728333575697 = void 0;
class Migrations1728333575697 {
    constructor() {
        this.name = 'Migrations1728333575697';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "contract" ADD "old_contract_pdf" character varying`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "old_contract_pdf"`);
    }
}
exports.Migrations1728333575697 = Migrations1728333575697;
//# sourceMappingURL=1728333575697-migrations.js.map