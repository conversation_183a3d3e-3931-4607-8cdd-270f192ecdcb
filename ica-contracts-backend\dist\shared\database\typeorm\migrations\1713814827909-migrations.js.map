{"version": 3, "file": "1713814827909-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1713814827909-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAWnC,CAAC;IATQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,0WAA0W,CAC3W,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;IACpD,CAAC;CACF;AAZD,0DAYC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1713814827909 implements MigrationInterface {\r\n  name = 'Migrations1713814827909';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"statistic\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"investors\" integer NOT NULL, \"total_applied\" numeric(10,2) NOT NULL, \"jsonb\" character varying NOT NULL, \"updated_at\" TIMESTAMP NOT NULL DEFAULT now(), \"created_at\" TIMESTAMP NOT NULL DEFAULT now(), \"deleted_at\" TIMESTAMP, CONSTRAINT \"PK_e3e6fd496e1988019d8a46749ae\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(`DROP TABLE \"statistic\"`);\r\n  }\r\n}\r\n"]}