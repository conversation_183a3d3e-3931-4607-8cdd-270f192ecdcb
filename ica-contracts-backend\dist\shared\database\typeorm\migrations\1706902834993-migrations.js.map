{"version": 3, "file": "1706902834993-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1706902834993-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAenC,CAAC;IAbQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACvE,MAAM,WAAW,CAAC,KAAK,CACrB,kKAAkK,CACnK,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,2EAA2E,CAC5E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC;IAC5E,CAAC;CACF;AAhBD,0DAgBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1706902834993 implements MigrationInterface {\r\n  name = 'Migrations1706902834993';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(`ALTER TABLE \"permission\" ADD \"role_id\" uuid`);\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"permission\" ADD CONSTRAINT \"FK_383892d758d08d346f837d3d8b7\" FOREIGN KEY (\"role_id\") REFERENCES \"role\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"permission\" DROP CONSTRAINT \"FK_383892d758d08d346f837d3d8b7\"`,\r\n    );\r\n    await queryRunner.query(`ALTER TABLE \"permission\" DROP COLUMN \"role_id\"`);\r\n  }\r\n}\r\n"]}