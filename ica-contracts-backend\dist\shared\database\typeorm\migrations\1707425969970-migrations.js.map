{"version": 3, "file": "1707425969970-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1707425969970-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAiDnC,CAAC;IA/CQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAC;QACxE,MAAM,WAAW,CAAC,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACvE,MAAM,WAAW,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAC;QAC3E,MAAM,WAAW,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;QACtE,MAAM,WAAW,CAAC,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACvE,MAAM,WAAW,CAAC,KAAK,CACrB,uKAAuK,CACxK,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,sKAAsK,CACvK,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,0KAA0K,CAC3K,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,qKAAqK,CACtK,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,kKAAkK,CACnK,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,2EAA2E,CAC5E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,sEAAsE,CACvE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,2EAA2E,CAC5E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,uEAAuE,CACxE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wEAAwE,CACzE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC;QAC1E,MAAM,WAAW,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACzE,MAAM,WAAW,CAAC,KAAK,CACrB,oDAAoD,CACrD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC;QAC1E,MAAM,WAAW,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAC;IAC7E,CAAC;CACF;AAlDD,0DAkDC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1707425969970 implements MigrationInterface {\r\n  name = 'Migrations1707425969970';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(`ALTER TABLE \"advisor\" ADD \"business_id\" uuid`);\r\n    await queryRunner.query(`ALTER TABLE \"broker\" ADD \"business_id\" uuid`);\r\n    await queryRunner.query(`ALTER TABLE \"superadmin\" ADD \"business_id\" uuid`);\r\n    await queryRunner.query(`ALTER TABLE \"admin\" ADD \"business_id\" uuid`);\r\n    await queryRunner.query(`ALTER TABLE \"permission\" ADD \"role_id\" uuid`);\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"advisor\" ADD CONSTRAINT \"FK_1b87021e9e988eb57b23557a779\" FOREIGN KEY (\"business_id\") REFERENCES \"business\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"broker\" ADD CONSTRAINT \"FK_4db78c5e0905ede9a579ed060bd\" FOREIGN KEY (\"business_id\") REFERENCES \"business\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"superadmin\" ADD CONSTRAINT \"FK_de7c99eb422a8e5e506ed99460e\" FOREIGN KEY (\"business_id\") REFERENCES \"business\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"admin\" ADD CONSTRAINT \"FK_08c71558c0fb298ca007dc58367\" FOREIGN KEY (\"business_id\") REFERENCES \"business\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"permission\" ADD CONSTRAINT \"FK_383892d758d08d346f837d3d8b7\" FOREIGN KEY (\"role_id\") REFERENCES \"role\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"permission\" DROP CONSTRAINT \"FK_383892d758d08d346f837d3d8b7\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"admin\" DROP CONSTRAINT \"FK_08c71558c0fb298ca007dc58367\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"superadmin\" DROP CONSTRAINT \"FK_de7c99eb422a8e5e506ed99460e\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"broker\" DROP CONSTRAINT \"FK_4db78c5e0905ede9a579ed060bd\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"advisor\" DROP CONSTRAINT \"FK_1b87021e9e988eb57b23557a779\"`,\r\n    );\r\n    await queryRunner.query(`ALTER TABLE \"permission\" DROP COLUMN \"role_id\"`);\r\n    await queryRunner.query(`ALTER TABLE \"admin\" DROP COLUMN \"business_id\"`);\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"superadmin\" DROP COLUMN \"business_id\"`,\r\n    );\r\n    await queryRunner.query(`ALTER TABLE \"broker\" DROP COLUMN \"business_id\"`);\r\n    await queryRunner.query(`ALTER TABLE \"advisor\" DROP COLUMN \"business_id\"`);\r\n  }\r\n}\r\n"]}