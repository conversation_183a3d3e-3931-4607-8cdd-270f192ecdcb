interface IClaim {
    id: string;
    claimType: string;
    key: string;
    keyType: string;
    claimerAccount: {
        participant: number;
        branch: number;
        account: number;
        accountType: string;
    };
    claimer: {
        type: string;
        taxId: number;
        name: string;
    };
    donorParticipant: number;
    donorAccount: {
        account: number;
        branch: number;
        taxId: number;
        name: string;
    };
    lastModified: string;
    confirmReason?: string;
    cancelReason?: string;
    resolutionPeriodEnd: string;
}
export interface IPixDictClaimOpen {
    entity: string;
    createTimestamp: string;
    status: string;
    body: IClaim;
}
export {};
