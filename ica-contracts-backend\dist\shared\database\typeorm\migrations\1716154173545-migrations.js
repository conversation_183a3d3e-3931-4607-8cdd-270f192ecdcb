"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1716154173545 = void 0;
class Migrations1716154173545 {
    constructor() {
        this.name = 'Migrations1716154173545';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "document" ADD "sent" boolean NOT NULL DEFAULT false`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "document" DROP COLUMN "sent"`);
    }
}
exports.Migrations1716154173545 = Migrations1716154173545;
//# sourceMappingURL=1716154173545-migrations.js.map