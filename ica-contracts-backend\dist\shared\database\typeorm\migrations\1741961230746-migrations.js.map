{"version": 3, "file": "1741961230746-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1741961230746-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACI,SAAI,GAAG,yBAAyB,CAAA;IAkCpC,CAAC;IAhCU,KAAK,CAAC,EAAE,CAAC,WAAwB;QACpC,MAAM,WAAW,CAAC,KAAK,CAAC,mQAAmQ,CAAC,CAAC;QAC7R,MAAM,WAAW,CAAC,KAAK,CAAC,kWAAkW,CAAC,CAAC;QAC5X,MAAM,WAAW,CAAC,KAAK,CAAC,mTAAmT,CAAC,CAAC;QAC7U,MAAM,WAAW,CAAC,KAAK,CAAC,ojBAAojB,CAAC,CAAC;QAC9kB,MAAM,WAAW,CAAC,KAAK,CAAC,keAAke,CAAC,CAAC;QAC5f,MAAM,WAAW,CAAC,KAAK,CAAC,iOAAiO,CAAC,CAAC;QAC3P,MAAM,WAAW,CAAC,KAAK,CAAC,0MAA0M,CAAC,CAAC;QACpO,MAAM,WAAW,CAAC,KAAK,CAAC,wLAAwL,CAAC,CAAC;QAClN,MAAM,WAAW,CAAC,KAAK,CAAC,6MAA6M,CAAC,CAAC;QACvO,MAAM,WAAW,CAAC,KAAK,CAAC,kMAAkM,CAAC,CAAC;QAC5N,MAAM,WAAW,CAAC,KAAK,CAAC,uKAAuK,CAAC,CAAC;QACjM,MAAM,WAAW,CAAC,KAAK,CAAC,sMAAsM,CAAC,CAAC;QAChO,MAAM,WAAW,CAAC,KAAK,CAAC,yLAAyL,CAAC,CAAC;IACvN,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CAAC,uEAAuE,CAAC,CAAC;QACjG,MAAM,WAAW,CAAC,KAAK,CAAC,+EAA+E,CAAC,CAAC;QACzG,MAAM,WAAW,CAAC,KAAK,CAAC,+EAA+E,CAAC,CAAC;QACzG,MAAM,WAAW,CAAC,KAAK,CAAC,qFAAqF,CAAC,CAAC;QAC/G,MAAM,WAAW,CAAC,KAAK,CAAC,qFAAqF,CAAC,CAAC;QAC/G,MAAM,WAAW,CAAC,KAAK,CAAC,yFAAyF,CAAC,CAAC;QACnH,MAAM,WAAW,CAAC,KAAK,CAAC,kGAAkG,CAAC,CAAC;QAC5H,MAAM,WAAW,CAAC,KAAK,CAAC,kGAAkG,CAAC,CAAC;QAC5H,MAAM,WAAW,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;QAC/C,MAAM,WAAW,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACvD,MAAM,WAAW,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAC;QAC7D,MAAM,WAAW,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC;QACjE,MAAM,WAAW,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC;IAC9E,CAAC;CAEJ;AAnCD,0DAmCC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from \"typeorm\";\r\n\r\nexport class Migrations1741961230746 implements MigrationInterface {\r\n    name = 'Migrations1741961230746'\r\n\r\n    public async up(queryRunner: QueryRunner): Promise<void> {\r\n        await queryRunner.query(`CREATE TABLE \"income_payment_scheduled_addendum\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"proportional_days\" integer NOT NULL, \"income_payment_scheduled_id\" uuid, \"addendum_id\" integer, CONSTRAINT \"PK_f1b9d433fc301d7d5672a6fc18f\" PRIMARY KEY (\"id\"))`);\r\n        await queryRunner.query(`CREATE TABLE \"income_payment_scheduled\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"scheduled_date\" date NOT NULL, \"amount\" numeric(12,2) NOT NULL, \"createdAt\" TIMESTAMP NOT NULL DEFAULT now(), \"updatedAt\" TIMESTAMP NOT NULL DEFAULT now(), \"deleted_at\" TIMESTAMP, \"contract_id\" uuid, CONSTRAINT \"PK_0af6ec8d4cb3402f0b044e887cb\" PRIMARY KEY (\"id\"))`);\r\n        await queryRunner.query(`CREATE TABLE \"payment_distribution\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"planned_amount\" numeric(12,2) NOT NULL, \"created_at\" TIMESTAMP NOT NULL DEFAULT now(), \"scheduled_payment_id\" uuid NOT NULL, \"participant_id\" uuid NOT NULL, CONSTRAINT \"PK_d9a717d5c89f5f8cda298ffc6cf\" PRIMARY KEY (\"id\"))`);\r\n        await queryRunner.query(`CREATE TABLE \"income_payment\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"payment_distribution_id\" uuid NOT NULL, \"paid_at\" date NOT NULL, \"paid_amount\" numeric(12,2) NOT NULL, \"payment_method\" character varying, \"transaction_reference\" character varying, \"info\" character varying, \"createdAt\" TIMESTAMP NOT NULL DEFAULT now(), \"updatedAt\" TIMESTAMP NOT NULL DEFAULT now(), \"deleted_at\" TIMESTAMP, \"file_id\" uuid, CONSTRAINT \"REL_69fd3a33405e78e32acfb3c4d0\" UNIQUE (\"payment_distribution_id\"), CONSTRAINT \"PK_64e0650628dd1a2ae42ca28b0f7\" PRIMARY KEY (\"id\"))`);\r\n        await queryRunner.query(`CREATE TABLE \"income\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"income_day\" numeric, \"payment_date\" TIMESTAMP, \"amount\" integer, \"percentage\" numeric, \"number_contracts\" integer, \"number_clients\" integer, \"contract_type\" character varying, \"role\" character varying, \"createdAt\" TIMESTAMP NOT NULL DEFAULT now(), \"updatedAt\" TIMESTAMP NOT NULL DEFAULT now(), \"deleted_at\" TIMESTAMP, \"owner_role_relation\" uuid, CONSTRAINT \"PK_29a10f17b97568f70cee8586d58\" PRIMARY KEY (\"id\"))`);\r\n        await queryRunner.query(`ALTER TABLE \"income_payment_scheduled_addendum\" ADD CONSTRAINT \"FK_9a8c92a6289fd28b96c5ca54163\" FOREIGN KEY (\"income_payment_scheduled_id\") REFERENCES \"income_payment_scheduled\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`);\r\n        await queryRunner.query(`ALTER TABLE \"income_payment_scheduled_addendum\" ADD CONSTRAINT \"FK_8fc4b7fc74077dd64629a3524b7\" FOREIGN KEY (\"addendum_id\") REFERENCES \"addendum\"(\"id_addendum\") ON DELETE NO ACTION ON UPDATE NO ACTION`);\r\n        await queryRunner.query(`ALTER TABLE \"income_payment_scheduled\" ADD CONSTRAINT \"FK_7f6668dd01215fa2445bf8f240a\" FOREIGN KEY (\"contract_id\") REFERENCES \"contract\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`);\r\n        await queryRunner.query(`ALTER TABLE \"payment_distribution\" ADD CONSTRAINT \"FK_678ded15de538df35bff86369dd\" FOREIGN KEY (\"scheduled_payment_id\") REFERENCES \"income_payment_scheduled\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`);\r\n        await queryRunner.query(`ALTER TABLE \"payment_distribution\" ADD CONSTRAINT \"FK_2f69b8fe5016246b895ac497ea3\" FOREIGN KEY (\"participant_id\") REFERENCES \"owner_role_relation\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`);\r\n        await queryRunner.query(`ALTER TABLE \"income_payment\" ADD CONSTRAINT \"FK_57052a1821ced5041c5b53bb578\" FOREIGN KEY (\"file_id\") REFERENCES \"files\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`);\r\n        await queryRunner.query(`ALTER TABLE \"income_payment\" ADD CONSTRAINT \"FK_69fd3a33405e78e32acfb3c4d0e\" FOREIGN KEY (\"payment_distribution_id\") REFERENCES \"payment_distribution\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`);\r\n        await queryRunner.query(`ALTER TABLE \"income\" ADD CONSTRAINT \"FK_2542605a11a32fcc24bd4c14a58\" FOREIGN KEY (\"owner_role_relation\") REFERENCES \"owner_role_relation\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`);\r\n    }\r\n\r\n    public async down(queryRunner: QueryRunner): Promise<void> {\r\n        await queryRunner.query(`ALTER TABLE \"income\" DROP CONSTRAINT \"FK_2542605a11a32fcc24bd4c14a58\"`);\r\n        await queryRunner.query(`ALTER TABLE \"income_payment\" DROP CONSTRAINT \"FK_69fd3a33405e78e32acfb3c4d0e\"`);\r\n        await queryRunner.query(`ALTER TABLE \"income_payment\" DROP CONSTRAINT \"FK_57052a1821ced5041c5b53bb578\"`);\r\n        await queryRunner.query(`ALTER TABLE \"payment_distribution\" DROP CONSTRAINT \"FK_2f69b8fe5016246b895ac497ea3\"`);\r\n        await queryRunner.query(`ALTER TABLE \"payment_distribution\" DROP CONSTRAINT \"FK_678ded15de538df35bff86369dd\"`);\r\n        await queryRunner.query(`ALTER TABLE \"income_payment_scheduled\" DROP CONSTRAINT \"FK_7f6668dd01215fa2445bf8f240a\"`);\r\n        await queryRunner.query(`ALTER TABLE \"income_payment_scheduled_addendum\" DROP CONSTRAINT \"FK_8fc4b7fc74077dd64629a3524b7\"`);\r\n        await queryRunner.query(`ALTER TABLE \"income_payment_scheduled_addendum\" DROP CONSTRAINT \"FK_9a8c92a6289fd28b96c5ca54163\"`);\r\n        await queryRunner.query(`DROP TABLE \"income\"`);\r\n        await queryRunner.query(`DROP TABLE \"income_payment\"`);\r\n        await queryRunner.query(`DROP TABLE \"payment_distribution\"`);\r\n        await queryRunner.query(`DROP TABLE \"income_payment_scheduled\"`);\r\n        await queryRunner.query(`DROP TABLE \"income_payment_scheduled_addendum\"`);\r\n    }\r\n\r\n}\r\n"]}