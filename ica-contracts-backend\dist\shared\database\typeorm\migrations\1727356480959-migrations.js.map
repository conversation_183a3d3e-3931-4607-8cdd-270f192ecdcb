{"version": 3, "file": "1727356480959-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1727356480959-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAanC,CAAC;IAXQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,mEAAmE,CACpE,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,2DAA2D,CAC5D,CAAC;IACJ,CAAC;CACF;AAdD,0DAcC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1727356480959 implements MigrationInterface {\r\n  name = 'Migrations1727356480959';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract\" ALTER COLUMN \"status\" SET DEFAULT 'aberto'`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract\" ALTER COLUMN \"status\" DROP DEFAULT`,\r\n    );\r\n  }\r\n}\r\n"]}