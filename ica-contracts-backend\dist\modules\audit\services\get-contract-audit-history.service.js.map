{"version": 3, "file": "get-contract-audit-history.service.js", "sourceRoot": "/", "sources": ["modules/audit/services/get-contract-audit-history.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAIwB;AACxB,6CAAmD;AACnD,2GAAiG;AACjG,+FAAsF;AACtF,qHAA0G;AAC1G,qCAAqC;AAG9B,IAAM,8BAA8B,GAApC,MAAM,8BAA8B;IACzC,YAEU,uBAAwD,EAGxD,kBAA8C,EAG9C,2BAAgE;QANhE,4BAAuB,GAAvB,uBAAuB,CAAiC;QAGxD,uBAAkB,GAAlB,kBAAkB,CAA4B;QAG9C,gCAA2B,GAA3B,2BAA2B,CAAqC;IACvE,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,UAAkB,EAAE,MAAc;QAC9C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;YACrD,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE;YACzB,SAAS,EAAE;gBACT,iBAAiB,EAAE,IAAI;gBACvB,QAAQ,EAAE,IAAI;aACf;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,0BAAiB,CAAC,yBAAyB,CAAC,CAAC;QACzD,CAAC;QAED,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC;YAC1D,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC;YACpD,SAAS,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;SAC1B,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,8BAAqB,CAAC,wBAAwB,CAAC,CAAC;QAC5D,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC;YACrD,KAAK,EAAE,EAAE,UAAU,EAAE;YACrB,SAAS,EAAE,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE;YACvC,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;SAC7B,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;YAC5B,EAAE,EAAE,KAAK,CAAC,EAAE;YACZ,QAAQ,EAAE,KAAK,CAAC,QAAQ;YACxB,QAAQ,EAAE,KAAK,CAAC,QAAQ;YACxB,gBAAgB,EAAE,KAAK,CAAC,gBAAgB;YACxC,OAAO,EAAE;gBACP,EAAE,EAAE,KAAK,CAAC,OAAO,CAAC,EAAE;gBACpB,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI;aAChC;YACD,SAAS,EAAE,KAAK,CAAC,SAAS;SAC3B,CAAC,CAAC,CAAC;IACN,CAAC;CACF,CAAA;AApDY,wEAA8B;yCAA9B,8BAA8B;IAD1C,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,2CAAmB,CAAC,CAAA;IAGrC,WAAA,IAAA,0BAAgB,EAAC,gCAAc,CAAC,CAAA;IAGhC,WAAA,IAAA,0BAAgB,EAAC,oDAAuB,CAAC,CAAA;qCALT,oBAAU;QAGf,oBAAU;QAGD,oBAAU;GATtC,8BAA8B,CAoD1C", "sourcesContent": ["import {\r\n  Injectable,\r\n  NotFoundException,\r\n  UnauthorizedException,\r\n} from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { ContractAuditEntity } from 'src/shared/database/typeorm/entities/contract-audit.entity';\r\nimport { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';\r\nimport { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';\r\nimport { Repository } from 'typeorm';\r\n\r\n@Injectable()\r\nexport class GetContractAuditHistoryService {\r\n  constructor(\r\n    @InjectRepository(ContractAuditEntity)\r\n    private contractAuditRepository: Repository<ContractAuditEntity>,\r\n\r\n    @InjectRepository(ContractEntity)\r\n    private contractRepository: Repository<ContractEntity>,\r\n\r\n    @InjectRepository(OwnerRoleRelationEntity)\r\n    private ownerRoleRelationRepository: Repository<OwnerRoleRelationEntity>\r\n  ) {}\r\n\r\n  async execute(contractId: string, userId: string) {\r\n    const contract = await this.contractRepository.findOne({\r\n      where: { id: contractId },\r\n      relations: {\r\n        ownerRoleRelation: true,\r\n        investor: true,\r\n      },\r\n    });\r\n\r\n    if (!contract) {\r\n      throw new NotFoundException('Contrato não encontrado');\r\n    }\r\n\r\n    const user = await this.ownerRoleRelationRepository.findOne({\r\n      where: [{ ownerId: userId }, { businessId: userId }],\r\n      relations: { role: true },\r\n    });\r\n\r\n    if (!user) {\r\n      throw new UnauthorizedException('Usuário não encontrado');\r\n    }\r\n\r\n    const audits = await this.contractAuditRepository.find({\r\n      where: { contractId },\r\n      relations: { auditor: { owner: true } },\r\n      order: { createdAt: 'DESC' },\r\n    });\r\n\r\n    return audits.map((audit) => ({\r\n      id: audit.id,\r\n      decision: audit.decision,\r\n      comments: audit.comments,\r\n      rejectionReasons: audit.rejectionReasons,\r\n      auditor: {\r\n        id: audit.auditor.id,\r\n        name: audit.auditor.owner?.name,\r\n      },\r\n      createdAt: audit.createdAt,\r\n    }));\r\n  }\r\n}\r\n"]}