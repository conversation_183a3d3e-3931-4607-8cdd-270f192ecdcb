{"version": 3, "file": "1744728699324-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1744728699324-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACI,SAAI,GAAG,yBAAyB,CAAA;IAgBpC,CAAC;IAdU,KAAK,CAAC,EAAE,CAAC,WAAwB;QACpC,MAAM,WAAW,CAAC,KAAK,CAAC,qFAAqF,CAAC,CAAC;QAC/G,MAAM,WAAW,CAAC,KAAK,CAAC,+YAA+Y,CAAC,CAAC;QACza,MAAM,WAAW,CAAC,KAAK,CAAC,8KAA8K,CAAC,CAAC;QACxM,MAAM,WAAW,CAAC,KAAK,CAAC,wLAAwL,CAAC,CAAC;IACtN,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CAAC,+EAA+E,CAAC,CAAC;QACzG,MAAM,WAAW,CAAC,KAAK,CAAC,+EAA+E,CAAC,CAAC;QACzG,MAAM,WAAW,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACvD,MAAM,WAAW,CAAC,KAAK,CAAC,mDAAmD,CAAC,CAAC;IACjF,CAAC;CAEJ;AAjBD,0DAiBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from \"typeorm\";\r\n\r\nexport class Migrations1744728699324 implements MigrationInterface {\r\n    name = 'Migrations1744728699324'\r\n\r\n    public async up(queryRunner: QueryRunner): Promise<void> {\r\n        await queryRunner.query(`CREATE TYPE \"public\".\"contract_audit_decision_enum\" AS ENUM('APPROVED', 'REJECTED')`);\r\n        await queryRunner.query(`CREATE TABLE \"contract_audit\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"contract_id\" uuid NOT NULL, \"auditor_id\" uuid NOT NULL, \"decision\" \"public\".\"contract_audit_decision_enum\" NOT NULL, \"comments\" text, \"rejection_reasons\" jsonb, \"created_at\" TIMESTAMP NOT NULL DEFAULT now(), \"updated_at\" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT \"PK_9d297ce3bd10f69e3da1eb0f67f\" PRIMARY KEY (\"id\"))`);\r\n        await queryRunner.query(`ALTER TABLE \"contract_audit\" ADD CONSTRAINT \"FK_896afab3853dbb209938a2cd0a5\" FOREIGN KEY (\"contract_id\") REFERENCES \"contract\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`);\r\n        await queryRunner.query(`ALTER TABLE \"contract_audit\" ADD CONSTRAINT \"FK_a413e0b00469e3ac43ef85f6642\" FOREIGN KEY (\"auditor_id\") REFERENCES \"owner_role_relation\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`);\r\n    }\r\n\r\n    public async down(queryRunner: QueryRunner): Promise<void> {\r\n        await queryRunner.query(`ALTER TABLE \"contract_audit\" DROP CONSTRAINT \"FK_a413e0b00469e3ac43ef85f6642\"`);\r\n        await queryRunner.query(`ALTER TABLE \"contract_audit\" DROP CONSTRAINT \"FK_896afab3853dbb209938a2cd0a5\"`);\r\n        await queryRunner.query(`DROP TABLE \"contract_audit\"`);\r\n        await queryRunner.query(`DROP TYPE \"public\".\"contract_audit_decision_enum\"`);\r\n    }\r\n\r\n}\r\n"]}