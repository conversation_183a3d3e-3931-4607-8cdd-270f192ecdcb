{"version": 3, "file": "update-celcoin-webhook.service.js", "sourceRoot": "/", "sources": ["modules/webhook/services/update-celcoin-webhook.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,kGAAwF;AAKjF,IAAM,2BAA2B,GAAjC,MAAM,2BAA2B;IACtC,YAA6B,qBAA2C;QAA3C,0BAAqB,GAArB,qBAAqB,CAAsB;IAAG,CAAC;IAE5E,KAAK,CAAC,OAAO,CAAC,KAA8B;QAC1C,OAAO,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IACjE,CAAC;CACF,CAAA;AANY,kEAA2B;sCAA3B,2BAA2B;IADvC,IAAA,mBAAU,GAAE;qCAEyC,6CAAoB;GAD7D,2BAA2B,CAMvC", "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { UpdateWebhookService } from 'src/apis/celcoin/services/update-webhook.service';\r\n\r\nimport { UpdateCelcoinWebhookDto } from '../dto/update-celcoin-webhooks.dto';\r\n\r\n@Injectable()\r\nexport class UpdateCelcoinWebhookService {\r\n  constructor(private readonly updateWebhooksService: UpdateWebhookService) {}\r\n\r\n  async perform(input: UpdateCelcoinWebhookDto) {\r\n    return this.updateWebhooksService.perform(input, input.entity);\r\n  }\r\n}\r\n"]}