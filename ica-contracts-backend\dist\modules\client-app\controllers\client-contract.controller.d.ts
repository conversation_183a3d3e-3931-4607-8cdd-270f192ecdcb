import { IClientContractResponse, IClientContractAddendumResponse } from '../responses/client-contract.response';
import { ClientContractService } from '../services/client-contract.service';
export declare class ClientContractController {
    private readonly clientContractService;
    constructor(clientContractService: ClientContractService);
    getContracts(req: {
        user: {
            id: string;
        };
    }): Promise<IClientContractResponse[]>;
    getContractAddendums(contractId: string): Promise<IClientContractAddendumResponse[]>;
}
