{"version": 3, "file": "approve-contract.dto.js", "sourceRoot": "/", "sources": ["modules/contract/dto/approve-contract.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAAuD;AAEvD,MAAa,kBAAkB;CAI9B;AAJD,gDAIC;AADC;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;mDACK", "sourcesContent": ["import { IsNotEmpty, IsString } from 'class-validator';\r\n\r\nexport class ApproveContractDto {\r\n  @IsNotEmpty()\r\n  @IsString()\r\n  adminId: string;\r\n}\r\n"]}