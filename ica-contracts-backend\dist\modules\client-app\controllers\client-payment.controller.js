"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClientPaymentController = void 0;
const common_1 = require("@nestjs/common");
const jwt_auth_guard_1 = require("../../../shared/guards/jwt-auth.guard");
const client_payment_dto_1 = require("../dto/client-payment.dto");
const client_payment_service_1 = require("../services/client-payment.service");
let ClientPaymentController = class ClientPaymentController {
    constructor(clientPaymentService) {
        this.clientPaymentService = clientPaymentService;
    }
    async getPayments(req, query) {
        const userId = req.user.id;
        return this.clientPaymentService.getPaymentsByUserId(userId, query);
    }
    async getPaymentById(paymentId) {
        try {
            return await this.clientPaymentService.getPaymentById(paymentId);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.NotFoundException('Pagamento não encontrado');
        }
    }
};
exports.ClientPaymentController = ClientPaymentController;
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, client_payment_dto_1.ClientPaymentsQueryDto]),
    __metadata("design:returntype", Promise)
], ClientPaymentController.prototype, "getPayments", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ClientPaymentController.prototype, "getPaymentById", null);
exports.ClientPaymentController = ClientPaymentController = __decorate([
    (0, common_1.Controller)('client/payments'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [client_payment_service_1.ClientPaymentService])
], ClientPaymentController);
//# sourceMappingURL=client-payment.controller.js.map