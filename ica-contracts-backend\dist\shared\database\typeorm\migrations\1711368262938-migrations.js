"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations************* = void 0;
class Migrations************* {
    constructor() {
        this.name = 'Migrations*************';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "account_transfer-limits" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "daily_limit" integer NOT NULL DEFAULT '1000', "monthly_limit" integer NOT NULL DEFAULT '1000', "daily_night_limit" integer NOT NULL DEFAULT '1000', "account_id" uuid, "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, CONSTRAINT "REL_51fc575749fb624f8debaab8c5" UNIQUE ("account_id"), CONSTRAINT "PK_f9c66b7635fc8816ab0573356bc" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" ADD CONSTRAINT "FK_51fc575749fb624f8debaab8c5c" FOREIGN KEY ("account_id") REFERENCES "account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" DROP CONSTRAINT "FK_51fc575749fb624f8debaab8c5c"`);
        await queryRunner.query(`DROP TABLE "account_transfer-limits"`);
    }
}
exports.Migrations************* = Migrations*************;
//# sourceMappingURL=*************-migrations.js.map