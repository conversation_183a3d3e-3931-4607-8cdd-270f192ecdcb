import { TransactionEntity } from 'src/shared/database/typeorm/entities/transaction.entity';
import { Repository } from 'typeorm';
import { IPixCashoutCelcoinWebhook } from '../dto/pix-cashout-celcoin-webhook.dto';
export declare class PixCashoutCelcoinWebhookService {
    private transactionDb;
    constructor(transactionDb: Repository<TransactionEntity>);
    perform(data: IPixCashoutCelcoinWebhook): Promise<void>;
}
