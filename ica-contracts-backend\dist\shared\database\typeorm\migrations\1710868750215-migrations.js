"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1710868750215 = void 0;
class Migrations1710868750215 {
    constructor() {
        this.name = 'Migrations1710868750215';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "pix-key" ADD "status" character varying NOT NULL DEFAULT 'PENDENT'`);
        await queryRunner.query(`ALTER TABLE "pix-key" ADD "claim_status" character varying`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "pix-key" DROP COLUMN "claim_status"`);
        await queryRunner.query(`ALTER TABLE "pix-key" DROP COLUMN "status"`);
    }
}
exports.Migrations1710868750215 = Migrations1710868750215;
//# sourceMappingURL=1710868750215-migrations.js.map