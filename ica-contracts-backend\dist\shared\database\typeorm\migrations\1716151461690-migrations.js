"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1716151461690 = void 0;
class Migrations1716151461690 {
    constructor() {
        this.name = 'Migrations1716151461690';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "document" ADD "type" character varying NOT NULL DEFAULT 'CNH'`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "document" DROP COLUMN "type"`);
    }
}
exports.Migrations1716151461690 = Migrations1716151461690;
//# sourceMappingURL=1716151461690-migrations.js.map