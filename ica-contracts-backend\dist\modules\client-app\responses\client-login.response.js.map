{"version": 3, "file": "client-login.response.js", "sourceRoot": "/", "sources": ["modules/client-app/responses/client-login.response.ts"], "names": [], "mappings": "", "sourcesContent": ["export interface IClientContract {\r\n  id: number | string;\r\n  type: 'starter' | 'additive';\r\n  date: string;\r\n  invested: number;\r\n  contract: string;\r\n}\r\n\r\nexport interface IClientAccount {\r\n  name: string;\r\n  yield: number;\r\n  nextPayment: string;\r\n  contractDate: string;\r\n  totalInvested: number;\r\n  paymentAmount: number;\r\n  contracts: IClientContract[];\r\n}\r\n\r\nexport interface IClientLoginResponse {\r\n  accessToken: string;\r\n  refreshToken?: string;\r\n  account: IClientAccount;\r\n}\r\n"]}