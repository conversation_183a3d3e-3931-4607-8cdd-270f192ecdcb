"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1712079838591 = void 0;
class Migrations1712079838591 {
    constructor() {
        this.name = 'Migrations1712079838591';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "billet" ADD "transaction_id" character varying`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "billet" DROP COLUMN "transaction_id"`);
    }
}
exports.Migrations1712079838591 = Migrations1712079838591;
//# sourceMappingURL=1712079838591-migrations.js.map