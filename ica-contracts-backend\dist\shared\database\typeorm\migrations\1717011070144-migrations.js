"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations************* = void 0;
class Migrations************* {
    constructor() {
        this.name = 'Migrations*************';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "recharge" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "account_id" uuid NOT NULL, "external_id" character varying NOT NULL, "value" character varying NOT NULL, "status" character varying NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_5cc2dad79b630f05df9d7c0df82" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" DROP COLUMN "general_transfer_limit"`);
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" DROP COLUMN "default_values"`);
        await queryRunner.query(`ALTER TABLE "recharge" ADD CONSTRAINT "FK_73c4afda905a8765218a1ffa1c6" FOREIGN KEY ("account_id") REFERENCES "account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "recharge" DROP CONSTRAINT "FK_73c4afda905a8765218a1ffa1c6"`);
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" ADD "default_values" character varying`);
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" ADD "general_transfer_limit" numeric DEFAULT '50000'`);
        await queryRunner.query(`DROP TABLE "recharge"`);
    }
}
exports.Migrations************* = Migrations*************;
//# sourceMappingURL=*************-migrations.js.map