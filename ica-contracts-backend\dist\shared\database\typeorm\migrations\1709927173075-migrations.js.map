{"version": 3, "file": "1709927173075-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1709927173075-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAWnC,CAAC;IATQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,scAAsc,CACvc,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;IACrD,CAAC;CACF;AAZD,0DAYC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1709927173075 implements MigrationInterface {\r\n  name = 'Migrations1709927173075';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"pix_qrcode\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"account_id\" character varying NOT NULL, \"external_id\" character varying NOT NULL, \"transaction_id\" character varying NOT NULL, \"type\" character varying NOT NULL, \"emv\" character varying NOT NULL, \"updated_at\" TIMESTAMP NOT NULL DEFAULT now(), \"created_at\" TIMESTAMP NOT NULL DEFAULT now(), \"deleted_at\" TIMESTAMP, CONSTRAINT \"PK_4088b442d7790b5b98f986a7076\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(`DROP TABLE \"pix_qrcode\"`);\r\n  }\r\n}\r\n"]}