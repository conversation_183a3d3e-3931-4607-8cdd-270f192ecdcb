"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1710517166132 = void 0;
class Migrations1710517166132 {
    constructor() {
        this.name = 'Migrations1710517166132';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "owner_role_relation" DROP CONSTRAINT "FK_622c8e2dabdd04a18f9bc656570"`);
        await queryRunner.query(`ALTER TABLE "owner_role_relation" ALTER COLUMN "owner_id" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "owner" ALTER COLUMN "phone" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "owner" ALTER COLUMN "mother_name" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "owner" ALTER COLUMN "dt_birth" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "owner" ALTER COLUMN "pep" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "owner" ALTER COLUMN "password" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "owner_role_relation" ADD CONSTRAINT "FK_622c8e2dabdd04a18f9bc656570" FOREIGN KEY ("owner_id") REFERENCES "owner"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "owner_role_relation" DROP CONSTRAINT "FK_622c8e2dabdd04a18f9bc656570"`);
        await queryRunner.query(`ALTER TABLE "owner" ALTER COLUMN "password" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "owner" ALTER COLUMN "pep" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "owner" ALTER COLUMN "dt_birth" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "owner" ALTER COLUMN "mother_name" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "owner" ALTER COLUMN "phone" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "owner_role_relation" ALTER COLUMN "owner_id" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "owner_role_relation" ADD CONSTRAINT "FK_622c8e2dabdd04a18f9bc656570" FOREIGN KEY ("owner_id") REFERENCES "owner"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
}
exports.Migrations1710517166132 = Migrations1710517166132;
//# sourceMappingURL=1710517166132-migrations.js.map