{"version": 3, "file": "1712240144479-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1712240144479-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IA6BnC,CAAC;IA3BQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,mDAAmD,CACpD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,yDAAyD,CAC1D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,+DAA+D,CAChE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,qFAAqF,CACtF,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC;QAC1E,MAAM,WAAW,CAAC,KAAK,CACrB,qDAAqD,CACtD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,uDAAuD,CACxD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,6DAA6D,CAC9D,CAAC;IACJ,CAAC;CACF;AA9BD,0DA8BC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1712240144479 implements MigrationInterface {\r\n  name = 'Migrations1712240144479';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"billet\" DROP COLUMN \"transaction_id\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"transaction\" ADD \"transfer_date\" TIMESTAMP`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"transaction\" ADD \"destiny_key\" character varying`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"transaction\" ADD \"status\" character varying NOT NULL DEFAULT 'PENDENT'`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(`ALTER TABLE \"transaction\" DROP COLUMN \"status\"`);\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"transaction\" DROP COLUMN \"destiny_key\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"transaction\" DROP COLUMN \"transfer_date\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"billet\" ADD \"transaction_id\" character varying`,\r\n    );\r\n  }\r\n}\r\n"]}