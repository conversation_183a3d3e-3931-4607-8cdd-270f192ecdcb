"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.dbDataSource = void 0;
const dotenv_1 = require("dotenv");
const typeorm_1 = require("typeorm");
(0, dotenv_1.config)({ path: '.env' });
exports.dbDataSource = {
    type: 'postgres',
    host: process.env.DB_HOST,
    port: Number(process.env.DB_PORT),
    username: process.env.DB_USER,
    password: process.env.DB_PASS,
    database: process.env.DB_NAME,
    synchronize: false,
    migrations: [`${__dirname}/migrations/*.ts`],
    entities: [`${__dirname}/entities/*.ts`],
    ssl: {
        rejectUnauthorized: false,
    },
};
const dataSource = new typeorm_1.DataSource(exports.dbDataSource);
exports.default = dataSource;
//# sourceMappingURL=data-source.js.map