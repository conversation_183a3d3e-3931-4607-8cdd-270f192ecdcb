"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1728335050216 = void 0;
class Migrations1728335050216 {
    constructor() {
        this.name = 'Migrations1728335050216';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "contract_event" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "eventType" character varying(50) NOT NULL, "eventDate" date NOT NULL, "status" character varying(50), "comment" text, "destinationArea" character varying(50), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "contract_id" uuid, "responsible_id" uuid, CONSTRAINT "PK_a0a0fdb2918e838e546c3b5fd01" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "contract_event" ADD CONSTRAINT "FK_654c5b3eae51e91ad465ad7daa0" FOREIGN KEY ("contract_id") REFERENCES "contract"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract_event" ADD CONSTRAINT "FK_196c2c26c5048d8f6760bbc281e" FOREIGN KEY ("responsible_id") REFERENCES "owner_role_relation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "contract_event" DROP CONSTRAINT "FK_196c2c26c5048d8f6760bbc281e"`);
        await queryRunner.query(`ALTER TABLE "contract_event" DROP CONSTRAINT "FK_654c5b3eae51e91ad465ad7daa0"`);
        await queryRunner.query(`DROP TABLE "contract_event"`);
    }
}
exports.Migrations1728335050216 = Migrations1728335050216;
//# sourceMappingURL=1728335050216-migrations.js.map