{"version": 3, "file": "1740599088107-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1740599088107-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACI,SAAI,GAAG,yBAAyB,CAAA;IAYpC,CAAC;IAVU,KAAK,CAAC,EAAE,CAAC,WAAwB;QACpC,MAAM,WAAW,CAAC,KAAK,CAAC,sDAAsD,CAAC,CAAC;QAChF,MAAM,WAAW,CAAC,KAAK,CAAC,qLAAqL,CAAC,CAAC;IACnN,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CAAC,6EAA6E,CAAC,CAAC;QACvG,MAAM,WAAW,CAAC,KAAK,CAAC,sDAAsD,CAAC,CAAC;IACpF,CAAC;CAEJ;AAbD,0DAaC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from \"typeorm\";\r\n\r\nexport class Migrations1740599088107 implements MigrationInterface {\r\n    name = 'Migrations1740599088107'\r\n\r\n    public async up(queryRunner: QueryRunner): Promise<void> {\r\n        await queryRunner.query(`ALTER TABLE \"notification\" ADD \"addendum_id\" integer`);\r\n        await queryRunner.query(`ALTER TABLE \"notification\" ADD CONSTRAINT \"FK_3eeec94e4bf39770577b3b20ca8\" FOREIGN KEY (\"addendum_id\") REFERENCES \"addendum\"(\"id_addendum\") ON DELETE NO ACTION ON UPDATE NO ACTION`);\r\n    }\r\n\r\n    public async down(queryRunner: QueryRunner): Promise<void> {\r\n        await queryRunner.query(`ALTER TABLE \"notification\" DROP CONSTRAINT \"FK_3eeec94e4bf39770577b3b20ca8\"`);\r\n        await queryRunner.query(`ALTER TABLE \"notification\" DROP COLUMN \"addendum_id\"`);\r\n    }\r\n\r\n}\r\n"]}