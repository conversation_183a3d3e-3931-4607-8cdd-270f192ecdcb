"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1710795893769 = void 0;
class Migrations1710795893769 {
    constructor() {
        this.name = 'Migrations1710795893769';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "business" ADD "refresh_token" character varying`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "business" DROP COLUMN "refresh_token"`);
    }
}
exports.Migrations1710795893769 = Migrations1710795893769;
//# sourceMappingURL=1710795893769-migrations.js.map