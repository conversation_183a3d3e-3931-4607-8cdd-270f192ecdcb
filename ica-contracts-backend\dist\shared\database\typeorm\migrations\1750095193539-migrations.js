"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1750095193539 = void 0;
class Migrations1750095193539 {
    constructor() {
        this.name = 'Migrations1750095193539';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "business" ADD "owner_id_new" uuid`);
        await queryRunner.query(`
                UPDATE "business" 
                SET "owner_id" = NULL 
                WHERE "owner_id" = ''
            `);
        await queryRunner.query(`
                UPDATE "business" 
                SET "owner_id_new" = "owner_id"::uuid 
                WHERE "owner_id" IS NOT NULL
            `);
        await queryRunner.query(`ALTER TABLE "business" DROP COLUMN IF EXISTS "owner_id"`);
        await queryRunner.query(`ALTER TABLE "business" RENAME COLUMN "owner_id_new" TO "owner_id"`);
        await queryRunner.query(`
                ALTER TABLE "business" 
                ADD CONSTRAINT "FK_da374bf24a4b84a4aa36dfd3e76" 
                FOREIGN KEY ("owner_id") 
                REFERENCES "owner"("id") 
                ON DELETE NO ACTION 
                ON UPDATE NO ACTION
            `);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "business" DROP CONSTRAINT "FK_da374bf24a4b84a4aa36dfd3e76"`);
        await queryRunner.query(`ALTER TABLE "business" ADD "owner_id_old" character varying`);
        await queryRunner.query(`
                UPDATE "business" 
                SET "owner_id_old" = "owner_id"::text 
                WHERE "owner_id" IS NOT NULL
            `);
        await queryRunner.query(`ALTER TABLE "business" DROP COLUMN "owner_id"`);
        await queryRunner.query(`ALTER TABLE "business" RENAME COLUMN "owner_id_old" TO "owner_id"`);
    }
}
exports.Migrations1750095193539 = Migrations1750095193539;
//# sourceMappingURL=1750095193539-migrations.js.map