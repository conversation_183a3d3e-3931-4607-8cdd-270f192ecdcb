{"version": 3, "file": "check-investor.controller.js", "sourceRoot": "/", "sources": ["modules/client-app/controllers/check-investor.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAMwB;AAExB,kEAA6D;AAE7D,+EAA0E;AAGnE,IAAM,uBAAuB,GAA7B,MAAM,uBAAuB;IAClC,YAA6B,oBAA0C;QAA1C,yBAAoB,GAApB,oBAAoB,CAAsB;IAAG,CAAC;IAGrE,AAAN,KAAK,CAAC,aAAa,CACT,IAAsB;QAE9B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC7D,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB;gBACE,MAAM,EAAE,mBAAU,CAAC,WAAW;aAC/B,EACD,mBAAU,CAAC,WAAW,EACtB;gBACE,KAAK,EAAE,KAAK;aACb,CACF,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AAtBY,0DAAuB;AAI5B;IADL,IAAA,aAAI,GAAE;IAEJ,WAAA,IAAA,aAAI,GAAE,CAAA;;qCAAO,qCAAgB;;4DAgB/B;kCArBU,uBAAuB;IADnC,IAAA,mBAAU,EAAC,uBAAuB,CAAC;qCAEiB,6CAAoB;GAD5D,uBAAuB,CAsBnC", "sourcesContent": ["import {\r\n  Body,\r\n  Controller,\r\n  Post,\r\n  HttpException,\r\n  HttpStatus,\r\n} from '@nestjs/common';\r\n\r\nimport { CheckInvestorDto } from '../dto/check-investor.dto';\r\nimport { ICheckInvestorResponse } from '../responses/check-investor.response';\r\nimport { CheckInvestorService } from '../services/check-investor.service';\r\n\r\n@Controller('client/check-investor')\r\nexport class CheckInvestorController {\r\n  constructor(private readonly checkInvestorService: CheckInvestorService) {}\r\n\r\n  @Post()\r\n  async checkInvestor(\r\n    @Body() body: CheckInvestorDto,\r\n  ): Promise<ICheckInvestorResponse> {\r\n    try {\r\n      const result = await this.checkInvestorService.perform(body);\r\n      return result;\r\n    } catch (error) {\r\n      throw new HttpException(\r\n        {\r\n          status: HttpStatus.BAD_REQUEST,\r\n        },\r\n        HttpStatus.BAD_REQUEST,\r\n        {\r\n          cause: error,\r\n        },\r\n      );\r\n    }\r\n  }\r\n}\r\n"]}