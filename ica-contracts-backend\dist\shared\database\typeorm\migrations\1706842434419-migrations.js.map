{"version": 3, "file": "1706842434419-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1706842434419-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAuBnC,CAAC;IArBQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,oEAAoE,CACrE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,2DAA2D,CAC5D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,2DAA2D,CAC5D,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,0DAA0D,CAC3D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,0DAA0D,CAC3D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAC;IAC7E,CAAC;CACF;AAxBD,0DAwBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1706842434419 implements MigrationInterface {\r\n  name = 'Migrations1706842434419';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account\" ADD \"external_id\" character varying NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account\" ALTER COLUMN \"number\" DROP NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account\" ALTER COLUMN \"branch\" DROP NOT NULL`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account\" ALTER COLUMN \"branch\" SET NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account\" ALTER COLUMN \"number\" SET NOT NULL`,\r\n    );\r\n    await queryRunner.query(`ALTER TABLE \"account\" DROP COLUMN \"external_id\"`);\r\n  }\r\n}\r\n"]}