{"version": 3, "file": "update-contract-lifecycle-monitoring.dto.js", "sourceRoot": "/", "sources": ["modules/contract-lifecycle-monitoring/dto/update-contract-lifecycle-monitoring.dto.ts"], "names": [], "mappings": ";;;AAAA,uDAAmD;AAEnD,yGAAkG;AAElG,MAAa,oCAAqC,SAAQ,IAAA,0BAAW,EACnE,+EAAoC,CACrC;CAAG;AAFJ,oFAEI", "sourcesContent": ["import { PartialType } from '@nestjs/mapped-types';\r\n\r\nimport { CreateContractLifecycleMonitoringDto } from './create-contract-lifecycle-monitoring.dto';\r\n\r\nexport class UpdateContractLifecycleMonitoringDto extends PartialType(\r\n  CreateContractLifecycleMonitoringDto,\r\n) {}\r\n"]}