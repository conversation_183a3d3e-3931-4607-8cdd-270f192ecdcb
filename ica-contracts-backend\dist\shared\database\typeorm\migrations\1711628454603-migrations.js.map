{"version": 3, "file": "1711628454603-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1711628454603-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAiBnC,CAAC;IAfQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,2OAA2O,CAC5O,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,oKAAoK,CACrK,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,uEAAuE,CACxE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;IACjD,CAAC;CACF;AAlBD,0DAkBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1711628454603 implements MigrationInterface {\r\n  name = 'Migrations1711628454603';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"billet\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"account_id\" uuid NOT NULL, \"amount\" integer NOT NULL, \"due_date\" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT \"PK_9b069b774f7172ce2a6700ef4bb\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"billet\" ADD CONSTRAINT \"FK_e67d9237513dfc3b7a02dd7ddb3\" FOREIGN KEY (\"account_id\") REFERENCES \"account\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"billet\" DROP CONSTRAINT \"FK_e67d9237513dfc3b7a02dd7ddb3\"`,\r\n    );\r\n    await queryRunner.query(`DROP TABLE \"billet\"`);\r\n  }\r\n}\r\n"]}