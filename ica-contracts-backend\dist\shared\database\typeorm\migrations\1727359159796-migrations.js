"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1727359159796 = void 0;
class Migrations1727359159796 {
    constructor() {
        this.name = 'Migrations1727359159796';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "pre_register" ALTER COLUMN "rg" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "pre_register" ALTER COLUMN "investment_term" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "pre_register" ALTER COLUMN "investment_modality" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "pre_register" ALTER COLUMN "observations" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "pre_register" ALTER COLUMN "purchase_with" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "pre_register" ALTER COLUMN "amount_quotes" DROP NOT NULL`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "pre_register" ALTER COLUMN "amount_quotes" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "pre_register" ALTER COLUMN "purchase_with" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "pre_register" ALTER COLUMN "observations" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "pre_register" ALTER COLUMN "investment_modality" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "pre_register" ALTER COLUMN "investment_term" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "pre_register" ALTER COLUMN "rg" SET NOT NULL`);
    }
}
exports.Migrations1727359159796 = Migrations1727359159796;
//# sourceMappingURL=1727359159796-migrations.js.map