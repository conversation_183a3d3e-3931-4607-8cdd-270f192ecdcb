import React from 'react';
import { useForceSignatureCheck } from '@/hooks/useForceSignatureCheck';

interface ForceSignatureCheckButtonProps {
  contractId: string;
  onSuccess?: (newStatus: string) => void;
  className?: string;
  disabled?: boolean;
}

const ForceSignatureCheckButton: React.FC<ForceSignatureCheckButtonProps> = ({
  contractId,
  onSuccess,
  className = '',
  disabled = false,
}) => {
  const { isLoading, checkSignatureStatus } = useForceSignatureCheck();

  const handleClick = async () => {
    const response = await checkSignatureStatus(contractId);
    
    if (response && onSuccess) {
      onSuccess(response.status);
    }
  };

  return (
    <button
      onClick={handleClick}
      disabled={disabled || isLoading}
      className={`
        px-3 py-1 text-sm font-medium rounded-md
        bg-blue-600 text-white hover:bg-blue-700
        disabled:bg-gray-400 disabled:cursor-not-allowed
        transition-colors duration-200
        ${className}
      `}
    >
      {isLoading ? (
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          <span>Verificando...</span>
        </div>
      ) : (
        'Verificar Status'
      )}
    </button>
  );
};

export default ForceSignatureCheckButton;
