"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1734085737849 = void 0;
class Migrations1734085737849 {
    constructor() {
        this.name = 'Migrations1734085737849';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "addendum" RENAME COLUMN "yield_rate" TO "expires_in"`);
        await queryRunner.query(`ALTER TABLE "addendum" DROP COLUMN "expires_in"`);
        await queryRunner.query(`ALTER TABLE "addendum" ADD "expires_in" date NOT NULL DEFAULT now()`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "addendum" DROP COLUMN "expires_in"`);
        await queryRunner.query(`ALTER TABLE "addendum" ADD "expires_in" numeric(5,2) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "addendum" RENAME COLUMN "expires_in" TO "yield_rate"`);
    }
}
exports.Migrations1734085737849 = Migrations1734085737849;
//# sourceMappingURL=1734085737849-migrations.js.map