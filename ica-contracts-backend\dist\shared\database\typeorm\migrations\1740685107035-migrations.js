"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1740685107035 = void 0;
class Migrations1740685107035 {
    constructor() {
        this.name = 'Migrations1740685107035';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "notification" DROP CONSTRAINT "FK_8ca93a4412001fddb88e326b91f"`);
        await queryRunner.query(`ALTER TABLE "notification" DROP COLUMN "user_owner_role_relation_id"`);
        await queryRunner.query(`ALTER TABLE "notification" ADD "investor_id" uuid`);
        await queryRunner.query(`ALTER TABLE "notification" ADD "broker_id" uuid`);
        await queryRunner.query(`ALTER TABLE "notification" ADD CONSTRAINT "FK_e3ef26f3cdca8b42a45bb8f25ab" FOREIGN KEY ("broker_id") REFERENCES "owner_role_relation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "notification" ADD CONSTRAINT "FK_91d75bba992225527b598ea2bf7" FOREIGN KEY ("investor_id") REFERENCES "owner_role_relation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "notification" DROP CONSTRAINT "FK_91d75bba992225527b598ea2bf7"`);
        await queryRunner.query(`ALTER TABLE "notification" DROP CONSTRAINT "FK_e3ef26f3cdca8b42a45bb8f25ab"`);
        await queryRunner.query(`ALTER TABLE "notification" DROP COLUMN "broker_id"`);
        await queryRunner.query(`ALTER TABLE "notification" DROP COLUMN "investor_id"`);
        await queryRunner.query(`ALTER TABLE "notification" ADD "user_owner_role_relation_id" uuid`);
        await queryRunner.query(`ALTER TABLE "notification" ADD CONSTRAINT "FK_8ca93a4412001fddb88e326b91f" FOREIGN KEY ("user_owner_role_relation_id") REFERENCES "owner_role_relation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
}
exports.Migrations1740685107035 = Migrations1740685107035;
//# sourceMappingURL=1740685107035-migrations.js.map