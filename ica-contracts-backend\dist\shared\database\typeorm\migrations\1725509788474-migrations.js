"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1725509788474 = void 0;
class Migrations1725509788474 {
    constructor() {
        this.name = 'Migrations1725509788474';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "pre-register" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying NOT NULL, "rg" character varying NOT NULL, "document" character varying NOT NULL, "phone_number" character varying NOT NULL, "dt_birth" date NOT NULL, "email" character varying NOT NULL, "zip_code" character varying NOT NULL, "neighborhood" character varying NOT NULL, "city" character varying NOT NULL, "address_complement" character varying, "address_number" character varying NOT NULL, "investment_value" numeric NOT NULL, "investment_term" character varying NOT NULL, "investment_yield" numeric NOT NULL, "investment_modality" character varying NOT NULL, "observations" character varying NOT NULL, "status" character varying NOT NULL, "adviser_id" uuid NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_e4c72f402a26abc5ad01f261090" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "wallets-views" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "upper_id" uuid NOT NULL, "bottom_id" uuid NOT NULL, CONSTRAINT "PK_e339cce0810748d2b6ffa38133d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "pre-register" ADD CONSTRAINT "FK_862fec7c8cbcef898f519c5c0ff" FOREIGN KEY ("adviser_id") REFERENCES "owner_role_relation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "wallets-views" ADD CONSTRAINT "FK_834b2d2f9cd1d6ffaf3214968c6" FOREIGN KEY ("upper_id") REFERENCES "owner_role_relation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "wallets-views" ADD CONSTRAINT "FK_7cde3a0398069bbf43680cd8d12" FOREIGN KEY ("bottom_id") REFERENCES "owner_role_relation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "wallets-views" DROP CONSTRAINT "FK_7cde3a0398069bbf43680cd8d12"`);
        await queryRunner.query(`ALTER TABLE "wallets-views" DROP CONSTRAINT "FK_834b2d2f9cd1d6ffaf3214968c6"`);
        await queryRunner.query(`ALTER TABLE "pre-register" DROP CONSTRAINT "FK_862fec7c8cbcef898f519c5c0ff"`);
        await queryRunner.query(`DROP TABLE "wallets-views"`);
        await queryRunner.query(`DROP TABLE "pre-register"`);
    }
}
exports.Migrations1725509788474 = Migrations1725509788474;
//# sourceMappingURL=1725509788474-migrations.js.map