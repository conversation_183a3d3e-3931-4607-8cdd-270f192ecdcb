"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations************* = void 0;
class Migrations************* {
    constructor() {
        this.name = 'Migrations*************';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "favorite_ted" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "document" character varying, "account_number" character varying, "account_branch" character varying, "account_bank" character varying, "name" character varying, "alias" character varying, "account_id" uuid, "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, CONSTRAINT "REL_a9320fa074e03dc370b5cd2d77" UNIQUE ("account_id"), CONSTRAINT "PK_61a15d3413bde23004a5644e57c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "favorite_ted" ADD CONSTRAINT "FK_a9320fa074e03dc370b5cd2d773" FOREIGN KEY ("account_id") REFERENCES "account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "favorite_ted" DROP CONSTRAINT "FK_a9320fa074e03dc370b5cd2d773"`);
        await queryRunner.query(`DROP TABLE "favorite_ted"`);
    }
}
exports.Migrations************* = Migrations*************;
//# sourceMappingURL=*************-migrations.js.map