"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1707160914285 = void 0;
class Migrations1707160914285 {
    constructor() {
        this.name = 'Migrations1707160914285';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "permission" DROP CONSTRAINT "FK_383892d758d08d346f837d3d8b7"`);
        await queryRunner.query(`CREATE TABLE "investor" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "broker_id" uuid, "owner_id" uuid, "advisor_id" uuid, CONSTRAINT "PK_c60a173349549955c39d3703551" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "advisor" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "broker_id" uuid, "owner_id" uuid, CONSTRAINT "PK_41bd0367a08f2d8c34560a2785e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "broker" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "admin_id" uuid, "owner_id" uuid, CONSTRAINT "PK_06617ad8cb3dc7339492a5c995d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "superadmin" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "owner_id" uuid, CONSTRAINT "PK_34da9117b572e9b32a8d829ae84" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "admin" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "superadmin_id" uuid, "owner_id" uuid, CONSTRAINT "PK_e032310bcef831fb83101899b10" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "permission" DROP COLUMN "role_id"`);
        await queryRunner.query(`ALTER TABLE "investor" ADD CONSTRAINT "FK_5aafdad076d9c769718a1a049a2" FOREIGN KEY ("broker_id") REFERENCES "broker"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "investor" ADD CONSTRAINT "FK_2f542bded5a8b34cc53d24d1c5c" FOREIGN KEY ("owner_id") REFERENCES "owner"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "investor" ADD CONSTRAINT "FK_4a98ce7d73b154d63f0aac4889e" FOREIGN KEY ("advisor_id") REFERENCES "advisor"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "advisor" ADD CONSTRAINT "FK_158b4652b9a7c747359c9fe2f09" FOREIGN KEY ("broker_id") REFERENCES "broker"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "advisor" ADD CONSTRAINT "FK_498ad66ddc714e114a26f43533d" FOREIGN KEY ("owner_id") REFERENCES "owner"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "broker" ADD CONSTRAINT "FK_7d049f6cef07839db176c50774c" FOREIGN KEY ("admin_id") REFERENCES "admin"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "broker" ADD CONSTRAINT "FK_9b02767be2adfea38e3fdff47c9" FOREIGN KEY ("owner_id") REFERENCES "owner"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "superadmin" ADD CONSTRAINT "FK_a8f475d250f5bb4ab10977e4ea2" FOREIGN KEY ("owner_id") REFERENCES "owner"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "admin" ADD CONSTRAINT "FK_758bd6ff951dc166552e14a9393" FOREIGN KEY ("superadmin_id") REFERENCES "superadmin"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "admin" ADD CONSTRAINT "FK_b8f113de6f23998118435fa3b98" FOREIGN KEY ("owner_id") REFERENCES "owner"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "admin" DROP CONSTRAINT "FK_b8f113de6f23998118435fa3b98"`);
        await queryRunner.query(`ALTER TABLE "admin" DROP CONSTRAINT "FK_758bd6ff951dc166552e14a9393"`);
        await queryRunner.query(`ALTER TABLE "superadmin" DROP CONSTRAINT "FK_a8f475d250f5bb4ab10977e4ea2"`);
        await queryRunner.query(`ALTER TABLE "broker" DROP CONSTRAINT "FK_9b02767be2adfea38e3fdff47c9"`);
        await queryRunner.query(`ALTER TABLE "broker" DROP CONSTRAINT "FK_7d049f6cef07839db176c50774c"`);
        await queryRunner.query(`ALTER TABLE "advisor" DROP CONSTRAINT "FK_498ad66ddc714e114a26f43533d"`);
        await queryRunner.query(`ALTER TABLE "advisor" DROP CONSTRAINT "FK_158b4652b9a7c747359c9fe2f09"`);
        await queryRunner.query(`ALTER TABLE "investor" DROP CONSTRAINT "FK_4a98ce7d73b154d63f0aac4889e"`);
        await queryRunner.query(`ALTER TABLE "investor" DROP CONSTRAINT "FK_2f542bded5a8b34cc53d24d1c5c"`);
        await queryRunner.query(`ALTER TABLE "investor" DROP CONSTRAINT "FK_5aafdad076d9c769718a1a049a2"`);
        await queryRunner.query(`ALTER TABLE "permission" ADD "role_id" uuid`);
        await queryRunner.query(`DROP TABLE "admin"`);
        await queryRunner.query(`DROP TABLE "superadmin"`);
        await queryRunner.query(`DROP TABLE "broker"`);
        await queryRunner.query(`DROP TABLE "advisor"`);
        await queryRunner.query(`DROP TABLE "investor"`);
        await queryRunner.query(`ALTER TABLE "permission" ADD CONSTRAINT "FK_383892d758d08d346f837d3d8b7" FOREIGN KEY ("role_id") REFERENCES "role"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
}
exports.Migrations1707160914285 = Migrations1707160914285;
//# sourceMappingURL=1707160914285-migrations.js.map