{"version": 3, "file": "email.config.js", "sourceRoot": "/", "sources": ["shared/email/email.config.ts"], "names": [], "mappings": ";;;AAEA,gGAA4F;AAC5F,qCAA8B;AAC9B,iCAAiC;AACjC,+BAA4B;AAE5B,MAAM,CAAC,MAAM,EAAE,CAAC;AAEH,QAAA,aAAa,GAAkB;IAC1C,SAAS,EAAE;QACT,GAAG,EAAE,IAAI,aAAG,CAAC;YACX,MAAM,EAAE,WAAW;YACnB,WAAW,EAAE;gBACX,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc;gBACvC,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc;aAC5C;SACF,CAAC;KACH;IACD,QAAQ,EAAE;QACR,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,aAAa;KAChC;IACD,QAAQ,EAAE;QACR,GAAG,EAAE,IAAA,WAAI,EAAC,SAAS,EAAE,WAAW,CAAC;QACjC,OAAO,EAAE,IAAI,sCAAiB,EAAE;QAChC,OAAO,EAAE;YACP,MAAM,EAAE,IAAI;SACb;KACF;CACF,CAAC", "sourcesContent": ["/* eslint-disable import/no-extraneous-dependencies */\r\nimport { MailerOptions } from '@nestjs-modules/mailer';\r\nimport { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';\r\nimport { SES } from 'aws-sdk';\r\nimport * as dotenv from 'dotenv';\r\nimport { join } from 'path';\r\n\r\ndotenv.config();\r\n\r\nexport const mailerOptions: MailerOptions = {\r\n  transport: {\r\n    SES: new SES({\r\n      region: 'us-east-1',\r\n      credentials: {\r\n        accessKeyId: process.env.AWS_ACCESS_KEY,\r\n        secretAccessKey: process.env.AWS_SECRET_KEY,\r\n      },\r\n    }),\r\n  },\r\n  defaults: {\r\n    from: process.env.EMAIL_ADDRESS,\r\n  },\r\n  template: {\r\n    dir: join(__dirname, 'templates'),\r\n    adapter: new HandlebarsAdapter(), // or new PugAdapter() or new EjsAdapter()\r\n    options: {\r\n      strict: true,\r\n    },\r\n  },\r\n};\r\n"]}