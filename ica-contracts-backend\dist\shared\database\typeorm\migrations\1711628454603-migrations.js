"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations************* = void 0;
class Migrations************* {
    constructor() {
        this.name = 'Migrations*************';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "billet" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "account_id" uuid NOT NULL, "amount" integer NOT NULL, "due_date" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_9b069b774f7172ce2a6700ef4bb" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "billet" ADD CONSTRAINT "FK_e67d9237513dfc3b7a02dd7ddb3" FOREIGN KEY ("account_id") REFERENCES "account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "billet" DROP CONSTRAINT "FK_e67d9237513dfc3b7a02dd7ddb3"`);
        await queryRunner.query(`DROP TABLE "billet"`);
    }
}
exports.Migrations************* = Migrations*************;
//# sourceMappingURL=*************-migrations.js.map