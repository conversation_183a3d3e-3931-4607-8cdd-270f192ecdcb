{"version": 3, "file": "client-app.module.js", "sourceRoot": "/", "sources": ["modules/client-app/client-app.module.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAAwC;AACxC,qCAAwC;AACxC,6CAAgD;AAChD,4FAAsF;AACtF,4FAAsF;AACtF,4HAAoH;AACpH,kHAA0G;AAC1G,sFAAgF;AAEhF,uFAAkF;AAClF,yFAAoF;AACpF,mFAA8E;AAC9E,uFAAkF;AAClF,2GAAoG;AACpG,qEAA0D;AAC1D,8EAAyE;AACzE,8EAAyE;AACzE,gFAA2E;AAC3E,0EAAqE;AACrE,8EAAyE;AACzE,kGAA2F;AA0CpF,IAAM,eAAe,GAArB,MAAM,eAAe;CAAG,CAAA;AAAlB,0CAAe;0BAAf,eAAe;IAxC3B,IAAA,eAAM,EAAC;QACN,OAAO,EAAE;YACP,uBAAa,CAAC,UAAU,CAAC;gBACvB,0BAAW;gBACX,gCAAc;gBACd,gCAAc;gBACd,8DAA4B;gBAC5B,oDAAuB;aACxB,CAAC;YACF,eAAS,CAAC,QAAQ,CAAC;gBACjB,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU;gBAC9B,WAAW,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE;aACjC,CAAC;SACH;QACD,WAAW,EAAE;YACX,+CAAqB;YACrB,qDAAwB;YACxB,mDAAuB;YACvB,mDAAuB;YACvB,qEAA+B;SAChC;QACD,SAAS,EAAE;YACT,yCAAkB;YAClB,6CAAoB;YACpB,+CAAqB;YACrB,6CAAoB;YACpB,6CAAoB;YACpB,+DAA4B;YAC5B;gBACE,OAAO,EAAE,aAAa;gBACtB,QAAQ,EAAE,+BAAU;aACrB;SACF;QACD,OAAO,EAAE;YACP,yCAAkB;YAClB,6CAAoB;YACpB,+CAAqB;YACrB,6CAAoB;SACrB;KACF,CAAC;GACW,eAAe,CAAG", "sourcesContent": ["import { Module } from '@nestjs/common';\r\nimport { JwtModule } from '@nestjs/jwt';\r\nimport { TypeOrmModule } from '@nestjs/typeorm';\r\nimport { BusinessEntity } from 'src/shared/database/typeorm/entities/business.entity';\r\nimport { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';\r\nimport { IncomePaymentScheduledEntity } from 'src/shared/database/typeorm/entities/income-payment-scheduled.entity';\r\nimport { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';\r\nimport { OwnerEntity } from 'src/shared/database/typeorm/entities/owner.entity';\r\n\r\nimport { CheckInvestorController } from './controllers/check-investor.controller';\r\nimport { ClientContractController } from './controllers/client-contract.controller';\r\nimport { ClientLoginController } from './controllers/client-login.controller';\r\nimport { ClientPaymentController } from './controllers/client-payment.controller';\r\nimport { GetInvestorByDocumentController } from './controllers/get-investor-by-document.controller';\r\nimport { formatDate } from './helpers/format-date.helper';\r\nimport { CheckInvestorService } from './services/check-investor.service';\r\nimport { ClientAccountService } from './services/client-account.service';\r\nimport { ClientContractService } from './services/client-contract.service';\r\nimport { ClientLoginService } from './services/client-login.service';\r\nimport { ClientPaymentService } from './services/client-payment.service';\r\nimport { GetInvestorByDocumentService } from './services/get-investor-by-document.service';\r\n\r\n@Module({\r\n  imports: [\r\n    TypeOrmModule.forFeature([\r\n      OwnerEntity,\r\n      BusinessEntity,\r\n      ContractEntity,\r\n      IncomePaymentScheduledEntity,\r\n      OwnerRoleRelationEntity,\r\n    ]),\r\n    JwtModule.register({\r\n      secret: process.env.JWT_SECRET,\r\n      signOptions: { expiresIn: '1h' },\r\n    }),\r\n  ],\r\n  controllers: [\r\n    ClientLoginController,\r\n    ClientContractController,\r\n    ClientPaymentController,\r\n    CheckInvestorController,\r\n    GetInvestorByDocumentController,\r\n  ],\r\n  providers: [\r\n    ClientLoginService,\r\n    ClientAccountService,\r\n    ClientContractService,\r\n    ClientPaymentService,\r\n    CheckInvestorService,\r\n    GetInvestorByDocumentService,\r\n    {\r\n      provide: 'FORMAT_DATE',\r\n      useValue: formatDate,\r\n    },\r\n  ],\r\n  exports: [\r\n    ClientLoginService,\r\n    ClientAccountService,\r\n    ClientContractService,\r\n    ClientPaymentService,\r\n  ],\r\n})\r\nexport class ClientAppModule {}\r\n"]}