"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1737382712775 = void 0;
class Migrations1737382712775 {
    constructor() {
        this.name = 'Migrations1737382712775';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "notification" ADD "contract_id" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "contract_advisor" DROP CONSTRAINT "FK_b3eb1d7d01d429b1fc2ae37c05b"`);
        await queryRunner.query(`ALTER TABLE "contract_advisor" DROP CONSTRAINT "FK_cba07ac9ce330bc89efb1e82786"`);
        await queryRunner.query(`ALTER TABLE "contract_advisor" DROP CONSTRAINT "UQ_57ddfe73a0f966bba89f031cc9d"`);
        await queryRunner.query(`ALTER TABLE "contract_advisor" ALTER COLUMN "id_contract" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "contract_advisor" ALTER COLUMN "id_advisor" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "contract_advisor" ADD CONSTRAINT "UQ_57ddfe73a0f966bba89f031cc9d" UNIQUE ("id_contract", "id_advisor")`);
        await queryRunner.query(`ALTER TABLE "contract_advisor" ADD CONSTRAINT "FK_b3eb1d7d01d429b1fc2ae37c05b" FOREIGN KEY ("id_contract") REFERENCES "contract"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract_advisor" ADD CONSTRAINT "FK_cba07ac9ce330bc89efb1e82786" FOREIGN KEY ("id_advisor") REFERENCES "owner_role_relation"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "contract_advisor" DROP CONSTRAINT "FK_cba07ac9ce330bc89efb1e82786"`);
        await queryRunner.query(`ALTER TABLE "contract_advisor" DROP CONSTRAINT "FK_b3eb1d7d01d429b1fc2ae37c05b"`);
        await queryRunner.query(`ALTER TABLE "contract_advisor" DROP CONSTRAINT "UQ_57ddfe73a0f966bba89f031cc9d"`);
        await queryRunner.query(`ALTER TABLE "contract_advisor" ALTER COLUMN "id_advisor" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "contract_advisor" ALTER COLUMN "id_contract" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "contract_advisor" ADD CONSTRAINT "UQ_57ddfe73a0f966bba89f031cc9d" UNIQUE ("id_contract", "id_advisor")`);
        await queryRunner.query(`ALTER TABLE "contract_advisor" ADD CONSTRAINT "FK_cba07ac9ce330bc89efb1e82786" FOREIGN KEY ("id_advisor") REFERENCES "owner_role_relation"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract_advisor" ADD CONSTRAINT "FK_b3eb1d7d01d429b1fc2ae37c05b" FOREIGN KEY ("id_contract") REFERENCES "contract"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "notification" DROP COLUMN "contract_id"`);
    }
}
exports.Migrations1737382712775 = Migrations1737382712775;
//# sourceMappingURL=1737382712775-migrations.js.map