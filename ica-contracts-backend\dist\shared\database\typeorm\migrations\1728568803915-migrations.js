"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1728568803915 = void 0;
class Migrations1728568803915 {
    constructor() {
        this.name = 'Migrations1728568803915';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "contract_event_attachment" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "file_url" text NOT NULL, "file_name" character varying(255) NOT NULL, "contract_event_id" uuid NOT NULL, CONSTRAINT "PK_23b9222cd3d55cf4f3688b8baa1" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "contract_event_attachment" ADD CONSTRAINT "FK_fa9466d0f494941cf4bc83d3cc1" FOREIGN KEY ("contract_event_id") REFERENCES "contract_event"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "contract_event_attachment" DROP CONSTRAINT "FK_fa9466d0f494941cf4bc83d3cc1"`);
        await queryRunner.query(`DROP TABLE "contract_event_attachment"`);
    }
}
exports.Migrations1728568803915 = Migrations1728568803915;
//# sourceMappingURL=1728568803915-migrations.js.map