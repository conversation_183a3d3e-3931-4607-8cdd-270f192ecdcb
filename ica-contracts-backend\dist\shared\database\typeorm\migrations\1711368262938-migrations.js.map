{"version": 3, "file": "1711368262938-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1711368262938-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAiBnC,CAAC;IAfQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,yfAAyf,CAC1f,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,qLAAqL,CACtL,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,wFAAwF,CACzF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;IAClE,CAAC;CACF;AAlBD,0DAkBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1711368262938 implements MigrationInterface {\r\n  name = 'Migrations1711368262938';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"account_transfer-limits\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"daily_limit\" integer NOT NULL DEFAULT '1000', \"monthly_limit\" integer NOT NULL DEFAULT '1000', \"daily_night_limit\" integer NOT NULL DEFAULT '1000', \"account_id\" uuid, \"updated_at\" TIMESTAMP NOT NULL DEFAULT now(), \"created_at\" TIMESTAMP NOT NULL DEFAULT now(), \"deleted_at\" TIMESTAMP, CONSTRAINT \"REL_51fc575749fb624f8debaab8c5\" UNIQUE (\"account_id\"), CONSTRAINT \"PK_f9c66b7635fc8816ab0573356bc\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" ADD CONSTRAINT \"FK_51fc575749fb624f8debaab8c5c\" FOREIGN KEY (\"account_id\") REFERENCES \"account\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" DROP CONSTRAINT \"FK_51fc575749fb624f8debaab8c5c\"`,\r\n    );\r\n    await queryRunner.query(`DROP TABLE \"account_transfer-limits\"`);\r\n  }\r\n}\r\n"]}