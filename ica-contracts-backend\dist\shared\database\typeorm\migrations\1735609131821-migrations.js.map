{"version": 3, "file": "1735609131821-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1735609131821-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAuBnC,CAAC;IArBQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,0UAA0U,CAC3U,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,yLAAyL,CAC1L,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,+JAA+J,CAChK,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,uEAAuE,CACxE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,uEAAuE,CACxE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;IACjD,CAAC;CACF;AAxBD,0DAwBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1735609131821 implements MigrationInterface {\r\n  name = 'Migrations1735609131821';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"report\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"type\" character varying NOT NULL, \"createdAt\" TIMESTAMP NOT NULL DEFAULT now(), \"updatedAt\" TIMESTAMP NOT NULL DEFAULT now(), \"deleted_at\" TIMESTAMP, \"owner_role_relation\" uuid, \"file_id\" uuid, CONSTRAINT \"PK_99e4d0bea58cba73c57f935a546\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"report\" ADD CONSTRAINT \"FK_266246ede9f575c5ef3c1c079ec\" FOREIGN KEY (\"owner_role_relation\") REFERENCES \"owner_role_relation\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"report\" ADD CONSTRAINT \"FK_1ee07d7eee68a3ea0a9a73e1454\" FOREIGN KEY (\"file_id\") REFERENCES \"files\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"report\" DROP CONSTRAINT \"FK_1ee07d7eee68a3ea0a9a73e1454\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"report\" DROP CONSTRAINT \"FK_266246ede9f575c5ef3c1c079ec\"`,\r\n    );\r\n    await queryRunner.query(`DROP TABLE \"report\"`);\r\n  }\r\n}\r\n"]}