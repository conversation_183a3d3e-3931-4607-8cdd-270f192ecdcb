"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1727370931879 = void 0;
class Migrations1727370931879 {
    constructor() {
        this.name = 'Migrations1727370931879';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "contract" ALTER COLUMN "external_id" DROP NOT NULL`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "contract" ALTER COLUMN "external_id" SET NOT NULL`);
    }
}
exports.Migrations1727370931879 = Migrations1727370931879;
//# sourceMappingURL=1727370931879-migrations.js.map