{"version": 3, "file": "1726118210916-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1726118210916-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAuBnC,CAAC;IArBQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,g/BAAg/B,CACj/B,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,sLAAsL,CACvL,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,4KAA4K,CAC7K,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,6EAA6E,CAC9E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,6EAA6E,CAC9E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;IACvD,CAAC;CACF;AAxBD,0DAwBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1726118210916 implements MigrationInterface {\r\n  name = 'Migrations1726118210916';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"pre_register\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"name\" character varying NOT NULL, \"rg\" character varying NOT NULL, \"document\" character varying NOT NULL, \"phone_number\" character varying NOT NULL, \"dt_birth\" date NOT NULL, \"email\" character varying NOT NULL, \"zip_code\" character varying NOT NULL, \"neighborhood\" character varying NOT NULL, \"city\" character varying NOT NULL, \"address_complement\" character varying, \"address_number\" character varying NOT NULL, \"investment_value\" numeric NOT NULL, \"investment_term\" character varying NOT NULL, \"investment_yield\" numeric NOT NULL, \"investment_modality\" character varying NOT NULL, \"observations\" character varying NOT NULL, \"status\" character varying NOT NULL, \"adviser_id\" uuid NOT NULL, \"purchase_with\" character varying NOT NULL, \"amount_quotes\" integer NOT NULL, \"grace_period\" date NOT NULL, \"created_at\" TIMESTAMP NOT NULL DEFAULT now(), \"contract_id\" uuid, CONSTRAINT \"PK_da786d4c6f01e1cbb0be67607a6\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pre_register\" ADD CONSTRAINT \"FK_db44bd7010cefe5ae4a9bb5506f\" FOREIGN KEY (\"adviser_id\") REFERENCES \"owner_role_relation\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pre_register\" ADD CONSTRAINT \"FK_2a142caa100d41db710a3f56734\" FOREIGN KEY (\"contract_id\") REFERENCES \"contract\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pre_register\" DROP CONSTRAINT \"FK_2a142caa100d41db710a3f56734\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pre_register\" DROP CONSTRAINT \"FK_db44bd7010cefe5ae4a9bb5506f\"`,\r\n    );\r\n    await queryRunner.query(`DROP TABLE \"pre_register\"`);\r\n  }\r\n}\r\n"]}