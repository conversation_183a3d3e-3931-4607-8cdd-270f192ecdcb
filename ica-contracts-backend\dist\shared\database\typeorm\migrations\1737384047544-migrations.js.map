{"version": 3, "file": "1737384047544-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1737384047544-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACI,SAAI,GAAG,yBAAyB,CAAA;IAYpC,CAAC;IAVU,KAAK,CAAC,EAAE,CAAC,WAAwB;QACpC,MAAM,WAAW,CAAC,KAAK,CAAC,yDAAyD,CAAC,CAAC;QACnF,MAAM,WAAW,CAAC,KAAK,CAAC,yDAAyD,CAAC,CAAC;IACvF,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CAAC,yDAAyD,CAAC,CAAC;QACnF,MAAM,WAAW,CAAC,KAAK,CAAC,yDAAyD,CAAC,CAAC;IACvF,CAAC;CAEJ;AAbD,0DAaC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from \"typeorm\";\r\n\r\nexport class Migrations1737384047544 implements MigrationInterface {\r\n    name = 'Migrations1737384047544'\r\n\r\n    public async up(queryRunner: QueryRunner): Promise<void> {\r\n        await queryRunner.query(`ALTER TABLE \"notification\" DROP COLUMN \"contract_value\"`);\r\n        await queryRunner.query(`ALTER TABLE \"notification\" ADD \"contract_value\" numeric`);\r\n    }\r\n\r\n    public async down(queryRunner: QueryRunner): Promise<void> {\r\n        await queryRunner.query(`ALTER TABLE \"notification\" DROP COLUMN \"contract_value\"`);\r\n        await queryRunner.query(`ALTER TABLE \"notification\" ADD \"contract_value\" integer`);\r\n    }\r\n\r\n}\r\n"]}