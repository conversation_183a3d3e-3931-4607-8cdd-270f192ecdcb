"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.formatDate = formatDate;
function formatDate(date) {
    try {
        const dateObj = date instanceof Date ? date : new Date(date);
        const year = dateObj.getUTCFullYear();
        const month = String(dateObj.getUTCMonth() + 1).padStart(2, '0');
        const day = String(dateObj.getUTCDate()).padStart(2, '0');
        return `${day}/${month}/${year}`;
    }
    catch (error) {
        console.error('Error formatting date:', date, error);
        return 'Invalid date';
    }
}
//# sourceMappingURL=format-date.helper.js.map