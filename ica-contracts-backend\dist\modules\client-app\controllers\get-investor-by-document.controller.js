"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetInvestorByDocumentController = void 0;
const common_1 = require("@nestjs/common");
const get_investor_by_document_dto_1 = require("../dto/get-investor-by-document.dto");
const get_investor_by_document_service_1 = require("../services/get-investor-by-document.service");
let GetInvestorByDocumentController = class GetInvestorByDocumentController {
    constructor(getInvestorByDocumentService) {
        this.getInvestorByDocumentService = getInvestorByDocumentService;
    }
    async getInvestorByDocument(params, apiKey) {
        try {
            if (!apiKey) {
                throw new common_1.HttpException('API key é obrigatória', 401);
            }
            const result = await this.getInvestorByDocumentService.perform(params, apiKey);
            return result;
        }
        catch (error) {
            throw new common_1.HttpException({
                status: common_1.HttpStatus.BAD_REQUEST,
            }, common_1.HttpStatus.BAD_REQUEST, {
                cause: error,
            });
        }
    }
};
exports.GetInvestorByDocumentController = GetInvestorByDocumentController;
__decorate([
    (0, common_1.Get)(':document'),
    __param(0, (0, common_1.Param)()),
    __param(1, (0, common_1.Headers)('x-api-key')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_investor_by_document_dto_1.GetInvestorByDocumentDto, String]),
    __metadata("design:returntype", Promise)
], GetInvestorByDocumentController.prototype, "getInvestorByDocument", null);
exports.GetInvestorByDocumentController = GetInvestorByDocumentController = __decorate([
    (0, common_1.Controller)('client/investor'),
    __metadata("design:paramtypes", [get_investor_by_document_service_1.GetInvestorByDocumentService])
], GetInvestorByDocumentController);
//# sourceMappingURL=get-investor-by-document.controller.js.map