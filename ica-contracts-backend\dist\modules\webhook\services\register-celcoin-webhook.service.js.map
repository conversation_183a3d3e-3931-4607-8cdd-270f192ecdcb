{"version": 3, "file": "register-celcoin-webhook.service.js", "sourceRoot": "/", "sources": ["modules/webhook/services/register-celcoin-webhook.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,sGAA4F;AAKrF,IAAM,6BAA6B,GAAnC,MAAM,6BAA6B;IACxC,YACmB,sBAA8C;QAA9C,2BAAsB,GAAtB,sBAAsB,CAAwB;IAC9D,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,KAAgC;QAC5C,OAAO,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IACpD,CAAC;CACF,CAAA;AARY,sEAA6B;wCAA7B,6BAA6B;IADzC,IAAA,mBAAU,GAAE;qCAGgC,iDAAsB;GAFtD,6BAA6B,CAQzC", "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { RegisterWebhookService } from 'src/apis/celcoin/services/register-webhook.service';\r\n\r\nimport { RegisterCelcoinWebhookDto } from '../dto/register-celcoin-webhook.dto';\r\n\r\n@Injectable()\r\nexport class RegisterCelcoinWebhookService {\r\n  constructor(\r\n    private readonly registerWebhookService: RegisterWebhookService,\r\n  ) {}\r\n\r\n  async perform(input: RegisterCelcoinWebhookDto) {\r\n    return this.registerWebhookService.perform(input);\r\n  }\r\n}\r\n"]}