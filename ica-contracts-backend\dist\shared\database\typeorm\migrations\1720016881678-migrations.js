"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations************* = void 0;
class Migrations************* {
    constructor() {
        this.name = 'Migrations*************';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" DROP CONSTRAINT "FK_51fc575749fb624f8debaab8c5c"`);
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" DROP CONSTRAINT "REL_51fc575749fb624f8debaab8c5"`);
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" ADD CONSTRAINT "FK_51fc575749fb624f8debaab8c5c" FOREIGN KEY ("account_id") REFERENCES "account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" DROP CONSTRAINT "FK_51fc575749fb624f8debaab8c5c"`);
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" ADD CONSTRAINT "REL_51fc575749fb624f8debaab8c5" UNIQUE ("account_id")`);
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" ADD CONSTRAINT "FK_51fc575749fb624f8debaab8c5c" FOREIGN KEY ("account_id") REFERENCES "account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
}
exports.Migrations************* = Migrations*************;
//# sourceMappingURL=*************-migrations.js.map