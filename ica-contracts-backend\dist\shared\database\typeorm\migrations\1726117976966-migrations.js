"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1726117976966 = void 0;
class Migrations1726117976966 {
    constructor() {
        this.name = 'Migrations1726117976966';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "pre-register" ADD "purchase_with" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "pre-register" ADD "amount_quotes" integer NOT NULL`);
        await queryRunner.query(`ALTER TABLE "pre-register" ADD "grace_period" date NOT NULL`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "pre-register" DROP COLUMN "grace_period"`);
        await queryRunner.query(`ALTER TABLE "pre-register" DROP COLUMN "amount_quotes"`);
        await queryRunner.query(`ALTER TABLE "pre-register" DROP COLUMN "purchase_with"`);
    }
}
exports.Migrations1726117976966 = Migrations1726117976966;
//# sourceMappingURL=1726117976966-migrations.js.map