import { BusinessEntity } from 'src/shared/database/typeorm/entities/business.entity';
import { OwnerEntity } from 'src/shared/database/typeorm/entities/owner.entity';
import { Repository } from 'typeorm';
import { CreateBusinessRequestDto } from '../dto/create-business.dto';
export declare class CreateBusinessService {
    private readonly ownerRepository;
    private readonly businessRepository;
    constructor(ownerRepository: Repository<OwnerEntity>, businessRepository: Repository<BusinessEntity>);
    perform(input: CreateBusinessRequestDto): Promise<{
        name: string;
        cpf: string;
        email: string;
        password: string;
        ownerBusinessRelation: {
            business: {
                cnpj: string;
                companyName: string;
                email: string;
            };
        }[];
    } & OwnerEntity>;
    private generatePassword;
}
