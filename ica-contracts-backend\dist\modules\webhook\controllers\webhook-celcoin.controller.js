"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebhookCelcoinController = void 0;
const common_1 = require("@nestjs/common");
const consult_celcoin_webhooks_dto_1 = require("../dto/consult-celcoin-webhooks.dto");
const create_acount_celcoin_webhook_dto_1 = require("../dto/create-acount-celcoin-webhook.dto");
const register_celcoin_webhook_dto_1 = require("../dto/register-celcoin-webhook.dto");
const update_celcoin_webhooks_dto_1 = require("../dto/update-celcoin-webhooks.dto");
const billpayment_celcoin_webhook_service_1 = require("../services/billpayment-celcoin-webhook.service");
const celcoin_pix_payment_webhook_service_1 = require("../services/celcoin-pix-payment-webhook.service");
const charge_create_celcoin_webhook_service_1 = require("../services/charge-create-celcoin-webhook.service");
const charge_in_celcoin_webhook_service_1 = require("../services/charge-in-celcoin-webhook.service");
const consult_celcoin_webhook_service_1 = require("../services/consult-celcoin-webhook.service");
const create_account_celcoin_webhook_service_1 = require("../services/create-account-celcoin-webhook.service");
const kyc_celcoin_webhook_service_1 = require("../services/kyc-celcoin-webhook.service");
const pix_cashout_celcoin_webhook_service_1 = require("../services/pix-cashout-celcoin-webhook.service");
const pix_claim_cancelled_celcoin_webhook_service_1 = require("../services/pix-claim-cancelled-celcoin-webhook.service");
const pix_claim_completed_celcoin_webhook_service_1 = require("../services/pix-claim-completed-celcoin-webhook.service");
const pix_claim_confirmed_celcoin_webhook_service_1 = require("../services/pix-claim-confirmed-celcoin-webhook.service");
const pix_claim_open_celcoin_webhook_service_1 = require("../services/pix-claim-open-celcoin-webhook.service");
const pix_claim_waiting_celcoin_webhook_service_1 = require("../services/pix-claim-waiting-celcoin-webhook.service");
const register_celcoin_webhook_service_1 = require("../services/register-celcoin-webhook.service");
const update_celcoin_webhook_service_1 = require("../services/update-celcoin-webhook.service");
let WebhookCelcoinController = class WebhookCelcoinController {
    constructor(celcoinPixPaymentWebhookService, updateWebhooksService, pixClaimOpenCelcoinWebhookService, billPaymentCelcoinWebhookService, createAccountService, pixCashoutService, chargeInCelcoinWebhookService, chargeCreateCelcoinWebhookService, kycCelcoinWebhookService, registerCelcoinWebhookService, consultCelcoinWebhookService, pixClaimWaitingCelcoinWebhookService, pixClaimConfirmedCelcoinWebhookService, pixClaimCancelledCelcoinWebhookService, pixClaimCompletedCelcoinWebhookService) {
        this.celcoinPixPaymentWebhookService = celcoinPixPaymentWebhookService;
        this.updateWebhooksService = updateWebhooksService;
        this.pixClaimOpenCelcoinWebhookService = pixClaimOpenCelcoinWebhookService;
        this.billPaymentCelcoinWebhookService = billPaymentCelcoinWebhookService;
        this.createAccountService = createAccountService;
        this.pixCashoutService = pixCashoutService;
        this.chargeInCelcoinWebhookService = chargeInCelcoinWebhookService;
        this.chargeCreateCelcoinWebhookService = chargeCreateCelcoinWebhookService;
        this.kycCelcoinWebhookService = kycCelcoinWebhookService;
        this.registerCelcoinWebhookService = registerCelcoinWebhookService;
        this.consultCelcoinWebhookService = consultCelcoinWebhookService;
        this.pixClaimWaitingCelcoinWebhookService = pixClaimWaitingCelcoinWebhookService;
        this.pixClaimConfirmedCelcoinWebhookService = pixClaimConfirmedCelcoinWebhookService;
        this.pixClaimCancelledCelcoinWebhookService = pixClaimCancelledCelcoinWebhookService;
        this.pixClaimCompletedCelcoinWebhookService = pixClaimCompletedCelcoinWebhookService;
    }
    async registerWebhook(body) {
        await this.registerCelcoinWebhookService.perform(body);
    }
    async consultWebhooks(query) {
        return this.consultCelcoinWebhookService.perform(query);
    }
    async updateWebhooks(body) {
        return this.updateWebhooksService.perform(body);
    }
    async createAccount(data) {
        await this.createAccountService.perform(data);
    }
    async pixCashout(data) {
        await this.pixCashoutService.perform(data);
    }
    async cashInWebhook(body) {
        await this.celcoinPixPaymentWebhookService.perform(body);
    }
    async claimOpen(body) {
        await this.pixClaimOpenCelcoinWebhookService.perform(body);
    }
    async claimWaiting(body) {
        await this.pixClaimWaitingCelcoinWebhookService.perform(body);
    }
    async claimConfirmed(body) {
        await this.pixClaimConfirmedCelcoinWebhookService.perform(body);
    }
    async claimCancelled(body) {
        await this.pixClaimCancelledCelcoinWebhookService.perform(body);
    }
    async claimCompleted(body) {
        await this.pixClaimCompletedCelcoinWebhookService.perform(body);
    }
    async billPayment(body) {
        await this.billPaymentCelcoinWebhookService.perform(body);
    }
    async chargeIn(body) {
        await this.chargeInCelcoinWebhookService.perform(body);
    }
    async chargeCreate(body) {
        await this.chargeCreateCelcoinWebhookService.perform(body);
    }
    async kyc(body) {
        await this.kycCelcoinWebhookService.perform(body);
    }
};
exports.WebhookCelcoinController = WebhookCelcoinController;
__decorate([
    (0, common_1.Post)('register'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [register_celcoin_webhook_dto_1.RegisterCelcoinWebhookDto]),
    __metadata("design:returntype", Promise)
], WebhookCelcoinController.prototype, "registerWebhook", null);
__decorate([
    (0, common_1.Get)('consult'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [consult_celcoin_webhooks_dto_1.ConsultCelcoinWebhookDto]),
    __metadata("design:returntype", Promise)
], WebhookCelcoinController.prototype, "consultWebhooks", null);
__decorate([
    (0, common_1.Put)('update'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [update_celcoin_webhooks_dto_1.UpdateCelcoinWebhookDto]),
    __metadata("design:returntype", Promise)
], WebhookCelcoinController.prototype, "updateWebhooks", null);
__decorate([
    (0, common_1.Post)('create'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_acount_celcoin_webhook_dto_1.CreateAccountCelcoinWebhookDto]),
    __metadata("design:returntype", Promise)
], WebhookCelcoinController.prototype, "createAccount", null);
__decorate([
    (0, common_1.Post)('pix/cashout'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebhookCelcoinController.prototype, "pixCashout", null);
__decorate([
    (0, common_1.Post)('pix/cashin'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebhookCelcoinController.prototype, "cashInWebhook", null);
__decorate([
    (0, common_1.Post)('pix-key/claim-open'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebhookCelcoinController.prototype, "claimOpen", null);
__decorate([
    (0, common_1.Post)('pix-key/claim-waiting'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebhookCelcoinController.prototype, "claimWaiting", null);
__decorate([
    (0, common_1.Post)('pix-key/claim-confirmed'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebhookCelcoinController.prototype, "claimConfirmed", null);
__decorate([
    (0, common_1.Post)('pix-key/claim-cancelled'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebhookCelcoinController.prototype, "claimCancelled", null);
__decorate([
    (0, common_1.Post)('pix-key/claim-completed'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebhookCelcoinController.prototype, "claimCompleted", null);
__decorate([
    (0, common_1.Post)('boleto/billpayment'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebhookCelcoinController.prototype, "billPayment", null);
__decorate([
    (0, common_1.Post)('/charge-in'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebhookCelcoinController.prototype, "chargeIn", null);
__decorate([
    (0, common_1.Post)('/charge-create'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebhookCelcoinController.prototype, "chargeCreate", null);
__decorate([
    (0, common_1.Post)('/kyc'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebhookCelcoinController.prototype, "kyc", null);
exports.WebhookCelcoinController = WebhookCelcoinController = __decorate([
    (0, common_1.Controller)('webhook/celcoin'),
    __metadata("design:paramtypes", [celcoin_pix_payment_webhook_service_1.CelcoinPixPaymentWebhookService,
        update_celcoin_webhook_service_1.UpdateCelcoinWebhookService,
        pix_claim_open_celcoin_webhook_service_1.PixClaimOpenCelcoinWebhookService,
        billpayment_celcoin_webhook_service_1.BillPaymentCelcoinWebhookService,
        create_account_celcoin_webhook_service_1.CreateAccountCelcoinWebhookService,
        pix_cashout_celcoin_webhook_service_1.PixCashoutCelcoinWebhookService,
        charge_in_celcoin_webhook_service_1.ChargeInCelcoinWebhookService,
        charge_create_celcoin_webhook_service_1.ChargeCreateCelcoinWebhookService,
        kyc_celcoin_webhook_service_1.KycCelcoinWebhookService,
        register_celcoin_webhook_service_1.RegisterCelcoinWebhookService,
        consult_celcoin_webhook_service_1.ConsultCelcoinWebhookService,
        pix_claim_waiting_celcoin_webhook_service_1.PixClaimWaitingCelcoinWebhookService,
        pix_claim_confirmed_celcoin_webhook_service_1.PixClaimConfirmedCelcoinWebhookService,
        pix_claim_cancelled_celcoin_webhook_service_1.PixClaimCancelledCelcoinWebhookService,
        pix_claim_completed_celcoin_webhook_service_1.PixClaimCompletedCelcoinWebhookService])
], WebhookCelcoinController);
//# sourceMappingURL=webhook-celcoin.controller.js.map