{"version": 3, "file": "1727095677188-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1727095677188-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IASnC,CAAC;IAPQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAC;IAC7E,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;IAC3E,CAAC;CACF;AAVD,0DAUC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1727095677188 implements MigrationInterface {\r\n  name = 'Migrations1727095677188';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(`ALTER TABLE \"income\" ADD \"deleted_at\" TIMESTAMP`);\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(`ALTER TABLE \"income\" DROP COLUMN \"deleted_at\"`);\r\n  }\r\n}\r\n"]}