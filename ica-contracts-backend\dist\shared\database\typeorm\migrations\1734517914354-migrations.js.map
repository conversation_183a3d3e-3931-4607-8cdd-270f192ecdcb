{"version": 3, "file": "1734517914354-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1734517914354-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAiBnC,CAAC;IAfQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,8XAA8X,CAC/X,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,+LAA+L,CAChM,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,6EAA6E,CAC9E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;IACvD,CAAC;CACF;AAlBD,0DAkBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1734517914354 implements MigrationInterface {\r\n  name = 'Migrations1734517914354';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"notification\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"title\" character varying, \"message\" character varying, \"viewed\" boolean NOT NULL DEFAULT false, \"createdAt\" TIMESTAMP NOT NULL DEFAULT now(), \"updatedAt\" TIMESTAMP NOT NULL DEFAULT now(), \"deleted_at\" TIMESTAMP, \"owner_role_relation\" uuid, CONSTRAINT \"PK_705b6c7cdf9b2c2ff7ac7872cb7\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"notification\" ADD CONSTRAINT \"FK_227bce406a187fd48c06b102fe0\" FOREIGN KEY (\"owner_role_relation\") REFERENCES \"owner_role_relation\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"notification\" DROP CONSTRAINT \"FK_227bce406a187fd48c06b102fe0\"`,\r\n    );\r\n    await queryRunner.query(`DROP TABLE \"notification\"`);\r\n  }\r\n}\r\n"]}