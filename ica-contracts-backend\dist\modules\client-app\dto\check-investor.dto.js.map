{"version": 3, "file": "check-investor.dto.js", "sourceRoot": "/", "sources": ["modules/client-app/dto/check-investor.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,yEAAwD;AACxD,qDAAsD;AAEtD,MAAa,gBAAgB;CAK5B;AALD,4CAKC;AADC;IAHC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;IACV,IAAA,uCAAW,GAAE;;kDACG", "sourcesContent": ["import { IsCPFOrCNPJ } from 'brazilian-class-validator';\r\nimport { IsDefined, IsString } from 'class-validator';\r\n\r\nexport class CheckInvestorDto {\r\n  @IsDefined()\r\n  @IsString()\r\n  @IsCPFOrCNPJ()\r\n  document: string;\r\n}"]}