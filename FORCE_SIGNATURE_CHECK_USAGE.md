# Solução para Problema de Atualização de Status após Assinatura

## Problema Identificado

Após a assinatura do contrato de upgrade pelo super admin, o status não estava sendo atualizado automaticamente e o histórico não estava sendo exibido. Isso acontece porque:

1. **Sistema de Polling**: O sistema usa cron jobs que verificam o status das assinaturas a cada 5 minutos
2. **Delay na Atualização**: Pode haver um delay de até 10 minutos entre a assinatura e a atualização do status
3. **Possível Falha nos Workers**: Os workers podem não estar rodando ou ter algum erro

## Solução Implementada

### 1. Endpoint de Verificação Forçada

**Contract Service**: `POST /contracts/:contractId/force-signature-check`
- Força a verificação imediata do status de assinatura
- Executa os mesmos use cases que os workers executam periodicamente

**ICA Contracts Backend**: `POST /contract/:id/force-signature-check`
- Endpoint público que chama o contract service
- Disponível para o frontend

### 2. Como Usar

#### Via API Diretamente
```bash
curl -X POST \
  http://localhost:3000/contract/{contractId}/force-signature-check \
  -H "Authorization: Bearer {seu-token}" \
  -H "Content-Type: application/json"
```

#### No Frontend (React)
```tsx
import ForceSignatureCheckButton from '@/components/ForceSignatureCheckButton';

function ContractCard({ contract }) {
  const handleStatusUpdate = (newStatus: string) => {
    console.log('Novo status:', newStatus);
    // Atualizar UI conforme necessário
    // Recarregar dados, atualizar estado, etc.
  };

  return (
    <div>
      <h3>{contract.name}</h3>
      <p>Status: {contract.status}</p>

      {contract.status !== 'ACTIVE' && (
        <ForceSignatureCheckButton
          contractId={contract.id}
          onSuccess={handleStatusUpdate}
        />
      )}
    </div>
  );
}
```

#### Usando o Hook Diretamente
```tsx
import { useForceSignatureCheck } from '@/hooks/useForceSignatureCheck';

function MyComponent() {
  const { isLoading, checkSignatureStatus } = useForceSignatureCheck();

  const handleCheck = async () => {
    const response = await checkSignatureStatus(contractId);
    if (response) {
      console.log('Status atualizado:', response.status);
    }
  };

  return (
    <button onClick={handleCheck} disabled={isLoading}>
      {isLoading ? 'Verificando...' : 'Verificar Status'}
    </button>
  );
}
```

### 3. Fluxo de Funcionamento

1. **Usuário clica no botão** "Verificar Status"
2. **Frontend chama** `POST /contract/:id/force-signature-check`
3. **Backend chama** `POST /contracts/:contractId/force-signature-check` no contract service
4. **Contract service executa**:
   - `ProcessInvestorSignatureUseCase` (verifica se investidor assinou)
   - `ProcessAuditSignatureUseCase` (verifica se auditoria assinou)
5. **Status é atualizado** no banco de dados se necessário
6. **Resposta retorna** com o novo status
7. **Frontend atualiza** a interface

### 4. Benefícios

- ✅ **Verificação Imediata**: Não precisa esperar até 10 minutos pelo cron job
- ✅ **Melhor UX**: Usuários podem forçar atualização quando necessário
- ✅ **Transparência**: Feedback imediato sobre o status da assinatura
- ✅ **Flexibilidade**: Pode ser usado em qualquer parte do sistema
- ✅ **Não Invasivo**: Não altera o funcionamento existente dos workers

### 5. Onde Adicionar o Botão

Recomendo adicionar o botão nas seguintes telas:

1. **Lista de Contratos** (para brokers/advisors)
2. **Detalhes do Contrato**
3. **Dashboard de Contratos Pendentes**
4. **Tela de Auditoria** (para super admins)

### 6. Exemplo de Integração na Lista de Contratos

```tsx
// Na tabela de contratos, adicionar uma coluna "Ações"
<td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
  {contract.status !== 'ACTIVE' && (
    <ForceSignatureCheckButton
      contractId={contract.id}
      onSuccess={() => {
        // Recarregar a lista de contratos
        refetchContracts();
      }}
      className="mr-2"
    />
  )}
</td>
```

### 7. Monitoramento

Para monitorar o uso da funcionalidade, verifique os logs:

```bash
# Contract Service
tail -f logs/contract-service.log | grep "force-signature-check"

# ICA Contracts Backend
tail -f logs/ica-contracts-backend.log | grep "ForceSignatureCheck"
```

### 8. Troubleshooting

**Problema**: Botão não aparece
- **Verificar**: Se o contrato tem um dos status esperados
- **Solução**: Confirmar que o status está correto no banco

**Problema**: Erro 404 no endpoint
- **Verificar**: Se o contract-service está rodando
- **Verificar**: Se a variável `API_CONTRACT_SERVICE_URL` está configurada
- **Solução**: Configurar corretamente as variáveis de ambiente

**Problema**: Erro de timeout
- **Verificar**: Se o RBM está respondendo
- **Solução**: Verificar conectividade com o provedor de assinatura

Esta solução resolve o problema imediato e fornece uma ferramenta útil para os usuários forçarem a verificação quando necessário.
