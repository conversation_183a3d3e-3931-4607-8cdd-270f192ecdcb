{"version": 3, "file": "1715797111306-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1715797111306-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAWnC,CAAC;IATQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;QACtE,MAAM,WAAW,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;IAC3E,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACzE,MAAM,WAAW,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;IACxE,CAAC;CACF;AAZD,0DAYC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1715797111306 implements MigrationInterface {\r\n  name = 'Migrations1715797111306';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(`ALTER TABLE \"owner\" ADD \"tax_rate\" numeric`);\r\n    await queryRunner.query(`ALTER TABLE \"business\" ADD \"tax_rate\" numeric`);\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(`ALTER TABLE \"business\" DROP COLUMN \"tax_rate\"`);\r\n    await queryRunner.query(`ALTER TABLE \"owner\" DROP COLUMN \"tax_rate\"`);\r\n  }\r\n}\r\n"]}