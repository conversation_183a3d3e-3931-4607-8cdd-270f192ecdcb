"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1713796253492 = void 0;
class Migrations1713796253492 {
    constructor() {
        this.name = 'Migrations1713796253492';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "owner" ADD "total_invest" numeric`);
        await queryRunner.query(`ALTER TABLE "owner" ADD "yield" numeric`);
        await queryRunner.query(`ALTER TABLE "owner" ADD "start_contract" date`);
        await queryRunner.query(`ALTER TABLE "owner" ADD "end_contract" date`);
        await queryRunner.query(`ALTER TABLE "business" ADD "total_invest" numeric`);
        await queryRunner.query(`ALTER TABLE "business" ADD "yield" numeric`);
        await queryRunner.query(`ALTER TABLE "business" ADD "start_contract" date`);
        await queryRunner.query(`ALTER TABLE "business" ADD "end_contract" date`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "business" DROP COLUMN "end_contract"`);
        await queryRunner.query(`ALTER TABLE "business" DROP COLUMN "start_contract"`);
        await queryRunner.query(`ALTER TABLE "business" DROP COLUMN "yield"`);
        await queryRunner.query(`ALTER TABLE "business" DROP COLUMN "total_invest"`);
        await queryRunner.query(`ALTER TABLE "owner" DROP COLUMN "end_contract"`);
        await queryRunner.query(`ALTER TABLE "owner" DROP COLUMN "start_contract"`);
        await queryRunner.query(`ALTER TABLE "owner" DROP COLUMN "yield"`);
        await queryRunner.query(`ALTER TABLE "owner" DROP COLUMN "total_invest"`);
    }
}
exports.Migrations1713796253492 = Migrations1713796253492;
//# sourceMappingURL=1713796253492-migrations.js.map