"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClientContractService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const addendum_entity_1 = require("../../../shared/database/typeorm/entities/addendum.entity");
const contract_entity_1 = require("../../../shared/database/typeorm/entities/contract.entity");
const owner_role_relation_entity_1 = require("../../../shared/database/typeorm/entities/owner-role-relation.entity");
const contract_status_enum_1 = require("../../../shared/enums/contract-status.enum");
const typeorm_2 = require("typeorm");
let ClientContractService = class ClientContractService {
    constructor(contractRepository, ownerRoleRelationRepository, formatDate) {
        this.contractRepository = contractRepository;
        this.ownerRoleRelationRepository = ownerRoleRelationRepository;
        this.formatDate = formatDate;
    }
    async getContractsByUserId(userId) {
        const userProfile = await this.ownerRoleRelationRepository.findOne({
            where: [{ ownerId: (0, typeorm_2.Equal)(userId) }, { businessId: (0, typeorm_2.Equal)(userId) }],
            relations: {
                owner: true,
                business: true,
            },
        });
        const contracts = await this.contractRepository.find({
            where: {
                investor: { id: userProfile.id },
                status: contract_status_enum_1.ContractStatusEnum.ACTIVE,
            },
            relations: {
                signataries: true,
                addendum: { addendumFiles: { file: true } },
            },
            order: {
                createdAt: 'ASC',
            },
        });
        if (!contracts || contracts.length === 0) {
            return [];
        }
        return contracts.map((contract) => {
            const investmentValue = contract.signataries[0]?.investmentValue || 0;
            const addendums = contract.addendum
                .filter((addendum) => addendum.status === addendum_entity_1.AddendumStatus.FULLY_SIGNED)
                .filter((addendum) => addendum.addendumFiles && addendum.addendumFiles.length > 0)
                .map((addendum) => ({
                id: addendum.id,
                type: 'additive',
                date: this.formatDate(addendum.applicationDate),
                invested: addendum.value,
                contract: addendum.addendumFiles[0]?.file?.url || '',
            }));
            return {
                id: contract.id,
                type: 'starter',
                date: this.formatDate(contract.startContract),
                invested: investmentValue,
                contract: contract.contractPdf || '',
                addendums: addendums.length > 0 ? addendums : undefined,
            };
        });
    }
    async getContractAddendums(contractId) {
        const contract = await this.contractRepository.findOne({
            where: {
                id: contractId,
                status: contract_status_enum_1.ContractStatusEnum.ACTIVE,
            },
            relations: {
                addendum: {
                    addendumFiles: {
                        file: true,
                    },
                },
            },
        });
        if (!contract) {
            throw new common_1.NotFoundException('Contrato não encontrado');
        }
        return contract.addendum
            .filter((addendum) => addendum.status === addendum_entity_1.AddendumStatus.FULLY_SIGNED)
            .filter((addendum) => addendum.addendumFiles && addendum.addendumFiles.length > 0)
            .map((addendum) => ({
            id: addendum.id,
            type: 'additive',
            date: this.formatDate(addendum.applicationDate),
            invested: addendum.value,
            contract: addendum.addendumFiles[0]?.file?.url || '',
        }));
    }
};
exports.ClientContractService = ClientContractService;
exports.ClientContractService = ClientContractService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(contract_entity_1.ContractEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(owner_role_relation_entity_1.OwnerRoleRelationEntity)),
    __param(2, (0, common_1.Inject)('FORMAT_DATE')),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository, Function])
], ClientContractService);
//# sourceMappingURL=client-contract.service.js.map