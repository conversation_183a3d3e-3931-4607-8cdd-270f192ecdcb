"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1708061373637 = void 0;
class Migrations1708061373637 {
    constructor() {
        this.name = 'Migrations1708061373637';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "owner_role_relation" ADD "business_id" uuid`);
        await queryRunner.query(`ALTER TABLE "business" ADD "password" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "business" ADD "temporary_password" boolean NOT NULL DEFAULT true`);
        await queryRunner.query(`ALTER TABLE "business" ADD "avatar" character varying`);
        await queryRunner.query(`ALTER TABLE "owner_role_relation" ADD CONSTRAINT "FK_469c060f976a087f2668fec5d04" FOREIGN KEY ("business_id") REFERENCES "business"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "owner_role_relation" DROP CONSTRAINT "FK_469c060f976a087f2668fec5d04"`);
        await queryRunner.query(`ALTER TABLE "business" DROP COLUMN "avatar"`);
        await queryRunner.query(`ALTER TABLE "business" DROP COLUMN "temporary_password"`);
        await queryRunner.query(`ALTER TABLE "business" DROP COLUMN "password"`);
        await queryRunner.query(`ALTER TABLE "owner_role_relation" DROP COLUMN "business_id"`);
    }
}
exports.Migrations1708061373637 = Migrations1708061373637;
//# sourceMappingURL=1708061373637-migrations.js.map