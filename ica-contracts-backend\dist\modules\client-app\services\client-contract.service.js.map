{"version": 3, "file": "client-contract.service.js", "sourceRoot": "/", "sources": ["modules/client-app/services/client-contract.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAuE;AACvE,6CAAmD;AACnD,+FAAsF;AACtF,+FAAsF;AACtF,qHAA0G;AAC1G,qFAA2E;AAC3E,qCAA4C;AAQrC,IAAM,qBAAqB,GAA3B,MAAM,qBAAqB;IAChC,YAEmB,kBAA8C,EAE9C,2BAAgE,EAEhE,UAA2C;QAJ3C,uBAAkB,GAAlB,kBAAkB,CAA4B;QAE9C,gCAA2B,GAA3B,2BAA2B,CAAqC;QAEhE,eAAU,GAAV,UAAU,CAAiC;IAC3D,CAAC;IAEJ,KAAK,CAAC,oBAAoB,CACxB,MAAc;QAEd,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC;YACjE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,IAAA,eAAK,EAAC,MAAM,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,IAAA,eAAK,EAAC,MAAM,CAAC,EAAE,CAAC;YAClE,SAAS,EAAE;gBACT,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;SACF,CAAC,CAAC;QAEH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;YACnD,KAAK,EAAE;gBACL,QAAQ,EAAE,EAAE,EAAE,EAAE,WAAW,CAAC,EAAE,EAAE;gBAChC,MAAM,EAAE,yCAAkB,CAAC,MAAM;aAClC;YACD,SAAS,EAAE;gBACT,WAAW,EAAE,IAAI;gBACjB,QAAQ,EAAE,EAAE,aAAa,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE;aAC5C;YACD,KAAK,EAAE;gBACL,SAAS,EAAE,KAAK;aACjB;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzC,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE;YAChC,MAAM,eAAe,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,eAAe,IAAI,CAAC,CAAC;YAEtE,MAAM,SAAS,GAAsC,QAAQ,CAAC,QAAQ;iBACnE,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,MAAM,KAAK,gCAAc,CAAC,YAAY,CAAC;iBACrE,MAAM,CACL,CAAC,QAAQ,EAAE,EAAE,CACX,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAC9D;iBACA,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;gBAClB,EAAE,EAAE,QAAQ,CAAC,EAAE;gBACf,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,eAAe,CAAC;gBAC/C,QAAQ,EAAE,QAAQ,CAAC,KAAK;gBACxB,QAAQ,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,GAAG,IAAI,EAAE;aACrD,CAAC,CAAC,CAAC;YAEN,OAAO;gBACL,EAAE,EAAE,QAAQ,CAAC,EAAE;gBACf,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,aAAa,CAAC;gBAC7C,QAAQ,EAAE,eAAe;gBACzB,QAAQ,EAAE,QAAQ,CAAC,WAAW,IAAI,EAAE;gBACpC,SAAS,EAAE,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;aACxD,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,oBAAoB,CACxB,UAAkB;QAElB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;YACrD,KAAK,EAAE;gBACL,EAAE,EAAE,UAAU;gBACd,MAAM,EAAE,yCAAkB,CAAC,MAAM;aAClC;YACD,SAAS,EAAE;gBACT,QAAQ,EAAE;oBACR,aAAa,EAAE;wBACb,IAAI,EAAE,IAAI;qBACX;iBACF;aACF;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,0BAAiB,CAAC,yBAAyB,CAAC,CAAC;QACzD,CAAC;QAED,OAAO,QAAQ,CAAC,QAAQ;aACrB,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,MAAM,KAAK,gCAAc,CAAC,YAAY,CAAC;aACrE,MAAM,CACL,CAAC,QAAQ,EAAE,EAAE,CACX,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAC9D;aACA,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;YAClB,EAAE,EAAE,QAAQ,CAAC,EAAE;YACf,IAAI,EAAE,UAAU;YAChB,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,eAAe,CAAC;YAC/C,QAAQ,EAAE,QAAQ,CAAC,KAAK;YACxB,QAAQ,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,GAAG,IAAI,EAAE;SACrD,CAAC,CAAC,CAAC;IACR,CAAC;CACF,CAAA;AAtGY,sDAAqB;gCAArB,qBAAqB;IADjC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,gCAAc,CAAC,CAAA;IAEhC,WAAA,IAAA,0BAAgB,EAAC,oDAAuB,CAAC,CAAA;IAEzC,WAAA,IAAA,eAAM,EAAC,aAAa,CAAC,CAAA;qCAHe,oBAAU;QAED,oBAAU;GAL/C,qBAAqB,CAsGjC", "sourcesContent": ["import { Inject, Injectable, NotFoundException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { AddendumStatus } from 'src/shared/database/typeorm/entities/addendum.entity';\r\nimport { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';\r\nimport { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';\r\nimport { ContractStatusEnum } from 'src/shared/enums/contract-status.enum';\r\nimport { Equal, Repository } from 'typeorm';\r\n\r\nimport {\r\n  IClientContractResponse,\r\n  IClientContractAddendumResponse,\r\n} from '../responses/client-contract.response';\r\n\r\n@Injectable()\r\nexport class ClientContractService {\r\n  constructor(\r\n    @InjectRepository(ContractEntity)\r\n    private readonly contractRepository: Repository<ContractEntity>,\r\n    @InjectRepository(OwnerRoleRelationEntity)\r\n    private readonly ownerRoleRelationRepository: Repository<OwnerRoleRelationEntity>,\r\n    @Inject('FORMAT_DATE')\r\n    private readonly formatDate: (date: Date | string) => string,\r\n  ) {}\r\n\r\n  async getContractsByUserId(\r\n    userId: string,\r\n  ): Promise<IClientContractResponse[]> {\r\n    const userProfile = await this.ownerRoleRelationRepository.findOne({\r\n      where: [{ ownerId: Equal(userId) }, { businessId: Equal(userId) }],\r\n      relations: {\r\n        owner: true,\r\n        business: true,\r\n      },\r\n    });\r\n\r\n    const contracts = await this.contractRepository.find({\r\n      where: {\r\n        investor: { id: userProfile.id },\r\n        status: ContractStatusEnum.ACTIVE, // Only get contracts with ACTIVE status\r\n      },\r\n      relations: {\r\n        signataries: true,\r\n        addendum: { addendumFiles: { file: true } },\r\n      },\r\n      order: {\r\n        createdAt: 'ASC',\r\n      },\r\n    });\r\n\r\n    if (!contracts || contracts.length === 0) {\r\n      return [];\r\n    }\r\n\r\n    return contracts.map((contract) => {\r\n      const investmentValue = contract.signataries[0]?.investmentValue || 0;\r\n\r\n      const addendums: IClientContractAddendumResponse[] = contract.addendum\r\n        .filter((addendum) => addendum.status === AddendumStatus.FULLY_SIGNED) // Only include addendums with FULLY_SIGNED status\r\n        .filter(\r\n          (addendum) =>\r\n            addendum.addendumFiles && addendum.addendumFiles.length > 0,\r\n        ) // Only include addendums with files\r\n        .map((addendum) => ({\r\n          id: addendum.id,\r\n          type: 'additive',\r\n          date: this.formatDate(addendum.applicationDate),\r\n          invested: addendum.value,\r\n          contract: addendum.addendumFiles[0]?.file?.url || '',\r\n        }));\r\n\r\n      return {\r\n        id: contract.id,\r\n        type: 'starter',\r\n        date: this.formatDate(contract.startContract),\r\n        invested: investmentValue,\r\n        contract: contract.contractPdf || '',\r\n        addendums: addendums.length > 0 ? addendums : undefined,\r\n      };\r\n    });\r\n  }\r\n\r\n  async getContractAddendums(\r\n    contractId: string,\r\n  ): Promise<IClientContractAddendumResponse[]> {\r\n    const contract = await this.contractRepository.findOne({\r\n      where: {\r\n        id: contractId,\r\n        status: ContractStatusEnum.ACTIVE, // Only get contracts with ACTIVE status\r\n      },\r\n      relations: {\r\n        addendum: {\r\n          addendumFiles: {\r\n            file: true,\r\n          },\r\n        },\r\n      },\r\n    });\r\n\r\n    if (!contract) {\r\n      throw new NotFoundException('Contrato não encontrado');\r\n    }\r\n\r\n    return contract.addendum\r\n      .filter((addendum) => addendum.status === AddendumStatus.FULLY_SIGNED) // Only include addendums with FULLY_SIGNED status\r\n      .filter(\r\n        (addendum) =>\r\n          addendum.addendumFiles && addendum.addendumFiles.length > 0,\r\n      ) // Only include addendums with files\r\n      .map((addendum) => ({\r\n        id: addendum.id,\r\n        type: 'additive',\r\n        date: this.formatDate(addendum.applicationDate),\r\n        invested: addendum.value,\r\n        contract: addendum.addendumFiles[0]?.file?.url || '',\r\n      }));\r\n  }\r\n}\r\n"]}