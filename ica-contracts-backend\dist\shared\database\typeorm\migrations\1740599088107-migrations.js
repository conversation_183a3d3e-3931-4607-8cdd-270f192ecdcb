"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1740599088107 = void 0;
class Migrations1740599088107 {
    constructor() {
        this.name = 'Migrations1740599088107';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "notification" ADD "addendum_id" integer`);
        await queryRunner.query(`ALTER TABLE "notification" ADD CONSTRAINT "FK_3eeec94e4bf39770577b3b20ca8" FOREIGN KEY ("addendum_id") REFERENCES "addendum"("id_addendum") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "notification" DROP CONSTRAINT "FK_3eeec94e4bf39770577b3b20ca8"`);
        await queryRunner.query(`ALTER TABLE "notification" DROP COLUMN "addendum_id"`);
    }
}
exports.Migrations1740599088107 = Migrations1740599088107;
//# sourceMappingURL=1740599088107-migrations.js.map