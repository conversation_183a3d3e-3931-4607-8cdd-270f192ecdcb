{"version": 3, "file": "1706729919891-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1706729919891-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IA+BnC,CAAC;IA7BQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,wDAAwD,CACzD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,8DAA8D,CAC/D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,2DAA2D,CAC5D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,sDAAsD,CACvD,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,qDAAqD,CACtD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,0DAA0D,CAC3D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,6DAA6D,CAC9D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,uDAAuD,CACxD,CAAC;IACJ,CAAC;CACF;AAhCD,0DAgCC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1706729919891 implements MigrationInterface {\r\n  name = 'Migrations1706729919891';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner\" ALTER COLUMN \"phone\" DROP NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner\" ALTER COLUMN \"mother_name\" DROP NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner\" ALTER COLUMN \"dt_birth\" DROP NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner\" ALTER COLUMN \"pep\" DROP NOT NULL`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner\" ALTER COLUMN \"pep\" SET NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner\" ALTER COLUMN \"dt_birth\" SET NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner\" ALTER COLUMN \"mother_name\" SET NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner\" ALTER COLUMN \"phone\" SET NOT NULL`,\r\n    );\r\n  }\r\n}\r\n"]}