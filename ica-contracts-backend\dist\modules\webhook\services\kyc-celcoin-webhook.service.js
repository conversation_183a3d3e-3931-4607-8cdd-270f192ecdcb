"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KycCelcoinWebhookService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const bcrypt = require("bcrypt");
const account_entity_1 = require("../../../shared/database/typeorm/entities/account.entity");
const business_entity_1 = require("../../../shared/database/typeorm/entities/business.entity");
const owner_entity_1 = require("../../../shared/database/typeorm/entities/owner.entity");
const email_service_1 = require("../../../shared/email/email.service");
const account_status_enum_1 = require("../../../shared/enums/account-status.enum");
const generate_password_1 = require("../../../shared/functions/generate-password");
const typeorm_2 = require("typeorm");
let KycCelcoinWebhookService = class KycCelcoinWebhookService {
    constructor(accountDb, ownerDb, businessDb, emailService) {
        this.accountDb = accountDb;
        this.ownerDb = ownerDb;
        this.businessDb = businessDb;
        this.emailService = emailService;
    }
    async perform(input) {
        if (input.status === 'REJECTED') {
            await this.accountDb.update({
                externalId: input.body.onboardingId,
            }, {
                status: account_status_enum_1.AccountStatusEnum.REPROVED,
            });
        }
        if (input.status === 'APPROVED') {
            const account = await this.accountDb.findOne({
                relations: {
                    owner: true,
                    business: true,
                },
                where: {
                    externalId: input.body.onboardingId,
                },
            });
            console.log('KYC aprovado para conta', account);
            const password = (0, generate_password_1.generatePassword)();
            const passwordEncrypted = await bcrypt.hash(password, 10);
            try {
                await this.emailService.sendEmail({
                    to: account.owner?.email || account.business.email,
                    subject: 'bem-vindo',
                    template: 'welcome',
                    context: {
                        document: account.owner?.cpf || account.business.cnpj,
                        password,
                        username: account.owner?.name || account.business.companyName,
                    },
                });
                console.log('Enviado e-mail com sucesso para: ', account.owner?.email || account.business.email);
            }
            catch (error) {
                console.log(`Erro ao enviar email para: ${account.owner?.email || account.business.email}`, error);
            }
            if (account.type === 'physical') {
                await this.ownerDb.update(account.ownerId, {
                    password: passwordEncrypted,
                    temporaryPassword: true,
                });
            }
            else {
                await this.businessDb.update(account.businessId, {
                    password: passwordEncrypted,
                    temporaryPassword: true,
                });
            }
            await this.accountDb.update({
                externalId: input.body.onboardingId,
            }, {
                status: account_status_enum_1.AccountStatusEnum.ACTIVE,
            });
        }
    }
};
exports.KycCelcoinWebhookService = KycCelcoinWebhookService;
exports.KycCelcoinWebhookService = KycCelcoinWebhookService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(account_entity_1.AccountEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(owner_entity_1.OwnerEntity)),
    __param(2, (0, typeorm_1.InjectRepository)(business_entity_1.BusinessEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        email_service_1.EmailService])
], KycCelcoinWebhookService);
//# sourceMappingURL=kyc-celcoin-webhook.service.js.map