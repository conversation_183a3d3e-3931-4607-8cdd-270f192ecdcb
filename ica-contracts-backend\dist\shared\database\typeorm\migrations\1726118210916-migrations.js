"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1726118210916 = void 0;
class Migrations1726118210916 {
    constructor() {
        this.name = 'Migrations1726118210916';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "pre_register" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying NOT NULL, "rg" character varying NOT NULL, "document" character varying NOT NULL, "phone_number" character varying NOT NULL, "dt_birth" date NOT NULL, "email" character varying NOT NULL, "zip_code" character varying NOT NULL, "neighborhood" character varying NOT NULL, "city" character varying NOT NULL, "address_complement" character varying, "address_number" character varying NOT NULL, "investment_value" numeric NOT NULL, "investment_term" character varying NOT NULL, "investment_yield" numeric NOT NULL, "investment_modality" character varying NOT NULL, "observations" character varying NOT NULL, "status" character varying NOT NULL, "adviser_id" uuid NOT NULL, "purchase_with" character varying NOT NULL, "amount_quotes" integer NOT NULL, "grace_period" date NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "contract_id" uuid, CONSTRAINT "PK_da786d4c6f01e1cbb0be67607a6" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "pre_register" ADD CONSTRAINT "FK_db44bd7010cefe5ae4a9bb5506f" FOREIGN KEY ("adviser_id") REFERENCES "owner_role_relation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pre_register" ADD CONSTRAINT "FK_2a142caa100d41db710a3f56734" FOREIGN KEY ("contract_id") REFERENCES "contract"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "pre_register" DROP CONSTRAINT "FK_2a142caa100d41db710a3f56734"`);
        await queryRunner.query(`ALTER TABLE "pre_register" DROP CONSTRAINT "FK_db44bd7010cefe5ae4a9bb5506f"`);
        await queryRunner.query(`DROP TABLE "pre_register"`);
    }
}
exports.Migrations1726118210916 = Migrations1726118210916;
//# sourceMappingURL=1726118210916-migrations.js.map