{"version": 3, "file": "1712343906261-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1712343906261-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IA6BnC,CAAC;IA3BQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,wEAAwE,CACzE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,yDAAyD,CAC1D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,+DAA+D,CAChE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,qFAAqF,CACtF,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC;QAC1E,MAAM,WAAW,CAAC,KAAK,CACrB,qDAAqD,CACtD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,uDAAuD,CACxD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,8DAA8D,CAC/D,CAAC;IACJ,CAAC;CACF;AA9BD,0DA8BC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1712343906261 implements MigrationInterface {\r\n  name = 'Migrations1712343906261';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"transaction\" ADD \"destiny_account_type\" character varying`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"transaction\" ADD \"transfer_date\" TIMESTAMP`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"transaction\" ADD \"destiny_key\" character varying`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"transaction\" ADD \"status\" character varying NOT NULL DEFAULT 'PENDENT'`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(`ALTER TABLE \"transaction\" DROP COLUMN \"status\"`);\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"transaction\" DROP COLUMN \"destiny_key\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"transaction\" DROP COLUMN \"transfer_date\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"transaction\" DROP COLUMN \"destiny_account_type\"`,\r\n    );\r\n  }\r\n}\r\n"]}