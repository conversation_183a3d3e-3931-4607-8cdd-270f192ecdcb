import { CacheModule } from '@nestjs/cache-manager';
import { Global, Module } from '@nestjs/common';

import { CreateExistingContractApiService } from './services/create-existing-contract.service';
import { CreateNewContractService } from './services/create-new-contract.service';
import { DeleteContractApiService } from './services/delete-contract.service';
import { DetailsReportInvestorService } from './services/details-report-investor.service';
import { GenerateReportInvestorsService } from './services/generate-report-investors.service';
import { RequestInvestorCredentialsService } from './services/request-investor-credentials.service';
import { ResubmitRejectedContractApiService } from './services/resubmit-rejected-contract.service';
@Global()
@Module({
  imports: [CacheModule.register()],
  providers: [
    GenerateReportInvestorsService,
    DetailsReportInvestorService,
    CreateNewContractService,
    CreateExistingContractApiService,
    ResubmitRejectedContractApiService,
    RequestInvestorCredentialsService,
    DeleteContractApiService,
  ],
  exports: [
    GenerateReportInvestorsService,
    DetailsReportInvestorService,
    CreateNewContractService,
    CreateExistingContractApiService,
    ResubmitRejectedContractApiService,
    RequestInvestorCredentialsService,
    DeleteContractApiService,
  ],
})
export class IcaContractServiceModule {}
