"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1708538218473 = void 0;
class Migrations1708538218473 {
    constructor() {
        this.name = 'Migrations1708538218473';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "owner_role_relation" DROP CONSTRAINT "FK_469c060f976a087f2668fec5d04"`);
        await queryRunner.query(`CREATE TABLE "otp" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "code" character varying NOT NULL, "expires_at" TIMESTAMP NOT NULL, "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "owner_id" uuid, CONSTRAINT "PK_32556d9d7b22031d7d0e1fd6723" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "owner_role_relation" DROP COLUMN "business_id"`);
        await queryRunner.query(`ALTER TABLE "business" DROP COLUMN "temporary_password"`);
        await queryRunner.query(`ALTER TABLE "business" DROP COLUMN "password"`);
        await queryRunner.query(`ALTER TABLE "business" DROP COLUMN "avatar"`);
        await queryRunner.query(`ALTER TABLE "otp" ADD CONSTRAINT "FK_32edb1f3da9f9b7cc25fd91d56f" FOREIGN KEY ("owner_id") REFERENCES "owner"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "otp" DROP CONSTRAINT "FK_32edb1f3da9f9b7cc25fd91d56f"`);
        await queryRunner.query(`ALTER TABLE "business" ADD "avatar" character varying`);
        await queryRunner.query(`ALTER TABLE "business" ADD "password" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "business" ADD "temporary_password" boolean NOT NULL DEFAULT true`);
        await queryRunner.query(`ALTER TABLE "owner_role_relation" ADD "business_id" uuid`);
        await queryRunner.query(`DROP TABLE "otp"`);
        await queryRunner.query(`ALTER TABLE "owner_role_relation" ADD CONSTRAINT "FK_469c060f976a087f2668fec5d04" FOREIGN KEY ("business_id") REFERENCES "business"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
}
exports.Migrations1708538218473 = Migrations1708538218473;
//# sourceMappingURL=1708538218473-migrations.js.map