{"version": 3, "file": "1729695196216-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1729695196216-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAuBnC,CAAC;IArBQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,yDAAyD,CAC1D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,2DAA2D,CAC5D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,4DAA4D,CAC7D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wDAAwD,CACzD,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAC;QACxE,MAAM,WAAW,CAAC,KAAK,CAAC,kDAAkD,CAAC,CAAC;QAC5E,MAAM,WAAW,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAC;QAC3E,MAAM,WAAW,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;IAC3E,CAAC;CACF;AAxBD,0DAwBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1729695196216 implements MigrationInterface {\r\n  name = 'Migrations1729695196216';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pre_register\" ADD \"bank\" character varying`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pre_register\" ADD \"agency\" character varying`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pre_register\" ADD \"account\" character varying`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pre_register\" ADD \"pix\" character varying`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(`ALTER TABLE \"pre_register\" DROP COLUMN \"pix\"`);\r\n    await queryRunner.query(`ALTER TABLE \"pre_register\" DROP COLUMN \"account\"`);\r\n    await queryRunner.query(`ALTER TABLE \"pre_register\" DROP COLUMN \"agency\"`);\r\n    await queryRunner.query(`ALTER TABLE \"pre_register\" DROP COLUMN \"bank\"`);\r\n  }\r\n}\r\n"]}