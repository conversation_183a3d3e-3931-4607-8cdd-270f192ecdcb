"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mailerOptions = void 0;
const handlebars_adapter_1 = require("@nestjs-modules/mailer/dist/adapters/handlebars.adapter");
const aws_sdk_1 = require("aws-sdk");
const dotenv = require("dotenv");
const path_1 = require("path");
dotenv.config();
exports.mailerOptions = {
    transport: {
        SES: new aws_sdk_1.SES({
            region: 'us-east-1',
            credentials: {
                accessKeyId: process.env.AWS_ACCESS_KEY,
                secretAccessKey: process.env.AWS_SECRET_KEY,
            },
        }),
    },
    defaults: {
        from: process.env.EMAIL_ADDRESS,
    },
    template: {
        dir: (0, path_1.join)(__dirname, 'templates'),
        adapter: new handlebars_adapter_1.HandlebarsAdapter(),
        options: {
            strict: true,
        },
    },
};
//# sourceMappingURL=email.config.js.map