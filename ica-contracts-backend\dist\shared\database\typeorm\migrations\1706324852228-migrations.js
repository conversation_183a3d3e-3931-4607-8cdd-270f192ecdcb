"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1706324852228 = void 0;
class Migrations1706324852228 {
    constructor() {
        this.name = 'Migrations1706324852228';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "device_entity" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "owner_id" uuid, "name" character varying NOT NULL, "model" character varying NOT NULL, "code" character varying NOT NULL, "token" character varying NOT NULL, "latitude" character varying NOT NULL, "longitude" character varying NOT NULL, "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, CONSTRAINT "PK_a75e1d635b3b07412a2ab3eb000" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "permission" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "owner_id" uuid, "business_id" uuid, "owner_role_id" uuid NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, CONSTRAINT "PK_3b8b97af9d9d8807e41e6f48362" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "role" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying NOT NULL, CONSTRAINT "PK_b36bcfe02fc8de3c57a8b2391c2" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "owner_role_relation" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "owner_id" uuid NOT NULL, "role_id" uuid NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, CONSTRAINT "PK_e39ae65ffce1e6831360fd87c6d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "pix-key" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "account_id" uuid NOT NULL, "type_key" character varying NOT NULL, "key" character varying NOT NULL, "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, CONSTRAINT "PK_97b3a249aca9b0fd61ae0d877ee" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "device_entity" ADD CONSTRAINT "FK_8ecb72968cbdf87be784fe6cf1d" FOREIGN KEY ("owner_id") REFERENCES "owner"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "permission" ADD CONSTRAINT "FK_3c2f72152c8b1d79c0879c20506" FOREIGN KEY ("owner_id") REFERENCES "owner"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "permission" ADD CONSTRAINT "FK_80c78d5396e006f3a2ae7800c12" FOREIGN KEY ("business_id") REFERENCES "business"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "permission" ADD CONSTRAINT "FK_f697f57ebfc670aa2f6d82ded7c" FOREIGN KEY ("owner_role_id") REFERENCES "owner_role_relation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "owner_role_relation" ADD CONSTRAINT "FK_622c8e2dabdd04a18f9bc656570" FOREIGN KEY ("owner_id") REFERENCES "owner"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "owner_role_relation" ADD CONSTRAINT "FK_235ec492b335d8e8883f4997a70" FOREIGN KEY ("role_id") REFERENCES "role"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pix-key" ADD CONSTRAINT "FK_28c2c42359e273bc3552c8879b5" FOREIGN KEY ("account_id") REFERENCES "account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "pix-key" DROP CONSTRAINT "FK_28c2c42359e273bc3552c8879b5"`);
        await queryRunner.query(`ALTER TABLE "owner_role_relation" DROP CONSTRAINT "FK_235ec492b335d8e8883f4997a70"`);
        await queryRunner.query(`ALTER TABLE "owner_role_relation" DROP CONSTRAINT "FK_622c8e2dabdd04a18f9bc656570"`);
        await queryRunner.query(`ALTER TABLE "permission" DROP CONSTRAINT "FK_f697f57ebfc670aa2f6d82ded7c"`);
        await queryRunner.query(`ALTER TABLE "permission" DROP CONSTRAINT "FK_80c78d5396e006f3a2ae7800c12"`);
        await queryRunner.query(`ALTER TABLE "permission" DROP CONSTRAINT "FK_3c2f72152c8b1d79c0879c20506"`);
        await queryRunner.query(`ALTER TABLE "device_entity" DROP CONSTRAINT "FK_8ecb72968cbdf87be784fe6cf1d"`);
        await queryRunner.query(`DROP TABLE "pix-key"`);
        await queryRunner.query(`DROP TABLE "owner_role_relation"`);
        await queryRunner.query(`DROP TABLE "role"`);
        await queryRunner.query(`DROP TABLE "permission"`);
        await queryRunner.query(`DROP TABLE "device_entity"`);
    }
}
exports.Migrations1706324852228 = Migrations1706324852228;
//# sourceMappingURL=1706324852228-migrations.js.map