{"version": 3, "file": "webhook-celcoin.controller.js", "sourceRoot": "/", "sources": ["modules/webhook/controllers/webhook-celcoin.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAyE;AAEzE,sFAA+E;AAC/E,gGAA0F;AAC1F,sFAAgF;AAChF,oFAA6E;AAC7E,yGAAmG;AACnG,yGAAkG;AAClG,6GAAsG;AACtG,qGAA8F;AAC9F,iGAA2F;AAC3F,+GAAwG;AACxG,yFAAmF;AACnF,yGAAkG;AAClG,yHAAiH;AACjH,yHAAiH;AACjH,yHAAiH;AACjH,+GAAuG;AACvG,qHAA6G;AAC7G,mGAA6F;AAC7F,+FAAyF;AAGlF,IAAM,wBAAwB,GAA9B,MAAM,wBAAwB;IACnC,YACmB,+BAAgE,EAChE,qBAAkD,EAClD,iCAAoE,EACpE,gCAAkE,EAClE,oBAAwD,EACxD,iBAAkD,EAClD,6BAA4D,EAC5D,iCAAoE,EACpE,wBAAkD,EAClD,6BAA4D,EAC5D,4BAA0D,EAC1D,oCAA0E,EAC1E,sCAA8E,EAC9E,sCAA8E,EAC9E,sCAA8E;QAd9E,oCAA+B,GAA/B,+BAA+B,CAAiC;QAChE,0BAAqB,GAArB,qBAAqB,CAA6B;QAClD,sCAAiC,GAAjC,iCAAiC,CAAmC;QACpE,qCAAgC,GAAhC,gCAAgC,CAAkC;QAClE,yBAAoB,GAApB,oBAAoB,CAAoC;QACxD,sBAAiB,GAAjB,iBAAiB,CAAiC;QAClD,kCAA6B,GAA7B,6BAA6B,CAA+B;QAC5D,sCAAiC,GAAjC,iCAAiC,CAAmC;QACpE,6BAAwB,GAAxB,wBAAwB,CAA0B;QAClD,kCAA6B,GAA7B,6BAA6B,CAA+B;QAC5D,iCAA4B,GAA5B,4BAA4B,CAA8B;QAC1D,yCAAoC,GAApC,oCAAoC,CAAsC;QAC1E,2CAAsC,GAAtC,sCAAsC,CAAwC;QAC9E,2CAAsC,GAAtC,sCAAsC,CAAwC;QAC9E,2CAAsC,GAAtC,sCAAsC,CAAwC;IAC9F,CAAC;IAGE,AAAN,KAAK,CAAC,eAAe,CAEnB,IAA+B;QAE/B,MAAM,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACzD,CAAC;IAGK,AAAN,KAAK,CAAC,eAAe,CAEnB,KAA+B;QAE/B,OAAO,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAC1D,CAAC;IAGK,AAAN,KAAK,CAAC,cAAc,CAElB,IAA6B;QAE7B,OAAO,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC;IAGK,AAAN,KAAK,CAAC,aAAa,CAAS,IAAoC;QAC9D,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAChD,CAAC;IAEK,AAAN,KAAK,CAAC,UAAU,CAAS,IAAS;QAChC,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAGK,AAAN,KAAK,CAAC,aAAa,CAAS,IAAS;QACnC,MAAM,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC3D,CAAC;IAGK,AAAN,KAAK,CAAC,SAAS,CAAS,IAAS;QAC/B,MAAM,IAAI,CAAC,iCAAiC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC7D,CAAC;IAGK,AAAN,KAAK,CAAC,YAAY,CAAS,IAAS;QAClC,MAAM,IAAI,CAAC,oCAAoC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAChE,CAAC;IAGK,AAAN,KAAK,CAAC,cAAc,CAAS,IAAS;QACpC,MAAM,IAAI,CAAC,sCAAsC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAClE,CAAC;IAGK,AAAN,KAAK,CAAC,cAAc,CAAS,IAAS;QACpC,MAAM,IAAI,CAAC,sCAAsC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAClE,CAAC;IAGK,AAAN,KAAK,CAAC,cAAc,CAAS,IAAS;QACpC,MAAM,IAAI,CAAC,sCAAsC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAClE,CAAC;IAGK,AAAN,KAAK,CAAC,WAAW,CAAS,IAAS;QACjC,MAAM,IAAI,CAAC,gCAAgC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC5D,CAAC;IAGK,AAAN,KAAK,CAAC,QAAQ,CAAS,IAAS;QAC9B,MAAM,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACzD,CAAC;IAGK,AAAN,KAAK,CAAC,YAAY,CAAS,IAAS;QAClC,MAAM,IAAI,CAAC,iCAAiC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC7D,CAAC;IAGK,AAAN,KAAK,CAAC,GAAG,CAAS,IAAS;QACzB,MAAM,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACpD,CAAC;CACF,CAAA;AArGY,4DAAwB;AAoB7B;IADL,IAAA,aAAI,EAAC,UAAU,CAAC;IAEd,WAAA,IAAA,aAAI,GAAE,CAAA;;qCACD,wDAAyB;;+DAGhC;AAGK;IADL,IAAA,YAAG,EAAC,SAAS,CAAC;IAEZ,WAAA,IAAA,cAAK,GAAE,CAAA;;qCACD,uDAAwB;;+DAGhC;AAGK;IADL,IAAA,YAAG,EAAC,QAAQ,CAAC;IAEX,WAAA,IAAA,aAAI,GAAE,CAAA;;qCACD,qDAAuB;;8DAG9B;AAGK;IADL,IAAA,aAAI,EAAC,QAAQ,CAAC;IACM,WAAA,IAAA,aAAI,GAAE,CAAA;;qCAAO,kEAA8B;;6DAE/D;AAEK;IADL,IAAA,aAAI,EAAC,aAAa,CAAC;IACF,WAAA,IAAA,aAAI,GAAE,CAAA;;;;0DAEvB;AAGK;IADL,IAAA,aAAI,EAAC,YAAY,CAAC;IACE,WAAA,IAAA,aAAI,GAAE,CAAA;;;;6DAE1B;AAGK;IADL,IAAA,aAAI,EAAC,oBAAoB,CAAC;IACV,WAAA,IAAA,aAAI,GAAE,CAAA;;;;yDAEtB;AAGK;IADL,IAAA,aAAI,EAAC,uBAAuB,CAAC;IACV,WAAA,IAAA,aAAI,GAAE,CAAA;;;;4DAEzB;AAGK;IADL,IAAA,aAAI,EAAC,yBAAyB,CAAC;IACV,WAAA,IAAA,aAAI,GAAE,CAAA;;;;8DAE3B;AAGK;IADL,IAAA,aAAI,EAAC,yBAAyB,CAAC;IACV,WAAA,IAAA,aAAI,GAAE,CAAA;;;;8DAE3B;AAGK;IADL,IAAA,aAAI,EAAC,yBAAyB,CAAC;IACV,WAAA,IAAA,aAAI,GAAE,CAAA;;;;8DAE3B;AAGK;IADL,IAAA,aAAI,EAAC,oBAAoB,CAAC;IACR,WAAA,IAAA,aAAI,GAAE,CAAA;;;;2DAExB;AAGK;IADL,IAAA,aAAI,EAAC,YAAY,CAAC;IACH,WAAA,IAAA,aAAI,GAAE,CAAA;;;;wDAErB;AAGK;IADL,IAAA,aAAI,EAAC,gBAAgB,CAAC;IACH,WAAA,IAAA,aAAI,GAAE,CAAA;;;;4DAEzB;AAGK;IADL,IAAA,aAAI,EAAC,MAAM,CAAC;IACF,WAAA,IAAA,aAAI,GAAE,CAAA;;;;mDAEhB;mCApGU,wBAAwB;IADpC,IAAA,mBAAU,EAAC,iBAAiB,CAAC;qCAGwB,qEAA+B;QACzC,4DAA2B;QACf,0EAAiC;QAClC,sEAAgC;QAC5C,2EAAkC;QACrC,qEAA+B;QACnB,iEAA6B;QACzB,yEAAiC;QAC1C,sDAAwB;QACnB,gEAA6B;QAC9B,8DAA4B;QACpB,gFAAoC;QAClC,oFAAsC;QACtC,oFAAsC;QACtC,oFAAsC;GAhBtF,wBAAwB,CAqGpC", "sourcesContent": ["import { Body, Controller, Get, Post, Put, Query } from '@nestjs/common';\r\n\r\nimport { ConsultCelcoinWebhookDto } from '../dto/consult-celcoin-webhooks.dto';\r\nimport { CreateAccountCelcoinWebhookDto } from '../dto/create-acount-celcoin-webhook.dto';\r\nimport { RegisterCelcoinWebhookDto } from '../dto/register-celcoin-webhook.dto';\r\nimport { UpdateCelcoinWebhookDto } from '../dto/update-celcoin-webhooks.dto';\r\nimport { BillPaymentCelcoinWebhookService } from '../services/billpayment-celcoin-webhook.service';\r\nimport { CelcoinPixPaymentWebhookService } from '../services/celcoin-pix-payment-webhook.service';\r\nimport { ChargeCreateCelcoinWebhookService } from '../services/charge-create-celcoin-webhook.service';\r\nimport { ChargeInCelcoinWebhookService } from '../services/charge-in-celcoin-webhook.service';\r\nimport { ConsultCelcoinWebhookService } from '../services/consult-celcoin-webhook.service';\r\nimport { CreateAccountCelcoinWebhookService } from '../services/create-account-celcoin-webhook.service';\r\nimport { KycCelcoinWebhookService } from '../services/kyc-celcoin-webhook.service';\r\nimport { PixCashoutCelcoinWebhookService } from '../services/pix-cashout-celcoin-webhook.service';\r\nimport { PixClaimCancelledCelcoinWebhookService } from '../services/pix-claim-cancelled-celcoin-webhook.service';\r\nimport { PixClaimCompletedCelcoinWebhookService } from '../services/pix-claim-completed-celcoin-webhook.service';\r\nimport { PixClaimConfirmedCelcoinWebhookService } from '../services/pix-claim-confirmed-celcoin-webhook.service';\r\nimport { PixClaimOpenCelcoinWebhookService } from '../services/pix-claim-open-celcoin-webhook.service';\r\nimport { PixClaimWaitingCelcoinWebhookService } from '../services/pix-claim-waiting-celcoin-webhook.service';\r\nimport { RegisterCelcoinWebhookService } from '../services/register-celcoin-webhook.service';\r\nimport { UpdateCelcoinWebhookService } from '../services/update-celcoin-webhook.service';\r\n\r\n@Controller('webhook/celcoin')\r\nexport class WebhookCelcoinController {\r\n  constructor(\r\n    private readonly celcoinPixPaymentWebhookService: CelcoinPixPaymentWebhookService,\r\n    private readonly updateWebhooksService: UpdateCelcoinWebhookService,\r\n    private readonly pixClaimOpenCelcoinWebhookService: PixClaimOpenCelcoinWebhookService,\r\n    private readonly billPaymentCelcoinWebhookService: BillPaymentCelcoinWebhookService,\r\n    private readonly createAccountService: CreateAccountCelcoinWebhookService,\r\n    private readonly pixCashoutService: PixCashoutCelcoinWebhookService,\r\n    private readonly chargeInCelcoinWebhookService: ChargeInCelcoinWebhookService,\r\n    private readonly chargeCreateCelcoinWebhookService: ChargeCreateCelcoinWebhookService,\r\n    private readonly kycCelcoinWebhookService: KycCelcoinWebhookService,\r\n    private readonly registerCelcoinWebhookService: RegisterCelcoinWebhookService,\r\n    private readonly consultCelcoinWebhookService: ConsultCelcoinWebhookService,\r\n    private readonly pixClaimWaitingCelcoinWebhookService: PixClaimWaitingCelcoinWebhookService,\r\n    private readonly pixClaimConfirmedCelcoinWebhookService: PixClaimConfirmedCelcoinWebhookService,\r\n    private readonly pixClaimCancelledCelcoinWebhookService: PixClaimCancelledCelcoinWebhookService,\r\n    private readonly pixClaimCompletedCelcoinWebhookService: PixClaimCompletedCelcoinWebhookService,\r\n  ) {}\r\n\r\n  @Post('register')\r\n  async registerWebhook(\r\n    @Body()\r\n    body: RegisterCelcoinWebhookDto,\r\n  ) {\r\n    await this.registerCelcoinWebhookService.perform(body);\r\n  }\r\n\r\n  @Get('consult')\r\n  async consultWebhooks(\r\n    @Query()\r\n    query: ConsultCelcoinWebhookDto,\r\n  ) {\r\n    return this.consultCelcoinWebhookService.perform(query);\r\n  }\r\n\r\n  @Put('update')\r\n  async updateWebhooks(\r\n    @Body()\r\n    body: UpdateCelcoinWebhookDto,\r\n  ) {\r\n    return this.updateWebhooksService.perform(body);\r\n  }\r\n\r\n  @Post('create')\r\n  async createAccount(@Body() data: CreateAccountCelcoinWebhookDto) {\r\n    await this.createAccountService.perform(data);\r\n  }\r\n  @Post('pix/cashout')\r\n  async pixCashout(@Body() data: any) {\r\n    await this.pixCashoutService.perform(data);\r\n  }\r\n\r\n  @Post('pix/cashin')\r\n  async cashInWebhook(@Body() body: any) {\r\n    await this.celcoinPixPaymentWebhookService.perform(body);\r\n  }\r\n\r\n  @Post('pix-key/claim-open')\r\n  async claimOpen(@Body() body: any) {\r\n    await this.pixClaimOpenCelcoinWebhookService.perform(body);\r\n  }\r\n\r\n  @Post('pix-key/claim-waiting')\r\n  async claimWaiting(@Body() body: any) {\r\n    await this.pixClaimWaitingCelcoinWebhookService.perform(body);\r\n  }\r\n\r\n  @Post('pix-key/claim-confirmed')\r\n  async claimConfirmed(@Body() body: any) {\r\n    await this.pixClaimConfirmedCelcoinWebhookService.perform(body);\r\n  }\r\n\r\n  @Post('pix-key/claim-cancelled')\r\n  async claimCancelled(@Body() body: any) {\r\n    await this.pixClaimCancelledCelcoinWebhookService.perform(body);\r\n  }\r\n\r\n  @Post('pix-key/claim-completed')\r\n  async claimCompleted(@Body() body: any) {\r\n    await this.pixClaimCompletedCelcoinWebhookService.perform(body);\r\n  }\r\n\r\n  @Post('boleto/billpayment')\r\n  async billPayment(@Body() body: any) {\r\n    await this.billPaymentCelcoinWebhookService.perform(body);\r\n  }\r\n\r\n  @Post('/charge-in')\r\n  async chargeIn(@Body() body: any) {\r\n    await this.chargeInCelcoinWebhookService.perform(body);\r\n  }\r\n\r\n  @Post('/charge-create')\r\n  async chargeCreate(@Body() body: any) {\r\n    await this.chargeCreateCelcoinWebhookService.perform(body);\r\n  }\r\n\r\n  @Post('/kyc')\r\n  async kyc(@Body() body: any) {\r\n    await this.kycCelcoinWebhookService.perform(body);\r\n  }\r\n}\r\n"]}