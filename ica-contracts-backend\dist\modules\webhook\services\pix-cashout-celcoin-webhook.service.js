"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PixCashoutCelcoinWebhookService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const transaction_entity_1 = require("../../../shared/database/typeorm/entities/transaction.entity");
const transaction_status_enum_1 = require("../../../shared/enums/transaction-status-enum");
const typeorm_2 = require("typeorm");
let PixCashoutCelcoinWebhookService = class PixCashoutCelcoinWebhookService {
    constructor(transactionDb) {
        this.transactionDb = transactionDb;
    }
    async perform(data) {
        const transaction = await this.transactionDb.findOne({
            where: {
                code: data.body.clientCode,
            },
        });
        if (transaction) {
            if (data.status === 'CONFIRMED') {
                await this.transactionDb.update(transaction.id, {
                    status: transaction_status_enum_1.TransactionStatusEnum.DONE,
                });
            }
            if (data.status === 'ERROR') {
                await this.transactionDb.update(transaction.id, {
                    status: transaction_status_enum_1.TransactionStatusEnum.ERROR,
                });
            }
            if (data.status === 'PROCESSING') {
                await this.transactionDb.update(transaction.id, {
                    status: transaction_status_enum_1.TransactionStatusEnum.PENDENT,
                });
            }
        }
    }
};
exports.PixCashoutCelcoinWebhookService = PixCashoutCelcoinWebhookService;
exports.PixCashoutCelcoinWebhookService = PixCashoutCelcoinWebhookService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(transaction_entity_1.TransactionEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], PixCashoutCelcoinWebhookService);
//# sourceMappingURL=pix-cashout-celcoin-webhook.service.js.map