"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1731042896398 = void 0;
class Migrations1731042896398 {
    constructor() {
        this.name = 'Migrations1731042896398';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "uploads" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "type" character varying NOT NULL, "url" character varying NOT NULL, "owner_id" uuid, "business_id" uuid, "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_d1781d1eedd7459314f60f39bd3" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "uploads" ADD CONSTRAINT "FK_4dfa98b9e12204ea0f0f712f30c" FOREIGN KEY ("owner_id") REFERENCES "owner"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "uploads" ADD CONSTRAINT "FK_78d9afe9be70c9109d6824b4b58" FOREIGN KEY ("business_id") REFERENCES "business"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "uploads" DROP CONSTRAINT "FK_78d9afe9be70c9109d6824b4b58"`);
        await queryRunner.query(`ALTER TABLE "uploads" DROP CONSTRAINT "FK_4dfa98b9e12204ea0f0f712f30c"`);
        await queryRunner.query(`DROP TABLE "uploads"`);
    }
}
exports.Migrations1731042896398 = Migrations1731042896398;
//# sourceMappingURL=1731042896398-migrations.js.map