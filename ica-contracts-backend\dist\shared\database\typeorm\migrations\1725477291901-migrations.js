"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations************* = void 0;
class Migrations************* {
    constructor() {
        this.name = 'Migrations*************';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "account" ADD "bank" character varying`);
        await queryRunner.query(`ALTER TABLE "account" ADD "is_external" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" DROP CONSTRAINT "FK_51fc575749fb624f8debaab8c5c"`);
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" ADD CONSTRAINT "FK_51fc575749fb624f8debaab8c5c" FOREIGN KEY ("account_id") REFERENCES "account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" DROP CONSTRAINT "FK_51fc575749fb624f8debaab8c5c"`);
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" ADD CONSTRAINT "FK_51fc575749fb624f8debaab8c5c" FOREIGN KEY ("account_id") REFERENCES "account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "account" DROP COLUMN "is_external"`);
        await queryRunner.query(`ALTER TABLE "account" DROP COLUMN "bank"`);
    }
}
exports.Migrations************* = Migrations*************;
//# sourceMappingURL=*************-migrations.js.map