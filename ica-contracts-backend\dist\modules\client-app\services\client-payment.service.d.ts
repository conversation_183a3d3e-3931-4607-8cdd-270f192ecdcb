import { IncomePaymentScheduledEntity } from 'src/shared/database/typeorm/entities/income-payment-scheduled.entity';
import { Repository } from 'typeorm';
import { ClientPaymentsQueryDto } from '../dto/client-payment.dto';
import { IClientPaymentsListResponse, IClientPaymentDetailResponse } from '../responses/client-payment.response';
export declare class ClientPaymentService {
    private readonly incomePaymentScheduledRepository;
    constructor(incomePaymentScheduledRepository: Repository<IncomePaymentScheduledEntity>);
    getPaymentsByUserId(userId: string, filters?: ClientPaymentsQueryDto): Promise<IClientPaymentsListResponse>;
    getPaymentById(paymentId: string): Promise<IClientPaymentDetailResponse>;
}
