import { CheckInvestorDto } from '../dto/check-investor.dto';
import { ICheckInvestorResponse } from '../responses/check-investor.response';
import { CheckInvestorService } from '../services/check-investor.service';
export declare class CheckInvestorController {
    private readonly checkInvestorService;
    constructor(checkInvestorService: CheckInvestorService);
    checkInvestor(body: CheckInvestorDto): Promise<ICheckInvestorResponse>;
}
