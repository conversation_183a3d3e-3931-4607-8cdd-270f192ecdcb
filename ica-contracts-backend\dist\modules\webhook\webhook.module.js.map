{"version": 3, "file": "webhook.module.js", "sourceRoot": "/", "sources": ["modules/webhook/webhook.module.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAAwC;AACxC,wDAAkD;AAClD,8DAAwD;AAExD,yFAAoF;AACpF,wGAAkG;AAClG,wGAAiG;AACjG,4GAAqG;AACrG,oGAA6F;AAC7F,gGAA0F;AAC1F,8GAAuG;AACvG,wFAAkF;AAClF,wGAAiG;AACjG,wHAAgH;AAChH,wHAAgH;AAChH,wHAAgH;AAChH,8GAAsG;AACtG,oHAA4G;AAC5G,kGAA4F;AAC5F,8FAAwF;AAuBjF,IAAM,aAAa,GAAnB,MAAM,aAAa;CAAG,CAAA;AAAhB,sCAAa;wBAAb,aAAa;IArBzB,IAAA,eAAM,EAAC;QACN,OAAO,EAAE,CAAC,4BAAY,EAAE,wBAAU,CAAC;QACnC,WAAW,EAAE,CAAC,qDAAwB,CAAC;QACvC,SAAS,EAAE;YACT,2EAAkC;YAClC,qEAA+B;YAC/B,qEAA+B;YAC/B,0EAAiC;YACjC,gFAAoC;YACpC,oFAAsC;YACtC,oFAAsC;YACtC,oFAAsC;YACtC,sEAAgC;YAChC,iEAA6B;YAC7B,yEAAiC;YACjC,sDAAwB;YACxB,gEAA6B;YAC7B,8DAA4B;YAC5B,4DAA2B;SAC5B;KACF,CAAC;GACW,aAAa,CAAG", "sourcesContent": ["import { Module } from '@nestjs/common';\r\nimport { ApisModule } from 'src/apis/apis.module';\r\nimport { SharedModule } from 'src/shared/shared.module';\r\n\r\nimport { WebhookCelcoinController } from './controllers/webhook-celcoin.controller';\r\nimport { BillPaymentCelcoinWebhookService } from './services/billpayment-celcoin-webhook.service';\r\nimport { CelcoinPixPaymentWebhookService } from './services/celcoin-pix-payment-webhook.service';\r\nimport { ChargeCreateCelcoinWebhookService } from './services/charge-create-celcoin-webhook.service';\r\nimport { ChargeInCelcoinWebhookService } from './services/charge-in-celcoin-webhook.service';\r\nimport { ConsultCelcoinWebhookService } from './services/consult-celcoin-webhook.service';\r\nimport { CreateAccountCelcoinWebhookService } from './services/create-account-celcoin-webhook.service';\r\nimport { KycCelcoinWebhookService } from './services/kyc-celcoin-webhook.service';\r\nimport { PixCashoutCelcoinWebhookService } from './services/pix-cashout-celcoin-webhook.service';\r\nimport { PixClaimCancelledCelcoinWebhookService } from './services/pix-claim-cancelled-celcoin-webhook.service';\r\nimport { PixClaimCompletedCelcoinWebhookService } from './services/pix-claim-completed-celcoin-webhook.service';\r\nimport { PixClaimConfirmedCelcoinWebhookService } from './services/pix-claim-confirmed-celcoin-webhook.service';\r\nimport { PixClaimOpenCelcoinWebhookService } from './services/pix-claim-open-celcoin-webhook.service';\r\nimport { PixClaimWaitingCelcoinWebhookService } from './services/pix-claim-waiting-celcoin-webhook.service';\r\nimport { RegisterCelcoinWebhookService } from './services/register-celcoin-webhook.service';\r\nimport { UpdateCelcoinWebhookService } from './services/update-celcoin-webhook.service';\r\n\r\n@Module({\r\n  imports: [SharedModule, ApisModule],\r\n  controllers: [WebhookCelcoinController],\r\n  providers: [\r\n    CreateAccountCelcoinWebhookService,\r\n    PixCashoutCelcoinWebhookService,\r\n    CelcoinPixPaymentWebhookService,\r\n    PixClaimOpenCelcoinWebhookService,\r\n    PixClaimWaitingCelcoinWebhookService,\r\n    PixClaimConfirmedCelcoinWebhookService,\r\n    PixClaimCancelledCelcoinWebhookService,\r\n    PixClaimCompletedCelcoinWebhookService,\r\n    BillPaymentCelcoinWebhookService,\r\n    ChargeInCelcoinWebhookService,\r\n    ChargeCreateCelcoinWebhookService,\r\n    KycCelcoinWebhookService,\r\n    RegisterCelcoinWebhookService,\r\n    ConsultCelcoinWebhookService,\r\n    UpdateCelcoinWebhookService,\r\n  ],\r\n})\r\nexport class WebhookModule {}\r\n"]}