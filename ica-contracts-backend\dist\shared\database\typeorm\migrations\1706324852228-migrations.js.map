{"version": 3, "file": "1706324852228-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1706324852228-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAqEnC,CAAC;IAnEQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,kfAAkf,CACnf,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,sRAAsR,CACvR,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wKAAwK,CACzK,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,8QAA8Q,CAC/Q,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,mWAAmW,CACpW,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,uKAAuK,CACxK,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,oKAAoK,CACrK,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,0KAA0K,CAC3K,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,uLAAuL,CACxL,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,6KAA6K,CAC9K,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,2KAA2K,CAC5K,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,qKAAqK,CACtK,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,wEAAwE,CACzE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,oFAAoF,CACrF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,oFAAoF,CACrF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,2EAA2E,CAC5E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,2EAA2E,CAC5E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,2EAA2E,CAC5E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,8EAA8E,CAC/E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAChD,MAAM,WAAW,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC;QAC5D,MAAM,WAAW,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;QAC7C,MAAM,WAAW,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;QACnD,MAAM,WAAW,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;IACxD,CAAC;CACF;AAtED,0DAsEC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1706324852228 implements MigrationInterface {\r\n  name = 'Migrations1706324852228';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"device_entity\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"owner_id\" uuid, \"name\" character varying NOT NULL, \"model\" character varying NOT NULL, \"code\" character varying NOT NULL, \"token\" character varying NOT NULL, \"latitude\" character varying NOT NULL, \"longitude\" character varying NOT NULL, \"updated_at\" TIMESTAMP NOT NULL DEFAULT now(), \"created_at\" TIMESTAMP NOT NULL DEFAULT now(), \"deleted_at\" TIMESTAMP, CONSTRAINT \"PK_a75e1d635b3b07412a2ab3eb000\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"permission\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"owner_id\" uuid, \"business_id\" uuid, \"owner_role_id\" uuid NOT NULL, \"created_at\" TIMESTAMP NOT NULL DEFAULT now(), \"deleted_at\" TIMESTAMP, CONSTRAINT \"PK_3b8b97af9d9d8807e41e6f48362\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"role\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"name\" character varying NOT NULL, CONSTRAINT \"PK_b36bcfe02fc8de3c57a8b2391c2\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"owner_role_relation\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"owner_id\" uuid NOT NULL, \"role_id\" uuid NOT NULL, \"created_at\" TIMESTAMP NOT NULL DEFAULT now(), \"deleted_at\" TIMESTAMP, CONSTRAINT \"PK_e39ae65ffce1e6831360fd87c6d\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"pix-key\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"account_id\" uuid NOT NULL, \"type_key\" character varying NOT NULL, \"key\" character varying NOT NULL, \"updated_at\" TIMESTAMP NOT NULL DEFAULT now(), \"created_at\" TIMESTAMP NOT NULL DEFAULT now(), \"deleted_at\" TIMESTAMP, CONSTRAINT \"PK_97b3a249aca9b0fd61ae0d877ee\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"device_entity\" ADD CONSTRAINT \"FK_8ecb72968cbdf87be784fe6cf1d\" FOREIGN KEY (\"owner_id\") REFERENCES \"owner\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"permission\" ADD CONSTRAINT \"FK_3c2f72152c8b1d79c0879c20506\" FOREIGN KEY (\"owner_id\") REFERENCES \"owner\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"permission\" ADD CONSTRAINT \"FK_80c78d5396e006f3a2ae7800c12\" FOREIGN KEY (\"business_id\") REFERENCES \"business\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"permission\" ADD CONSTRAINT \"FK_f697f57ebfc670aa2f6d82ded7c\" FOREIGN KEY (\"owner_role_id\") REFERENCES \"owner_role_relation\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner_role_relation\" ADD CONSTRAINT \"FK_622c8e2dabdd04a18f9bc656570\" FOREIGN KEY (\"owner_id\") REFERENCES \"owner\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner_role_relation\" ADD CONSTRAINT \"FK_235ec492b335d8e8883f4997a70\" FOREIGN KEY (\"role_id\") REFERENCES \"role\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pix-key\" ADD CONSTRAINT \"FK_28c2c42359e273bc3552c8879b5\" FOREIGN KEY (\"account_id\") REFERENCES \"account\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pix-key\" DROP CONSTRAINT \"FK_28c2c42359e273bc3552c8879b5\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner_role_relation\" DROP CONSTRAINT \"FK_235ec492b335d8e8883f4997a70\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner_role_relation\" DROP CONSTRAINT \"FK_622c8e2dabdd04a18f9bc656570\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"permission\" DROP CONSTRAINT \"FK_f697f57ebfc670aa2f6d82ded7c\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"permission\" DROP CONSTRAINT \"FK_80c78d5396e006f3a2ae7800c12\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"permission\" DROP CONSTRAINT \"FK_3c2f72152c8b1d79c0879c20506\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"device_entity\" DROP CONSTRAINT \"FK_8ecb72968cbdf87be784fe6cf1d\"`,\r\n    );\r\n    await queryRunner.query(`DROP TABLE \"pix-key\"`);\r\n    await queryRunner.query(`DROP TABLE \"owner_role_relation\"`);\r\n    await queryRunner.query(`DROP TABLE \"role\"`);\r\n    await queryRunner.query(`DROP TABLE \"permission\"`);\r\n    await queryRunner.query(`DROP TABLE \"device_entity\"`);\r\n  }\r\n}\r\n"]}