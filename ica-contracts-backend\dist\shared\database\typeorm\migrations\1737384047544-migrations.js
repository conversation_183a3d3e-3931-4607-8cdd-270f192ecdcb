"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1737384047544 = void 0;
class Migrations1737384047544 {
    constructor() {
        this.name = 'Migrations1737384047544';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "notification" DROP COLUMN "contract_value"`);
        await queryRunner.query(`ALTER TABLE "notification" ADD "contract_value" numeric`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "notification" DROP COLUMN "contract_value"`);
        await queryRunner.query(`ALTER TABLE "notification" ADD "contract_value" integer`);
    }
}
exports.Migrations1737384047544 = Migrations1737384047544;
//# sourceMappingURL=1737384047544-migrations.js.map