{"version": 3, "file": "1750267202855-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1750267202855-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,iDAAiD;IAA9D;QAGE,SAAI,GAAG,mDAAmD,CAAC;IAa7D,CAAC;IAXQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,yDAAyD,CAC1D,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,yDAAyD,CAC1D,CAAC;IACJ,CAAC;CACF;AAhBD,8GAgBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class AddDurationInMonthseColumnToContract1750267202855\r\n  implements MigrationInterface\r\n{\r\n  name = 'AddDurationInMonthseColumnToContract1750267202855';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract\" ADD \"duration_in_months\" integer`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract\" DROP COLUMN \"duration_in_months\"`,\r\n    );\r\n  }\r\n}\r\n"]}