"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1726692843297 = void 0;
class Migrations1726692843297 {
    constructor() {
        this.name = 'Migrations1726692843297';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "movement" ("id" SERIAL NOT NULL, "operation_type" character varying NOT NULL, "amount" numeric NOT NULL, "description" text NOT NULL, "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "investor_id" uuid, CONSTRAINT "PK_079f005d01ebda984e75c2d67ee" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "investor_id" uuid`);
        await queryRunner.query(`ALTER TABLE "contract" ADD CONSTRAINT "FK_2a761745cdcd4c816a99d2243bc" FOREIGN KEY ("investor_id") REFERENCES "owner_role_relation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "movement" ADD CONSTRAINT "FK_bd105c1eda01fd5a1bace33beed" FOREIGN KEY ("investor_id") REFERENCES "owner_role_relation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "movement" DROP CONSTRAINT "FK_bd105c1eda01fd5a1bace33beed"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "FK_2a761745cdcd4c816a99d2243bc"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "investor_id"`);
        await queryRunner.query(`DROP TABLE "movement"`);
    }
}
exports.Migrations1726692843297 = Migrations1726692843297;
//# sourceMappingURL=1726692843297-migrations.js.map