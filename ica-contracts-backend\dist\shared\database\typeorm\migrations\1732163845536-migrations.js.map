{"version": 3, "file": "1732163845536-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1732163845536-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAWnC,CAAC;IATQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,4EAA4E,CAC7E,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAC;IACvE,CAAC;CACF;AAZD,0DAYC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1732163845536 implements MigrationInterface {\r\n  name = 'Migrations1732163845536';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract\" ADD \"type\" character varying NOT NULL DEFAULT 'p2p'`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(`ALTER TABLE \"contract\" DROP COLUMN \"type\"`);\r\n  }\r\n}\r\n"]}