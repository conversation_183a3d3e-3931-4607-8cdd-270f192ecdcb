"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditContractDto = void 0;
const class_validator_1 = require("class-validator");
const contract_audit_entity_1 = require("../../../shared/database/typeorm/entities/contract-audit.entity");
class AuditContractDto {
}
exports.AuditContractDto = AuditContractDto;
__decorate([
    (0, class_validator_1.IsUUID)(4, { message: 'ID do contrato inválido' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'ID do contrato é obrigatório' }),
    __metadata("design:type", String)
], AuditContractDto.prototype, "contractId", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(contract_audit_entity_1.AuditDecisionEnum, { message: 'Decisão inválida' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Decisão é obrigatória' }),
    __metadata("design:type", String)
], AuditContractDto.prototype, "decision", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], AuditContractDto.prototype, "comments", void 0);
__decorate([
    (0, class_validator_1.ValidateIf)((o) => o.decision === contract_audit_entity_1.AuditDecisionEnum.REJECTED),
    (0, class_validator_1.IsObject)({ message: 'Motivos da rejeição devem ser um objeto' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Motivos da rejeição são obrigatórios para contratos rejeitados' }),
    __metadata("design:type", Object)
], AuditContractDto.prototype, "rejectionReasons", void 0);
//# sourceMappingURL=audit-contract.dto.js.map