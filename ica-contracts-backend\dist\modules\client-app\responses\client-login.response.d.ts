export interface IClientContract {
    id: number | string;
    type: 'starter' | 'additive';
    date: string;
    invested: number;
    contract: string;
}
export interface IClientAccount {
    name: string;
    yield: number;
    nextPayment: string;
    contractDate: string;
    totalInvested: number;
    paymentAmount: number;
    contracts: IClientContract[];
}
export interface IClientLoginResponse {
    accessToken: string;
    refreshToken?: string;
    account: IClientAccount;
}
