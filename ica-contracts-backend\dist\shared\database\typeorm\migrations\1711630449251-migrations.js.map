{"version": 3, "file": "1711630449251-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1711630449251-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAqCnC,CAAC;IAnCQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,+DAA+D,CAChE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wEAAwE,CACzE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wEAAwE,CACzE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,mEAAmE,CACpE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,uEAAuE,CACxE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAC;QAC3E,MAAM,WAAW,CAAC,KAAK,CACrB,2DAA2D,CAC5D,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,gEAAgE,CACjE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACzE,MAAM,WAAW,CAAC,KAAK,CACrB,oDAAoD,CACrD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC;QAC1E,MAAM,WAAW,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACzE,MAAM,WAAW,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACzE,MAAM,WAAW,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;IACxE,CAAC;CACF;AAtCD,0DAsCC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1711630449251 implements MigrationInterface {\r\n  name = 'Migrations1711630449251';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"billet\" ADD \"barcode\" character varying NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"billet\" ADD \"updated_at\" TIMESTAMP NOT NULL DEFAULT now()`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"billet\" ADD \"created_at\" TIMESTAMP NOT NULL DEFAULT now()`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"billet\" ADD \"debtor_name\" character varying NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"billet\" ADD \"debtor_document\" character varying NOT NULL`,\r\n    );\r\n    await queryRunner.query(`ALTER TABLE \"billet\" ADD \"deleted_at\" TIMESTAMP`);\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"billet\" ALTER COLUMN \"due_date\" DROP DEFAULT`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"billet\" ALTER COLUMN \"due_date\" SET DEFAULT now()`,\r\n    );\r\n    await queryRunner.query(`ALTER TABLE \"billet\" DROP COLUMN \"deleted_at\"`);\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"billet\" DROP COLUMN \"debtor_document\"`,\r\n    );\r\n    await queryRunner.query(`ALTER TABLE \"billet\" DROP COLUMN \"debtor_name\"`);\r\n    await queryRunner.query(`ALTER TABLE \"billet\" DROP COLUMN \"created_at\"`);\r\n    await queryRunner.query(`ALTER TABLE \"billet\" DROP COLUMN \"updated_at\"`);\r\n    await queryRunner.query(`ALTER TABLE \"billet\" DROP COLUMN \"barcode\"`);\r\n  }\r\n}\r\n"]}