"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1736902814115 = void 0;
class Migrations1736902814115 {
    constructor() {
        this.name = 'Migrations1736902814115';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "contract_advisor" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "rate" numeric(5,2) NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "id_contract" uuid, "id_advisor" uuid, CONSTRAINT "UQ_57ddfe73a0f966bba89f031cc9d" UNIQUE ("id_contract", "id_advisor"), CONSTRAINT "PK_76d4fa3107ee65be1406e768a30" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "contract_advisor" ADD CONSTRAINT "FK_b3eb1d7d01d429b1fc2ae37c05b" FOREIGN KEY ("id_contract") REFERENCES "contract"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract_advisor" ADD CONSTRAINT "FK_cba07ac9ce330bc89efb1e82786" FOREIGN KEY ("id_advisor") REFERENCES "owner_role_relation"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "contract_advisor" DROP CONSTRAINT "FK_cba07ac9ce330bc89efb1e82786"`);
        await queryRunner.query(`ALTER TABLE "contract_advisor" DROP CONSTRAINT "FK_b3eb1d7d01d429b1fc2ae37c05b"`);
        await queryRunner.query(`DROP TABLE "contract_advisor"`);
    }
}
exports.Migrations1736902814115 = Migrations1736902814115;
//# sourceMappingURL=1736902814115-migrations.js.map