{"version": 3, "file": "1726117976966-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1726117976966-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAyBnC,CAAC;IAvBQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,2EAA2E,CAC5E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,iEAAiE,CAClE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,6DAA6D,CAC9D,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,uDAAuD,CACxD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wDAAwD,CACzD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wDAAwD,CACzD,CAAC;IACJ,CAAC;CACF;AA1BD,0DA0BC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1726117976966 implements MigrationInterface {\r\n  name = 'Migrations1726117976966';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pre-register\" ADD \"purchase_with\" character varying NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pre-register\" ADD \"amount_quotes\" integer NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pre-register\" ADD \"grace_period\" date NOT NULL`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pre-register\" DROP COLUMN \"grace_period\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pre-register\" DROP COLUMN \"amount_quotes\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pre-register\" DROP COLUMN \"purchase_with\"`,\r\n    );\r\n  }\r\n}\r\n"]}