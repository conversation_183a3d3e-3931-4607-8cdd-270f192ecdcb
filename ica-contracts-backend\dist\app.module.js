"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const schedule_1 = require("@nestjs/schedule");
const apis_module_1 = require("./apis/apis.module");
const messaging_module_1 = require("./messaging/messaging.module");
const account_transfer_limit_module_1 = require("./modules/account-transfer-limit/account-transfer-limit.module");
const account_module_1 = require("./modules/account/account.module");
const acquisition_module_1 = require("./modules/acquisition/acquisition.module");
const admin_module_1 = require("./modules/admin/admin.module");
const advisor_module_1 = require("./modules/advisor/advisor.module");
const app_version_module_1 = require("./modules/app-version/app-version.module");
const auth_backoffice_module_1 = require("./modules/auth-backoffice/auth-backoffice.module");
const auth_module_1 = require("./modules/auth/auth.module");
const boleto_module_1 = require("./modules/boleto/boleto.module");
const broker_module_1 = require("./modules/broker/broker.module");
const contract_lifecycle_monitoring_module_1 = require("./modules/contract-lifecycle-monitoring/contract-lifecycle-monitoring.module");
const contract_module_1 = require("./modules/contract/contract.module");
const device_module_1 = require("./modules/device/device.module");
const goals_module_1 = require("./modules/goals/goals.module");
const income_payment_module_1 = require("./modules/income-payment/income-payment.module");
const income_module_1 = require("./modules/income/income.module");
const investor_module_1 = require("./modules/investor/investor.module");
const location_module_1 = require("./modules/location/location.module");
const notification_module_1 = require("./modules/notifications/notification.module");
const owner_module_1 = require("./modules/owner/owner.module");
const pix_module_1 = require("./modules/pix/pix.module");
const pre_register_module_1 = require("./modules/pre-register/pre-register.module");
const recharge_module_1 = require("./modules/recharge/recharge.module");
const report_module_1 = require("./modules/reports/report.module");
const statistic_module_1 = require("./modules/statistic/statistic.module");
const superadmin_module_1 = require("./modules/superadmin/superadmin.module");
const ted_module_1 = require("./modules/ted/ted.module");
const two_factor_auth_module_1 = require("./modules/two-factor-auth/two-factor-auth.module");
const uploads_module_1 = require("./modules/uploads/uploads.module");
const wallets_module_1 = require("./modules/wallets/wallets.module");
const webhook_module_1 = require("./modules/webhook/webhook.module");
const br_holiday_module_1 = require("./shared/modules/br-holiday.module");
const shared_module_1 = require("./shared/shared.module");
const income_report_module_1 = require("./modules/income-report/income-report.module");
const client_app_module_1 = require("./modules/client-app/client-app.module");
const audit_module_1 = require("./modules/audit/audit.module");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
            }),
            shared_module_1.SharedModule,
            account_module_1.AccountModule,
            auth_module_1.AuthModule,
            owner_module_1.OwnerModule,
            client_app_module_1.ClientAppModule,
            broker_module_1.BrokerModule,
            apis_module_1.ApisModule,
            investor_module_1.InvestorModule,
            superadmin_module_1.SuperAdminModule,
            webhook_module_1.WebhookModule,
            advisor_module_1.AdvisorModule,
            admin_module_1.AdminModule,
            pix_module_1.PixModule,
            device_module_1.DeviceModule,
            account_transfer_limit_module_1.AccountTransferLimitModule,
            boleto_module_1.BoletoModule,
            ted_module_1.TedModule,
            messaging_module_1.MessagingModule,
            schedule_1.ScheduleModule.forRoot(),
            app_version_module_1.AppVersionModule,
            statistic_module_1.StatistcModule,
            location_module_1.LocationModule,
            recharge_module_1.RechargeModule,
            auth_backoffice_module_1.AuthBackofficeModule,
            pre_register_module_1.PreRegisterModule,
            contract_module_1.ContractModule,
            income_module_1.IncomeModule,
            wallets_module_1.WalletsModule,
            contract_lifecycle_monitoring_module_1.ContractLifecycleMonitoringModule,
            uploads_module_1.UploadsModule,
            goals_module_1.GoalsModule,
            income_payment_module_1.IncomePaymentModule,
            notification_module_1.NotificationModule,
            acquisition_module_1.AcquisitionModule,
            report_module_1.ReportModule,
            br_holiday_module_1.BrHolidayModule,
            two_factor_auth_module_1.TwoFactorAuthModule,
            income_report_module_1.IncomeReportsModule,
            audit_module_1.AuditModule
        ],
        providers: [],
        controllers: [],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map