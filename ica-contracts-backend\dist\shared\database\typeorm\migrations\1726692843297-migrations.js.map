{"version": 3, "file": "1726692843297-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1726692843297-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAyBnC,CAAC;IAvBQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,+VAA+V,CAChW,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACzE,MAAM,WAAW,CAAC,KAAK,CACrB,mLAAmL,CACpL,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,mLAAmL,CACpL,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,yEAAyE,CAC1E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,yEAAyE,CAC1E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,kDAAkD,CAAC,CAAC;QAC5E,MAAM,WAAW,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;IACnD,CAAC;CACF;AA1BD,0DA0BC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1726692843297 implements MigrationInterface {\r\n  name = 'Migrations1726692843297';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"movement\" (\"id\" SERIAL NOT NULL, \"operation_type\" character varying NOT NULL, \"amount\" numeric NOT NULL, \"description\" text NOT NULL, \"updated_at\" TIMESTAMP NOT NULL DEFAULT now(), \"created_at\" TIMESTAMP NOT NULL DEFAULT now(), \"deleted_at\" TIMESTAMP, \"investor_id\" uuid, CONSTRAINT \"PK_079f005d01ebda984e75c2d67ee\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(`ALTER TABLE \"contract\" ADD \"investor_id\" uuid`);\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract\" ADD CONSTRAINT \"FK_2a761745cdcd4c816a99d2243bc\" FOREIGN KEY (\"investor_id\") REFERENCES \"owner_role_relation\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"movement\" ADD CONSTRAINT \"FK_bd105c1eda01fd5a1bace33beed\" FOREIGN KEY (\"investor_id\") REFERENCES \"owner_role_relation\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"movement\" DROP CONSTRAINT \"FK_bd105c1eda01fd5a1bace33beed\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract\" DROP CONSTRAINT \"FK_2a761745cdcd4c816a99d2243bc\"`,\r\n    );\r\n    await queryRunner.query(`ALTER TABLE \"contract\" DROP COLUMN \"investor_id\"`);\r\n    await queryRunner.query(`DROP TABLE \"movement\"`);\r\n  }\r\n}\r\n"]}