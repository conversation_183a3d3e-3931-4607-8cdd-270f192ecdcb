{"version": 3, "file": "1716317845514-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1716317845514-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IA2CnC,CAAC;IAzCQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,iEAAiE,CAClE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,yFAAyF,CAC1F,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,mEAAmE,CACpE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,2FAA2F,CAC5F,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,uEAAuE,CACxE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,+FAA+F,CAChG,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,uEAAuE,CACxE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,+FAA+F,CAChG,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,mEAAmE,CACpE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,2FAA2F,CAC5F,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,iEAAiE,CAClE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,yFAAyF,CAC1F,CAAC;IACJ,CAAC;CACF;AA5CD,0DA4CC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1716317845514 implements MigrationInterface {\r\n  name = 'Migrations1716317845514';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" DROP COLUMN \"daily_limit\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" ADD \"daily_limit\" numeric NOT NULL DEFAULT '1000'`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" DROP COLUMN \"monthly_limit\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" ADD \"monthly_limit\" numeric NOT NULL DEFAULT '1000'`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" DROP COLUMN \"daily_night_limit\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" ADD \"daily_night_limit\" numeric NOT NULL DEFAULT '1000'`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" DROP COLUMN \"daily_night_limit\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" ADD \"daily_night_limit\" integer NOT NULL DEFAULT '1000'`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" DROP COLUMN \"monthly_limit\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" ADD \"monthly_limit\" integer NOT NULL DEFAULT '1000'`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" DROP COLUMN \"daily_limit\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" ADD \"daily_limit\" integer NOT NULL DEFAULT '1000'`,\r\n    );\r\n  }\r\n}\r\n"]}