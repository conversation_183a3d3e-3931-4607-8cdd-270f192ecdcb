{"version": 3, "file": "1740685107035-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1740685107035-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACI,SAAI,GAAG,yBAAyB,CAAA;IAoBpC,CAAC;IAlBU,KAAK,CAAC,EAAE,CAAC,WAAwB;QACpC,MAAM,WAAW,CAAC,KAAK,CAAC,6EAA6E,CAAC,CAAC;QACvG,MAAM,WAAW,CAAC,KAAK,CAAC,sEAAsE,CAAC,CAAC;QAChG,MAAM,WAAW,CAAC,KAAK,CAAC,mDAAmD,CAAC,CAAC;QAC7E,MAAM,WAAW,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAC;QAC3E,MAAM,WAAW,CAAC,KAAK,CAAC,qLAAqL,CAAC,CAAC;QAC/M,MAAM,WAAW,CAAC,KAAK,CAAC,uLAAuL,CAAC,CAAC;IACrN,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CAAC,6EAA6E,CAAC,CAAC;QACvG,MAAM,WAAW,CAAC,KAAK,CAAC,6EAA6E,CAAC,CAAC;QACvG,MAAM,WAAW,CAAC,KAAK,CAAC,oDAAoD,CAAC,CAAC;QAC9E,MAAM,WAAW,CAAC,KAAK,CAAC,sDAAsD,CAAC,CAAC;QAChF,MAAM,WAAW,CAAC,KAAK,CAAC,mEAAmE,CAAC,CAAC;QAC7F,MAAM,WAAW,CAAC,KAAK,CAAC,uMAAuM,CAAC,CAAC;IACrO,CAAC;CAEJ;AArBD,0DAqBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from \"typeorm\";\r\n\r\nexport class Migrations1740685107035 implements MigrationInterface {\r\n    name = 'Migrations1740685107035'\r\n\r\n    public async up(queryRunner: QueryRunner): Promise<void> {\r\n        await queryRunner.query(`ALTER TABLE \"notification\" DROP CONSTRAINT \"FK_8ca93a4412001fddb88e326b91f\"`);\r\n        await queryRunner.query(`ALTER TABLE \"notification\" DROP COLUMN \"user_owner_role_relation_id\"`);\r\n        await queryRunner.query(`ALTER TABLE \"notification\" ADD \"investor_id\" uuid`);\r\n        await queryRunner.query(`ALTER TABLE \"notification\" ADD \"broker_id\" uuid`);\r\n        await queryRunner.query(`ALTER TABLE \"notification\" ADD CONSTRAINT \"FK_e3ef26f3cdca8b42a45bb8f25ab\" FOREIGN KEY (\"broker_id\") REFERENCES \"owner_role_relation\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`);\r\n        await queryRunner.query(`ALTER TABLE \"notification\" ADD CONSTRAINT \"FK_91d75bba992225527b598ea2bf7\" FOREIGN KEY (\"investor_id\") REFERENCES \"owner_role_relation\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`);\r\n    }\r\n\r\n    public async down(queryRunner: QueryRunner): Promise<void> {\r\n        await queryRunner.query(`ALTER TABLE \"notification\" DROP CONSTRAINT \"FK_91d75bba992225527b598ea2bf7\"`);\r\n        await queryRunner.query(`ALTER TABLE \"notification\" DROP CONSTRAINT \"FK_e3ef26f3cdca8b42a45bb8f25ab\"`);\r\n        await queryRunner.query(`ALTER TABLE \"notification\" DROP COLUMN \"broker_id\"`);\r\n        await queryRunner.query(`ALTER TABLE \"notification\" DROP COLUMN \"investor_id\"`);\r\n        await queryRunner.query(`ALTER TABLE \"notification\" ADD \"user_owner_role_relation_id\" uuid`);\r\n        await queryRunner.query(`ALTER TABLE \"notification\" ADD CONSTRAINT \"FK_8ca93a4412001fddb88e326b91f\" FOREIGN KEY (\"user_owner_role_relation_id\") REFERENCES \"owner_role_relation\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`);\r\n    }\r\n\r\n}\r\n"]}