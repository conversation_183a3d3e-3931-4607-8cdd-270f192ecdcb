{"version": 3, "file": "1713463327102-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1713463327102-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAiBnC,CAAC;IAfQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,2DAA2D,CAC5D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,+DAA+D,CAChE,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,qDAAqD,CACtD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAC;IAC7E,CAAC;CACF;AAlBD,0DAkBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1713463327102 implements MigrationInterface {\r\n  name = 'Migrations1713463327102';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"app_version\" ADD \"ios_url\" character varying`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"app_version\" ADD \"android_url\" character varying`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"app_version\" DROP COLUMN \"android_url\"`,\r\n    );\r\n    await queryRunner.query(`ALTER TABLE \"app_version\" DROP COLUMN \"ios_url\"`);\r\n  }\r\n}\r\n"]}