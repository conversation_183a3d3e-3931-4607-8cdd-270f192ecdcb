{"version": 3, "file": "1709918663925-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1709918663925-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAanC,CAAC;IAXQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,+DAA+D,CAChE,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,8DAA8D,CAC/D,CAAC;IACJ,CAAC;CACF;AAdD,0DAcC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1709918663925 implements MigrationInterface {\r\n  name = 'Migrations1709918663925';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"address\" ALTER COLUMN \"complement\" DROP NOT NULL`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"address\" ALTER COLUMN \"complement\" SET NOT NULL`,\r\n    );\r\n  }\r\n}\r\n"]}