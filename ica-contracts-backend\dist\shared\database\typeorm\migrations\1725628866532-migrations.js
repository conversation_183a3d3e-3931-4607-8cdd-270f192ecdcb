"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1725628866532 = void 0;
class Migrations1725628866532 {
    constructor() {
        this.name = 'Migrations1725628866532';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "contract" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "external_id" integer NOT NULL, "creatorType" character varying NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "owner_id" uuid, "business_id" uuid, CONSTRAINT "PK_17c3a89f58a2997276084e706e8" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "pre-register" ADD "contract_id" uuid`);
        await queryRunner.query(`ALTER TABLE "pre-register" ADD CONSTRAINT "FK_f5dd323431219906bb41f6c1abf" FOREIGN KEY ("contract_id") REFERENCES "contract"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract" ADD CONSTRAINT "FK_9b5bba51f1fc7a6a6ddbfe87c93" FOREIGN KEY ("owner_id") REFERENCES "owner"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract" ADD CONSTRAINT "FK_f1f42c0cd4752f6420997f04f10" FOREIGN KEY ("business_id") REFERENCES "business"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "FK_f1f42c0cd4752f6420997f04f10"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "FK_9b5bba51f1fc7a6a6ddbfe87c93"`);
        await queryRunner.query(`ALTER TABLE "pre-register" DROP CONSTRAINT "FK_f5dd323431219906bb41f6c1abf"`);
        await queryRunner.query(`ALTER TABLE "pre-register" DROP COLUMN "contract_id"`);
        await queryRunner.query(`DROP TABLE "contract"`);
    }
}
exports.Migrations1725628866532 = Migrations1725628866532;
//# sourceMappingURL=1725628866532-migrations.js.map