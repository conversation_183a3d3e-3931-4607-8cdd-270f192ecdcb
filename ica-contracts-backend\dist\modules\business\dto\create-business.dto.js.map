{"version": 3, "file": "create-business.dto.js", "sourceRoot": "/", "sources": ["modules/business/dto/create-business.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,yEAA0D;AAC1D,qDAA+D;AAE/D,MAAa,wBAAwB;CAuBpC;AAvBD,4DAuBC;AApBC;IAFC,IAAA,2BAAS,EAAC,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;IAC9C,IAAA,0BAAQ,EAAC,EAAE,OAAO,EAAE,4BAA4B,EAAE,CAAC;;sDACvC;AAKb;IAHC,IAAA,2BAAS,EAAC,EAAE,OAAO,EAAE,qBAAqB,EAAE,CAAC;IAC7C,IAAA,0BAAQ,EAAC,EAAE,OAAO,EAAE,2BAA2B,EAAE,CAAC;IAClD,IAAA,iCAAK,EAAC,EAAE,OAAO,EAAE,8BAA8B,EAAE,CAAC;;qDACvC;AAKZ;IAHC,IAAA,2BAAS,EAAC,EAAE,OAAO,EAAE,wBAAwB,EAAE,CAAC;IAChD,IAAA,0BAAQ,EAAC,EAAE,OAAO,EAAE,8BAA8B,EAAE,CAAC;IACrD,IAAA,yBAAO,EAAC,EAAE,EAAE,EAAE,OAAO,EAAE,gDAAgD,EAAE,CAAC;;uDAC7D;AAKd;IAHC,IAAA,2BAAS,EAAC,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;IAC9C,IAAA,0BAAQ,EAAC,EAAE,OAAO,EAAE,4BAA4B,EAAE,CAAC;IACnD,IAAA,kCAAM,EAAC,EAAE,OAAO,EAAE,gCAAgC,EAAE,CAAC;;sDACzC;AAIb;IAFC,IAAA,2BAAS,EAAC,EAAE,OAAO,EAAE,iCAAiC,EAAE,CAAC;IACzD,IAAA,0BAAQ,EAAC,EAAE,OAAO,EAAE,uCAAuC,EAAE,CAAC;;6DAC3C", "sourcesContent": ["import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'brazilian-class-validator';\r\nimport { IsDefined, IsEmail, IsString } from 'class-validator';\r\n\r\nexport class CreateBusinessRequestDto {\r\n  @IsDefined({ message: 'O nome é obrigatorio' })\r\n  @IsString({ message: 'O nome deve ser uma string' })\r\n  name: string;\r\n\r\n  @IsDefined({ message: 'O CPF é obrigatório' })\r\n  @IsString({ message: 'O CPF deve ser uma string' })\r\n  @IsCPF({ message: 'O CPF deve ser um CPF válido' })\r\n  cpf: string;\r\n\r\n  @IsDefined({ message: 'O e-mail é obrigatório' })\r\n  @IsString({ message: 'O e-mail deve ser uma string' })\r\n  @IsEmail({}, { message: 'O e-mail deve ser um endereço de e-mail válido' })\r\n  email: string;\r\n\r\n  @IsDefined({ message: 'O CNPJ é obrigatório' })\r\n  @IsString({ message: 'O CNPJ deve ser uma string' })\r\n  @IsCNPJ({ message: 'O CNPJ deve ser um CNPJ válido' })\r\n  cnpj: string;\r\n\r\n  @IsDefined({ message: 'O nome da empresa é obrigatório' })\r\n  @IsString({ message: 'O nome da empresa deve ser uma string' })\r\n  companyName: string;\r\n}\r\n"]}