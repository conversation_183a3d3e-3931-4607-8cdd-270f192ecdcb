{"version": 3, "file": "consult-admin-account-extract.dto.js", "sourceRoot": "/", "sources": ["modules/account/dto/consult-admin-account-extract.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAOyB;AACzB,yGAA8F;AAE9F,MAAa,2BAA2B;CA8BvC;AA9BD,kEA8BC;AAxBC;IALC,IAAA,2BAAS,GAAE;IACX,IAAA,2BAAS,EACR,EAAE,MAAM,EAAE,IAAI,EAAE,EAChB,EAAE,OAAO,EAAE,iDAAiD,EAAE,CAC/D;;6DACgB;AAOjB;IALC,IAAA,2BAAS,GAAE;IACX,IAAA,2BAAS,EACR,EAAE,MAAM,EAAE,IAAI,EAAE,EAChB,EAAE,OAAO,EAAE,iDAAiD,EAAE,CAC/D;;2DACc;AAIf;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;iEACW;AAItB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;yDACG;AAId;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,4DAA2B,CAAC;;oEACU;AAI9C;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,wBAAM,GAAE;;8DACS", "sourcesContent": ["import {\r\n  IsDefined,\r\n  IsE<PERSON>,\r\n  IsISO86<PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  IsS<PERSON>,\r\n  IsUUI<PERSON>,\r\n} from 'class-validator';\r\nimport { TransactionMovementTypeEnum } from 'src/shared/enums/transaction-movement-type.enum';\r\n\r\nexport class IConsultAdminAccountExtract {\r\n  @IsDefined()\r\n  @IsISO8601(\r\n    { strict: true },\r\n    { message: 'A data de início deve estar no formato ISO 8601' },\r\n  )\r\n  DateFrom: string;\r\n\r\n  @IsDefined()\r\n  @IsISO8601(\r\n    { strict: true },\r\n    { message: 'A data de início deve estar no formato ISO 8601' },\r\n  )\r\n  DateTo: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  LimitPerPage?: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  Page?: string;\r\n\r\n  @IsOptional()\r\n  @IsEnum(TransactionMovementTypeEnum)\r\n  transactionType?: TransactionMovementTypeEnum;\r\n\r\n  @IsDefined()\r\n  @IsUUID()\r\n  accountId: string;\r\n}\r\n"]}