{"version": 3, "file": "1717521672731-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1717521672731-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAanC,CAAC;IAXQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,gEAAgE,CACjE,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,sDAAsD,CACvD,CAAC;IACJ,CAAC;CACF;AAdD,0DAcC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1717521672731 implements MigrationInterface {\r\n  name = 'Migrations1717521672731';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"document\" ADD \"social_contract\" character varying`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"document\" DROP COLUMN \"social_contract\"`,\r\n    );\r\n  }\r\n}\r\n"]}