"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1706648182227 = void 0;
class Migrations1706648182227 {
    constructor() {
        this.name = 'Migrations1706648182227';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "owner" ALTER COLUMN "temporary_password" SET DEFAULT true`);
        await queryRunner.query(`ALTER TABLE "owner" ALTER COLUMN "refresh_token" DROP NOT NULL`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "owner" ALTER COLUMN "refresh_token" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "owner" ALTER COLUMN "temporary_password" DROP DEFAULT`);
    }
}
exports.Migrations1706648182227 = Migrations1706648182227;
//# sourceMappingURL=1706648182227-migrations.js.map