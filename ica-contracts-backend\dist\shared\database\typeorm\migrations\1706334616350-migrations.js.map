{"version": 3, "file": "1706334616350-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1706334616350-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAqBnC,CAAC;IAnBQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,+DAA+D,CAChE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,+DAA+D,CAChE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,oEAAoE,CACrE,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAC;QAC3E,MAAM,WAAW,CAAC,KAAK,CACrB,sDAAsD,CACvD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;IACxE,CAAC;CACF;AAtBD,0DAsBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1706334616350 implements MigrationInterface {\r\n  name = 'Migrations1706334616350';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner\" ADD \"password\" character varying NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner\" ADD \"temporary_password\" boolean NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner\" ADD \"refresh_token\" character varying NOT NULL`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(`ALTER TABLE \"owner\" DROP COLUMN \"refresh_token\"`);\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner\" DROP COLUMN \"temporary_password\"`,\r\n    );\r\n    await queryRunner.query(`ALTER TABLE \"owner\" DROP COLUMN \"password\"`);\r\n  }\r\n}\r\n"]}