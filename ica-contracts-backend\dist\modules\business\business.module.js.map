{"version": 3, "file": "business.module.js", "sourceRoot": "/", "sources": ["modules/business/business.module.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAAwC;AACxC,8DAAwD;AAExD,gFAA2E;AAQpE,IAAM,cAAc,GAApB,MAAM,cAAc;CAAG,CAAA;AAAjB,wCAAc;yBAAd,cAAc;IAN1B,IAAA,eAAM,EAAC;QACN,OAAO,EAAE,CAAC,4BAAY,CAAC;QACvB,SAAS,EAAE,CAAC,+CAAqB,CAAC;QAClC,WAAW,EAAE,EAAE;QACf,OAAO,EAAE,EAAE;KACZ,CAAC;GACW,cAAc,CAAG", "sourcesContent": ["import { Module } from '@nestjs/common';\r\nimport { SharedModule } from 'src/shared/shared.module';\r\n\r\nimport { CreateBusinessService } from './services/create-business.service';\r\n\r\n@Module({\r\n  imports: [SharedModule],\r\n  providers: [CreateBusinessService],\r\n  controllers: [],\r\n  exports: [],\r\n})\r\nexport class BusinessModule {}\r\n"]}