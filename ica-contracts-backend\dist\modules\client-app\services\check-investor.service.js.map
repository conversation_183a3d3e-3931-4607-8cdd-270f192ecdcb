{"version": 3, "file": "check-investor.service.js", "sourceRoot": "/", "sources": ["modules/client-app/services/check-investor.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA,2CAA4C;AAC5C,6CAAmD;AACnD,+FAAsF;AACtF,yFAAgF;AAChF,iEAAwD;AACxD,qCAAqC;AAM9B,IAAM,oBAAoB,GAA1B,MAAM,oBAAoB;IAC/B,YAEU,OAAgC,EAEhC,UAAsC;QAFtC,YAAO,GAAP,OAAO,CAAyB;QAEhC,eAAU,GAAV,UAAU,CAA4B;IAC7C,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,IAAsB;QAClC,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;QAC1B,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAE9C,IAAI,YAAY,KAAK,KAAK,EAAE,CAAC;YAC3B,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;gBACvC,SAAS,EAAE,CAAC,mBAAmB,EAAE,wBAAwB,CAAC;gBAC1D,KAAK,EAAE;oBACL,GAAG,EAAE,QAAQ;iBACd;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;YAC3B,CAAC;YAGD,MAAM,eAAe,GAAG,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAClD,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAS,CAAC,QAAQ,CACxD,CAAC;YAEF,IAAI,CAAC,eAAe,EAAE,CAAC;gBACrB,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;YAC3B,CAAC;YAED,OAAO;gBACL,MAAM,EAAE,IAAI;gBACZ,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,KAAK,EAAE,KAAK,CAAC,KAAK;aACnB,CAAC;QACJ,CAAC;aAAM,IAAI,YAAY,KAAK,MAAM,EAAE,CAAC;YACnC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;gBAC7C,SAAS,EAAE,CAAC,mBAAmB,EAAE,wBAAwB,CAAC;gBAC1D,KAAK,EAAE;oBACL,IAAI,EAAE,QAAQ;iBACf;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;YAC3B,CAAC;YAGD,MAAM,eAAe,GAAG,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CACrD,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAS,CAAC,QAAQ,CACxD,CAAC;YAEF,IAAI,CAAC,eAAe,EAAE,CAAC;gBACrB,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;YAC3B,CAAC;YAED,OAAO;gBACL,MAAM,EAAE,IAAI;gBACZ,IAAI,EAAE,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,WAAW;gBAClD,KAAK,EAAE,QAAQ,CAAC,KAAK;aACtB,CAAC;QACJ,CAAC;QAED,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;IAC3B,CAAC;IAEO,SAAS,CAAC,QAAgB;QAChC,IAAI,QAAQ,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACzB,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;CACF,CAAA;AA3EY,oDAAoB;+BAApB,oBAAoB;IADhC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,0BAAW,CAAC,CAAA;IAE7B,WAAA,IAAA,0BAAgB,EAAC,gCAAc,CAAC,CAAA;qCADhB,oBAAU;QAEP,oBAAU;GALrB,oBAAoB,CA2EhC", "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unsafe-enum-comparison */\r\nimport { Injectable } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { BusinessEntity } from 'src/shared/database/typeorm/entities/business.entity';\r\nimport { OwnerEntity } from 'src/shared/database/typeorm/entities/owner.entity';\r\nimport { RolesEnum } from 'src/shared/enums/roles.enum';\r\nimport { Repository } from 'typeorm';\r\n\r\nimport { CheckInvestorDto } from '../dto/check-investor.dto';\r\nimport { ICheckInvestorResponse } from '../responses/check-investor.response';\r\n\r\n@Injectable()\r\nexport class CheckInvestorService {\r\n  constructor(\r\n    @InjectRepository(OwnerEntity)\r\n    private ownerDb: Repository<OwnerEntity>,\r\n    @InjectRepository(BusinessEntity)\r\n    private businessDb: Repository<BusinessEntity>,\r\n  ) {}\r\n\r\n  async perform(data: CheckInvestorDto): Promise<ICheckInvestorResponse> {\r\n    const { document } = data;\r\n    const typeDocument = this.cpfOrCnpj(document);\r\n\r\n    if (typeDocument === 'cpf') {\r\n      const owner = await this.ownerDb.findOne({\r\n        relations: ['ownerRoleRelation', 'ownerRoleRelation.role'],\r\n        where: {\r\n          cpf: document,\r\n        },\r\n      });\r\n\r\n      if (!owner) {\r\n        return { exists: false };\r\n      }\r\n\r\n      // Verificar se o proprietário tem role de investidor\r\n      const hasInvestorRole = owner.ownerRoleRelation.some(\r\n        (relation) => relation.role.name === RolesEnum.INVESTOR,\r\n      );\r\n\r\n      if (!hasInvestorRole) {\r\n        return { exists: false };\r\n      }\r\n\r\n      return {\r\n        exists: true,\r\n        name: owner.name,\r\n        email: owner.email,\r\n      };\r\n    } else if (typeDocument === 'cnpj') {\r\n      const business = await this.businessDb.findOne({\r\n        relations: ['ownerRoleRelation', 'ownerRoleRelation.role'],\r\n        where: {\r\n          cnpj: document,\r\n        },\r\n      });\r\n\r\n      if (!business) {\r\n        return { exists: false };\r\n      }\r\n\r\n      // Verificar se o negócio tem role de investidor\r\n      const hasInvestorRole = business.ownerRoleRelation.some(\r\n        (relation) => relation.role.name === RolesEnum.INVESTOR,\r\n      );\r\n\r\n      if (!hasInvestorRole) {\r\n        return { exists: false };\r\n      }\r\n\r\n      return {\r\n        exists: true,\r\n        name: business.companyName || business.fantasyName,\r\n        email: business.email,\r\n      };\r\n    }\r\n\r\n    return { exists: false };\r\n  }\r\n\r\n  private cpfOrCnpj(document: string): string {\r\n    if (document.length > 11) {\r\n      return 'cnpj';\r\n    }\r\n    return 'cpf';\r\n  }\r\n}\r\n"]}