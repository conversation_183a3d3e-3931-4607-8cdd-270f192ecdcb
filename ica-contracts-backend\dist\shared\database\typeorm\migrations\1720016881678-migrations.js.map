{"version": 3, "file": "1720016881678-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1720016881678-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAyBnC,CAAC;IAvBQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,wFAAwF,CACzF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wFAAwF,CACzF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,qLAAqL,CACtL,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,wFAAwF,CACzF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,6GAA6G,CAC9G,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,qLAAqL,CACtL,CAAC;IACJ,CAAC;CACF;AA1BD,0DA0BC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1720016881678 implements MigrationInterface {\r\n  name = 'Migrations1720016881678';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" DROP CONSTRAINT \"FK_51fc575749fb624f8debaab8c5c\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" DROP CONSTRAINT \"REL_51fc575749fb624f8debaab8c5\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" ADD CONSTRAINT \"FK_51fc575749fb624f8debaab8c5c\" FOREIGN KEY (\"account_id\") REFERENCES \"account\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" DROP CONSTRAINT \"FK_51fc575749fb624f8debaab8c5c\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" ADD CONSTRAINT \"REL_51fc575749fb624f8debaab8c5\" UNIQUE (\"account_id\")`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" ADD CONSTRAINT \"FK_51fc575749fb624f8debaab8c5c\" FOREIGN KEY (\"account_id\") REFERENCES \"account\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n}\r\n"]}