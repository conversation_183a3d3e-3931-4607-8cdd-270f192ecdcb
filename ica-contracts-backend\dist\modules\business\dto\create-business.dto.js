"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateBusinessRequestDto = void 0;
const brazilian_class_validator_1 = require("brazilian-class-validator");
const class_validator_1 = require("class-validator");
class CreateBusinessRequestDto {
}
exports.CreateBusinessRequestDto = CreateBusinessRequestDto;
__decorate([
    (0, class_validator_1.IsDefined)({ message: 'O nome é obrigatorio' }),
    (0, class_validator_1.IsString)({ message: 'O nome deve ser uma string' }),
    __metadata("design:type", String)
], CreateBusinessRequestDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsDefined)({ message: 'O CPF é obrigatório' }),
    (0, class_validator_1.IsString)({ message: 'O CPF deve ser uma string' }),
    (0, brazilian_class_validator_1.IsCPF)({ message: 'O CPF deve ser um CPF válido' }),
    __metadata("design:type", String)
], CreateBusinessRequestDto.prototype, "cpf", void 0);
__decorate([
    (0, class_validator_1.IsDefined)({ message: 'O e-mail é obrigatório' }),
    (0, class_validator_1.IsString)({ message: 'O e-mail deve ser uma string' }),
    (0, class_validator_1.IsEmail)({}, { message: 'O e-mail deve ser um endereço de e-mail válido' }),
    __metadata("design:type", String)
], CreateBusinessRequestDto.prototype, "email", void 0);
__decorate([
    (0, class_validator_1.IsDefined)({ message: 'O CNPJ é obrigatório' }),
    (0, class_validator_1.IsString)({ message: 'O CNPJ deve ser uma string' }),
    (0, brazilian_class_validator_1.IsCNPJ)({ message: 'O CNPJ deve ser um CNPJ válido' }),
    __metadata("design:type", String)
], CreateBusinessRequestDto.prototype, "cnpj", void 0);
__decorate([
    (0, class_validator_1.IsDefined)({ message: 'O nome da empresa é obrigatório' }),
    (0, class_validator_1.IsString)({ message: 'O nome da empresa deve ser uma string' }),
    __metadata("design:type", String)
], CreateBusinessRequestDto.prototype, "companyName", void 0);
//# sourceMappingURL=create-business.dto.js.map