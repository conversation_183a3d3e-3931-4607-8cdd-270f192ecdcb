"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateCelcoinWebhookService = void 0;
const common_1 = require("@nestjs/common");
const update_webhook_service_1 = require("../../../apis/celcoin/services/update-webhook.service");
let UpdateCelcoinWebhookService = class UpdateCelcoinWebhookService {
    constructor(updateWebhooksService) {
        this.updateWebhooksService = updateWebhooksService;
    }
    async perform(input) {
        return this.updateWebhooksService.perform(input, input.entity);
    }
};
exports.UpdateCelcoinWebhookService = UpdateCelcoinWebhookService;
exports.UpdateCelcoinWebhookService = UpdateCelcoinWebhookService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [update_webhook_service_1.UpdateWebhookService])
], UpdateCelcoinWebhookService);
//# sourceMappingURL=update-celcoin-webhook.service.js.map