"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditContractService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const contract_audit_entity_1 = require("../../../shared/database/typeorm/entities/contract-audit.entity");
const contract_entity_1 = require("../../../shared/database/typeorm/entities/contract.entity");
const owner_role_relation_entity_1 = require("../../../shared/database/typeorm/entities/owner-role-relation.entity");
const contract_status_enum_1 = require("../../../shared/enums/contract-status.enum");
const roles_enum_1 = require("../../../shared/enums/roles.enum");
const typeorm_2 = require("typeorm");
const request_investor_credentials_service_1 = require("../../../apis/ica-contract-service/services/request-investor-credentials.service");
let AuditContractService = class AuditContractService {
    constructor(contractAuditRepository, contractRepository, ownerRoleRelationRepository, requestInvestorCredentialsService) {
        this.contractAuditRepository = contractAuditRepository;
        this.contractRepository = contractRepository;
        this.ownerRoleRelationRepository = ownerRoleRelationRepository;
        this.requestInvestorCredentialsService = requestInvestorCredentialsService;
    }
    async auditContract(data, auditorId) {
        const contract = await this.contractRepository.findOne({
            where: { id: data.contractId },
            relations: {
                ownerRoleRelation: true,
                investor: true,
            },
        });
        if (!contract) {
            throw new common_1.NotFoundException('Contrato não encontrado');
        }
        if (contract.status !== contract_status_enum_1.ContractStatusEnum.AWAITING_AUDIT) {
            throw new common_1.UnprocessableEntityException('Contrato não está aguardando análise da auditoria');
        }
        const auditor = await this.ownerRoleRelationRepository.findOne({
            where: [
                {
                    ownerId: auditorId,
                    role: { name: (0, typeorm_2.In)([roles_enum_1.RolesEnum.SUPERADMIN, roles_enum_1.RolesEnum.ADMIN]) },
                },
                {
                    businessId: auditorId,
                    role: { name: (0, typeorm_2.In)([roles_enum_1.RolesEnum.SUPERADMIN, roles_enum_1.RolesEnum.ADMIN]) },
                },
            ],
            relations: { role: true },
        });
        if (!auditor) {
            throw new common_1.NotFoundException('Auditor não encontrado');
        }
        if (data.decision === contract_audit_entity_1.AuditDecisionEnum.REJECTED &&
            (!data.rejectionReasons ||
                Object.keys(data.rejectionReasons).length === 0)) {
            throw new common_1.BadRequestException('É necessário fornecer pelo menos um motivo para a rejeição do contrato');
        }
        const audit = this.contractAuditRepository.create({
            contractId: data.contractId,
            auditorId: auditor.id,
            decision: data.decision,
            comments: data.comments,
            rejectionReasons: data.decision === contract_audit_entity_1.AuditDecisionEnum.REJECTED
                ? data.rejectionReasons
                : null,
        });
        if (data.decision === contract_audit_entity_1.AuditDecisionEnum.APPROVED) {
            contract.status = contract_status_enum_1.ContractStatusEnum.ACTIVE;
        }
        else {
            contract.status = contract_status_enum_1.ContractStatusEnum.REJECTED_BY_AUDIT;
        }
        await this.contractRepository.update(contract.id, {
            status: contract.status,
        });
        const savedAudit = await this.contractAuditRepository.save(audit);
        if (savedAudit.decision === contract_audit_entity_1.AuditDecisionEnum.APPROVED) {
            await this.requestInvestorCredentialsService.perform({
                contractId: data.contractId,
            });
        }
        return {
            id: savedAudit.id,
            contractId: data.contractId,
            decision: data.decision,
            comments: data.comments,
            rejectionReasons: data.rejectionReasons,
            createdAt: savedAudit.createdAt,
        };
    }
};
exports.AuditContractService = AuditContractService;
exports.AuditContractService = AuditContractService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(contract_audit_entity_1.ContractAuditEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(contract_entity_1.ContractEntity)),
    __param(2, (0, typeorm_1.InjectRepository)(owner_role_relation_entity_1.OwnerRoleRelationEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        request_investor_credentials_service_1.RequestInvestorCredentialsService])
], AuditContractService);
//# sourceMappingURL=audit-contract.service.js.map