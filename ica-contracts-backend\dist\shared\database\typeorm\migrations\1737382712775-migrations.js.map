{"version": 3, "file": "1737382712775-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1737382712775-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IA6DnC,CAAC;IA3DQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,yEAAyE,CAC1E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,iFAAiF,CAClF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,iFAAiF,CAClF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,iFAAiF,CAClF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wEAAwE,CACzE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,uEAAuE,CACxE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,qHAAqH,CACtH,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,8KAA8K,CAC/K,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wLAAwL,CACzL,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,iFAAiF,CAClF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,iFAAiF,CAClF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,iFAAiF,CAClF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wEAAwE,CACzE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,yEAAyE,CAC1E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,qHAAqH,CACtH,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wLAAwL,CACzL,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,8KAA8K,CAC/K,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,sDAAsD,CACvD,CAAC;IACJ,CAAC;CACF;AA9DD,0DA8DC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1737382712775 implements MigrationInterface {\r\n  name = 'Migrations1737382712775';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"notification\" ADD \"contract_id\" character varying NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract_advisor\" DROP CONSTRAINT \"FK_b3eb1d7d01d429b1fc2ae37c05b\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract_advisor\" DROP CONSTRAINT \"FK_cba07ac9ce330bc89efb1e82786\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract_advisor\" DROP CONSTRAINT \"UQ_57ddfe73a0f966bba89f031cc9d\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract_advisor\" ALTER COLUMN \"id_contract\" SET NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract_advisor\" ALTER COLUMN \"id_advisor\" SET NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract_advisor\" ADD CONSTRAINT \"UQ_57ddfe73a0f966bba89f031cc9d\" UNIQUE (\"id_contract\", \"id_advisor\")`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract_advisor\" ADD CONSTRAINT \"FK_b3eb1d7d01d429b1fc2ae37c05b\" FOREIGN KEY (\"id_contract\") REFERENCES \"contract\"(\"id\") ON DELETE CASCADE ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract_advisor\" ADD CONSTRAINT \"FK_cba07ac9ce330bc89efb1e82786\" FOREIGN KEY (\"id_advisor\") REFERENCES \"owner_role_relation\"(\"id\") ON DELETE CASCADE ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract_advisor\" DROP CONSTRAINT \"FK_cba07ac9ce330bc89efb1e82786\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract_advisor\" DROP CONSTRAINT \"FK_b3eb1d7d01d429b1fc2ae37c05b\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract_advisor\" DROP CONSTRAINT \"UQ_57ddfe73a0f966bba89f031cc9d\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract_advisor\" ALTER COLUMN \"id_advisor\" DROP NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract_advisor\" ALTER COLUMN \"id_contract\" DROP NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract_advisor\" ADD CONSTRAINT \"UQ_57ddfe73a0f966bba89f031cc9d\" UNIQUE (\"id_contract\", \"id_advisor\")`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract_advisor\" ADD CONSTRAINT \"FK_cba07ac9ce330bc89efb1e82786\" FOREIGN KEY (\"id_advisor\") REFERENCES \"owner_role_relation\"(\"id\") ON DELETE CASCADE ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract_advisor\" ADD CONSTRAINT \"FK_b3eb1d7d01d429b1fc2ae37c05b\" FOREIGN KEY (\"id_contract\") REFERENCES \"contract\"(\"id\") ON DELETE CASCADE ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"notification\" DROP COLUMN \"contract_id\"`,\r\n    );\r\n  }\r\n}\r\n"]}