"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations************* = void 0;
class Migrations************* {
    constructor() {
        this.name = 'Migrations*************';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" ADD "general_transfer_limit" numeric DEFAULT '50000'`);
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" ADD "default_values" character varying`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" DROP COLUMN "default_values"`);
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" DROP COLUMN "general_transfer_limit"`);
    }
}
exports.Migrations************* = Migrations*************;
//# sourceMappingURL=*************-migrations.js.map