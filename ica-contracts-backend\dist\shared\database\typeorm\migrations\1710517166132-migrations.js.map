{"version": 3, "file": "1710517166132-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1710517166132-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAuDnC,CAAC;IArDQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,oFAAoF,CACrF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,yEAAyE,CAC1E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,uDAAuD,CACxD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,6DAA6D,CAC9D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,0DAA0D,CAC3D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,qDAAqD,CACtD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,2DAA2D,CAC5D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,6KAA6K,CAC9K,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,oFAAoF,CACrF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,0DAA0D,CAC3D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,sDAAsD,CACvD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,2DAA2D,CAC5D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,8DAA8D,CAC/D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wDAAwD,CACzD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wEAAwE,CACzE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,6KAA6K,CAC9K,CAAC;IACJ,CAAC;CACF;AAxDD,0DAwDC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1710517166132 implements MigrationInterface {\r\n  name = 'Migrations1710517166132';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner_role_relation\" DROP CONSTRAINT \"FK_622c8e2dabdd04a18f9bc656570\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner_role_relation\" ALTER COLUMN \"owner_id\" DROP NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner\" ALTER COLUMN \"phone\" SET NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner\" ALTER COLUMN \"mother_name\" SET NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner\" ALTER COLUMN \"dt_birth\" SET NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner\" ALTER COLUMN \"pep\" SET NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner\" ALTER COLUMN \"password\" DROP NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner_role_relation\" ADD CONSTRAINT \"FK_622c8e2dabdd04a18f9bc656570\" FOREIGN KEY (\"owner_id\") REFERENCES \"owner\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner_role_relation\" DROP CONSTRAINT \"FK_622c8e2dabdd04a18f9bc656570\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner\" ALTER COLUMN \"password\" SET NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner\" ALTER COLUMN \"pep\" DROP NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner\" ALTER COLUMN \"dt_birth\" DROP NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner\" ALTER COLUMN \"mother_name\" DROP NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner\" ALTER COLUMN \"phone\" DROP NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner_role_relation\" ALTER COLUMN \"owner_id\" SET NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner_role_relation\" ADD CONSTRAINT \"FK_622c8e2dabdd04a18f9bc656570\" FOREIGN KEY (\"owner_id\") REFERENCES \"owner\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n}\r\n"]}