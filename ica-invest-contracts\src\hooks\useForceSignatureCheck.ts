import { useState } from 'react';
import { toast } from 'react-toastify';
import { forceSignatureCheck, ForceSignatureCheckResponse } from '@/services/force-signature-check.service';

interface UseForceSignatureCheckReturn {
  isLoading: boolean;
  checkSignatureStatus: (contractId: string) => Promise<ForceSignatureCheckResponse | null>;
}

export const useForceSignatureCheck = (): UseForceSignatureCheckReturn => {
  const [isLoading, setIsLoading] = useState(false);

  const checkSignatureStatus = async (contractId: string): Promise<ForceSignatureCheckResponse | null> => {
    setIsLoading(true);
    
    try {
      const response = await forceSignatureCheck(contractId);
      
      toast.success(`Status atualizado: ${response.status}`);
      return response;
    } catch (error: any) {
      console.error('Erro ao verificar status de assinatura:', error);
      
      const errorMessage = error?.response?.data?.message || 
                          error?.message || 
                          'Erro ao verificar status de assinatura';
      
      toast.error(errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isLoading,
    checkSignatureStatus,
  };
};
