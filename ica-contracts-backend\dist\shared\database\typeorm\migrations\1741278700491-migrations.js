"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1741278700491 = void 0;
class Migrations1741278700491 {
    constructor() {
        this.name = 'Migrations1741278700491';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "pre_register" DROP CONSTRAINT "FK_db44bd7010cefe5ae4a9bb5506f"`);
        await queryRunner.query(`ALTER TABLE "pre_register" ALTER COLUMN "adviser_id" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "pre_register" ADD CONSTRAINT "FK_db44bd7010cefe5ae4a9bb5506f" FOREIGN KEY ("adviser_id") REFERENCES "owner_role_relation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "pre_register" DROP CONSTRAINT "FK_db44bd7010cefe5ae4a9bb5506f"`);
        await queryRunner.query(`ALTER TABLE "pre_register" ALTER COLUMN "adviser_id" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "pre_register" ADD CONSTRAINT "FK_db44bd7010cefe5ae4a9bb5506f" FOREIGN KEY ("adviser_id") REFERENCES "owner_role_relation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
}
exports.Migrations1741278700491 = Migrations1741278700491;
//# sourceMappingURL=1741278700491-migrations.js.map