"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1715797111306 = void 0;
class Migrations1715797111306 {
    constructor() {
        this.name = 'Migrations1715797111306';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "owner" ADD "tax_rate" numeric`);
        await queryRunner.query(`ALTER TABLE "business" ADD "tax_rate" numeric`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "business" DROP COLUMN "tax_rate"`);
        await queryRunner.query(`ALTER TABLE "owner" DROP COLUMN "tax_rate"`);
    }
}
exports.Migrations1715797111306 = Migrations1715797111306;
//# sourceMappingURL=1715797111306-migrations.js.map