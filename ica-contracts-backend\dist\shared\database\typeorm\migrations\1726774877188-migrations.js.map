{"version": 3, "file": "1726774877188-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1726774877188-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAiBnC,CAAC;IAfQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,0cAA0c,CAC3c,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,yLAAyL,CAC1L,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,uEAAuE,CACxE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;IACjD,CAAC;CACF;AAlBD,0DAkBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1726774877188 implements MigrationInterface {\r\n  name = 'Migrations1726774877188';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"income\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"income_day\" integer, \"payment_date\" TIMESTAMP, \"amount\" integer, \"percentage\" integer, \"number_contracts\" integer, \"number_clients\" integer, \"contract_type\" character varying, \"role\" character varying, \"createdAt\" TIMESTAMP NOT NULL DEFAULT now(), \"updatedAt\" TIMESTAMP NOT NULL DEFAULT now(), \"owner_role_relation\" uuid, CONSTRAINT \"PK_29a10f17b97568f70cee8586d58\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"income\" ADD CONSTRAINT \"FK_2542605a11a32fcc24bd4c14a58\" FOREIGN KEY (\"owner_role_relation\") REFERENCES \"owner_role_relation\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"income\" DROP CONSTRAINT \"FK_2542605a11a32fcc24bd4c14a58\"`,\r\n    );\r\n    await queryRunner.query(`DROP TABLE \"income\"`);\r\n  }\r\n}\r\n"]}