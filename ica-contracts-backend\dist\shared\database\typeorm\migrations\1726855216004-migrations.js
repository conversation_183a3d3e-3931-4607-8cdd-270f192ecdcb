"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1726855216004 = void 0;
class Migrations1726855216004 {
    constructor() {
        this.name = 'Migrations1726855216004';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "contract" ADD "start_contract" date NOT NULL DEFAULT '"2024-09-20T18:00:18.619Z"'`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "end_contract" date NOT NULL DEFAULT '"2024-09-20T18:00:18.619Z"'`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "end_contract"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "start_contract"`);
    }
}
exports.Migrations1726855216004 = Migrations1726855216004;
//# sourceMappingURL=1726855216004-migrations.js.map