{"version": 3, "file": "kyc-celcoin-webhook.service.js", "sourceRoot": "/", "sources": ["modules/webhook/services/kyc-celcoin-webhook.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6CAAmD;AACnD,iCAAiC;AACjC,6FAAoF;AACpF,+FAAsF;AACtF,yFAAgF;AAChF,uEAA8D;AAC9D,mFAAyE;AACzE,mFAA0E;AAC1E,qCAAqC;AAK9B,IAAM,wBAAwB,GAA9B,MAAM,wBAAwB;IACnC,YAEU,SAAoC,EAEpC,OAAgC,EAEhC,UAAsC,EAC7B,YAA0B;QALnC,cAAS,GAAT,SAAS,CAA2B;QAEpC,YAAO,GAAP,OAAO,CAAyB;QAEhC,eAAU,GAAV,UAAU,CAA4B;QAC7B,iBAAY,GAAZ,YAAY,CAAc;IAC1C,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,KAA4B;QACxC,IAAI,KAAK,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;YAChC,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CACzB;gBACE,UAAU,EAAE,KAAK,CAAC,IAAI,CAAC,YAAY;aACpC,EACD;gBACE,MAAM,EAAE,uCAAiB,CAAC,QAAQ;aACnC,CACF,CAAC;QACJ,CAAC;QACD,IAAI,KAAK,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;YAChC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;gBAC3C,SAAS,EAAE;oBACT,KAAK,EAAE,IAAI;oBACX,QAAQ,EAAE,IAAI;iBACf;gBACD,KAAK,EAAE;oBACL,UAAU,EAAE,KAAK,CAAC,IAAI,CAAC,YAAY;iBACpC;aACF,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,OAAO,CAAC,CAAC;YAEhD,MAAM,QAAQ,GAAG,IAAA,oCAAgB,GAAE,CAAC;YACpC,MAAM,iBAAiB,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;YAE1D,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC;oBAChC,EAAE,EAAE,OAAO,CAAC,KAAK,EAAE,KAAK,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK;oBAClD,OAAO,EAAE,WAAW;oBACpB,QAAQ,EAAE,SAAS;oBACnB,OAAO,EAAE;wBACP,QAAQ,EAAE,OAAO,CAAC,KAAK,EAAE,GAAG,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI;wBACrD,QAAQ;wBACR,QAAQ,EAAE,OAAO,CAAC,KAAK,EAAE,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,WAAW;qBAC9D;iBACF,CAAC,CAAC;gBAEH,OAAO,CAAC,GAAG,CACT,mCAAmC,EACnC,OAAO,CAAC,KAAK,EAAE,KAAK,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAC/C,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,GAAG,CACT,8BAA8B,OAAO,CAAC,KAAK,EAAE,KAAK,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,EAC9E,KAAK,CACN,CAAC;YACJ,CAAC;YAED,IAAI,OAAO,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;gBAChC,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE;oBACzC,QAAQ,EAAE,iBAAiB;oBAC3B,iBAAiB,EAAE,IAAI;iBACxB,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE;oBAC/C,QAAQ,EAAE,iBAAiB;oBAC3B,iBAAiB,EAAE,IAAI;iBACxB,CAAC,CAAC;YACL,CAAC;YACD,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CACzB;gBACE,UAAU,EAAE,KAAK,CAAC,IAAI,CAAC,YAAY;aACpC,EACD;gBACE,MAAM,EAAE,uCAAiB,CAAC,MAAM;aACjC,CACF,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AAlFY,4DAAwB;mCAAxB,wBAAwB;IADpC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,8BAAa,CAAC,CAAA;IAE/B,WAAA,IAAA,0BAAgB,EAAC,0BAAW,CAAC,CAAA;IAE7B,WAAA,IAAA,0BAAgB,EAAC,gCAAc,CAAC,CAAA;qCAHd,oBAAU;QAEZ,oBAAU;QAEP,oBAAU;QACC,4BAAY;GARlC,wBAAwB,CAkFpC", "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport * as bcrypt from 'bcrypt';\r\nimport { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';\r\nimport { BusinessEntity } from 'src/shared/database/typeorm/entities/business.entity';\r\nimport { OwnerEntity } from 'src/shared/database/typeorm/entities/owner.entity';\r\nimport { EmailService } from 'src/shared/email/email.service';\r\nimport { AccountStatusEnum } from 'src/shared/enums/account-status.enum';\r\nimport { generatePassword } from 'src/shared/functions/generate-password';\r\nimport { Repository } from 'typeorm';\r\n\r\nimport { IKycCelcoinWebhookDto } from '../dto/kyc-celcoin-webhook.dto';\r\n\r\n@Injectable()\r\nexport class KycCelcoinWebhookService {\r\n  constructor(\r\n    @InjectRepository(AccountEntity)\r\n    private accountDb: Repository<AccountEntity>,\r\n    @InjectRepository(OwnerEntity)\r\n    private ownerDb: Repository<OwnerEntity>,\r\n    @InjectRepository(BusinessEntity)\r\n    private businessDb: Repository<BusinessEntity>,\r\n    private readonly emailService: EmailService,\r\n  ) {}\r\n\r\n  async perform(input: IKycCelcoinWebhookDto) {\r\n    if (input.status === 'REJECTED') {\r\n      await this.accountDb.update(\r\n        {\r\n          externalId: input.body.onboardingId,\r\n        },\r\n        {\r\n          status: AccountStatusEnum.REPROVED,\r\n        },\r\n      );\r\n    }\r\n    if (input.status === 'APPROVED') {\r\n      const account = await this.accountDb.findOne({\r\n        relations: {\r\n          owner: true,\r\n          business: true,\r\n        },\r\n        where: {\r\n          externalId: input.body.onboardingId,\r\n        },\r\n      });\r\n\r\n      console.log('KYC aprovado para conta', account);\r\n\r\n      const password = generatePassword();\r\n      const passwordEncrypted = await bcrypt.hash(password, 10);\r\n\r\n      try {\r\n        await this.emailService.sendEmail({\r\n          to: account.owner?.email || account.business.email,\r\n          subject: 'bem-vindo',\r\n          template: 'welcome',\r\n          context: {\r\n            document: account.owner?.cpf || account.business.cnpj,\r\n            password,\r\n            username: account.owner?.name || account.business.companyName,\r\n          },\r\n        });\r\n\r\n        console.log(\r\n          'Enviado e-mail com sucesso para: ',\r\n          account.owner?.email || account.business.email,\r\n        );\r\n      } catch (error) {\r\n        console.log(\r\n          `Erro ao enviar email para: ${account.owner?.email || account.business.email}`,\r\n          error,\r\n        );\r\n      }\r\n\r\n      if (account.type === 'physical') {\r\n        await this.ownerDb.update(account.ownerId, {\r\n          password: passwordEncrypted,\r\n          temporaryPassword: true,\r\n        });\r\n      } else {\r\n        await this.businessDb.update(account.businessId, {\r\n          password: passwordEncrypted,\r\n          temporaryPassword: true,\r\n        });\r\n      }\r\n      await this.accountDb.update(\r\n        {\r\n          externalId: input.body.onboardingId,\r\n        },\r\n        {\r\n          status: AccountStatusEnum.ACTIVE,\r\n        },\r\n      );\r\n    }\r\n  }\r\n}\r\n"]}