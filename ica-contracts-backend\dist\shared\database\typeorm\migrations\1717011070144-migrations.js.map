{"version": 3, "file": "1717011070144-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1717011070144-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IA6BnC,CAAC;IA3BQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,uUAAuU,CACxU,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,4EAA4E,CAC7E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,oEAAoE,CACrE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,sKAAsK,CACvK,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,yEAAyE,CAC1E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,8EAA8E,CAC/E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,4FAA4F,CAC7F,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;IACnD,CAAC;CACF;AA9BD,0DA8BC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1717011070144 implements MigrationInterface {\r\n  name = 'Migrations1717011070144';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"recharge\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"account_id\" uuid NOT NULL, \"external_id\" character varying NOT NULL, \"value\" character varying NOT NULL, \"status\" character varying NOT NULL, \"created_at\" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT \"PK_5cc2dad79b630f05df9d7c0df82\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" DROP COLUMN \"general_transfer_limit\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" DROP COLUMN \"default_values\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"recharge\" ADD CONSTRAINT \"FK_73c4afda905a8765218a1ffa1c6\" FOREIGN KEY (\"account_id\") REFERENCES \"account\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"recharge\" DROP CONSTRAINT \"FK_73c4afda905a8765218a1ffa1c6\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" ADD \"default_values\" character varying`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" ADD \"general_transfer_limit\" numeric DEFAULT '50000'`,\r\n    );\r\n    await queryRunner.query(`DROP TABLE \"recharge\"`);\r\n  }\r\n}\r\n"]}