"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1737383392934 = void 0;
class Migrations1737383392934 {
    constructor() {
        this.name = 'Migrations1737383392934';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "notification" DROP COLUMN "contract_id"`);
        await queryRunner.query(`ALTER TABLE "notification" ADD "contract_id" uuid`);
        await queryRunner.query(`ALTER TABLE "notification" ADD CONSTRAINT "FK_f9cd0107e86bcd1d22a4ed7125b" FOREIGN KEY ("contract_id") REFERENCES "contract"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "notification" DROP CONSTRAINT "FK_f9cd0107e86bcd1d22a4ed7125b"`);
        await queryRunner.query(`ALTER TABLE "notification" DROP COLUMN "contract_id"`);
        await queryRunner.query(`ALTER TABLE "notification" ADD "contract_id" character varying NOT NULL`);
    }
}
exports.Migrations1737383392934 = Migrations1737383392934;
//# sourceMappingURL=1737383392934-migrations.js.map