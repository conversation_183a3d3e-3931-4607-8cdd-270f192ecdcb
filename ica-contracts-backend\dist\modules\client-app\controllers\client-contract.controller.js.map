{"version": 3, "file": "client-contract.controller.js", "sourceRoot": "/", "sources": ["modules/client-app/controllers/client-contract.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA4E;AAC5E,0EAAgE;AAOhE,iFAA4E;AAIrE,IAAM,wBAAwB,GAA9B,MAAM,wBAAwB;IACnC,YAA6B,qBAA4C;QAA5C,0BAAqB,GAArB,qBAAqB,CAAuB;IAAG,CAAC;IAGvE,AAAN,KAAK,CAAC,YAAY,CACL,GAA6B;QAExC,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;QAC3B,OAAO,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;IACjE,CAAC;IAGK,AAAN,KAAK,CAAC,oBAAoB,CACX,UAAkB;QAE/B,OAAO,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;IACrE,CAAC;CACF,CAAA;AAjBY,4DAAwB;AAI7B;IADL,IAAA,YAAG,GAAE;IAEH,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;4DAIX;AAGK;IADL,IAAA,YAAG,EAAC,cAAc,CAAC;IAEjB,WAAA,IAAA,cAAK,EAAC,IAAI,CAAC,CAAA;;;;oEAGb;mCAhBU,wBAAwB;IAFpC,IAAA,mBAAU,EAAC,kBAAkB,CAAC;IAC9B,IAAA,kBAAS,EAAC,6BAAY,CAAC;qCAE8B,+CAAqB;GAD9D,wBAAwB,CAiBpC", "sourcesContent": ["import { Controller, Get, Param, UseGuards, Request } from '@nestjs/common';\r\nimport { JwtAuthGuard } from 'src/shared/guards/jwt-auth.guard';\r\nimport { IRequestUser } from 'src/shared/interfaces/request-user.interface';\r\n\r\nimport {\r\n  IClientContractResponse,\r\n  IClientContractAddendumResponse,\r\n} from '../responses/client-contract.response';\r\nimport { ClientContractService } from '../services/client-contract.service';\r\n\r\n@Controller('client/contracts')\r\n@UseGuards(JwtAuthGuard)\r\nexport class ClientContractController {\r\n  constructor(private readonly clientContractService: ClientContractService) {}\r\n\r\n  @Get()\r\n  async getContracts(\r\n    @Request() req: { user: { id: string } },\r\n  ): Promise<IClientContractResponse[]> {\r\n    const userId = req.user.id;\r\n    return this.clientContractService.getContractsByUserId(userId);\r\n  }\r\n\r\n  @Get(':id/addendum')\r\n  async getContractAddendums(\r\n    @Param('id') contractId: string,\r\n  ): Promise<IClientContractAddendumResponse[]> {\r\n    return this.clientContractService.getContractAddendums(contractId);\r\n  }\r\n}\r\n"]}