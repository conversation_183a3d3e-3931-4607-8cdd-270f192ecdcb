{"version": 3, "file": "1713815769604-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1713815769604-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAyBnC,CAAC;IAvBQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,gEAAgE,CACjE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,mDAAmD,CACpD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,0DAA0D,CAC3D,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,mDAAmD,CACpD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,sEAAsE,CACvE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,gEAAgE,CACjE,CAAC;IACJ,CAAC;CACF;AA1BD,0DA0BC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1713815769604 implements MigrationInterface {\r\n  name = 'Migrations1713815769604';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"statistic\" RENAME COLUMN \"jsonb\" TO \"states_data\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"statistic\" DROP COLUMN \"states_data\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"statistic\" ADD \"states_data\" jsonb NOT NULL`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"statistic\" DROP COLUMN \"states_data\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"statistic\" ADD \"states_data\" character varying NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"statistic\" RENAME COLUMN \"states_data\" TO \"jsonb\"`,\r\n    );\r\n  }\r\n}\r\n"]}