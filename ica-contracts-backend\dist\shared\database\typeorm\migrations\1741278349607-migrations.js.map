{"version": 3, "file": "1741278349607-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1741278349607-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACI,SAAI,GAAG,yBAAyB,CAAA;IAoBpC,CAAC;IAlBU,KAAK,CAAC,EAAE,CAAC,WAAwB;QACpC,MAAM,WAAW,CAAC,KAAK,CAAC,6EAA6E,CAAC,CAAC;QACvG,MAAM,WAAW,CAAC,KAAK,CAAC,oEAAoE,CAAC,CAAC;QAC9F,MAAM,WAAW,CAAC,KAAK,CAAC,+EAA+E,CAAC,CAAC;QACzG,MAAM,WAAW,CAAC,KAAK,CAAC,yEAAyE,CAAC,CAAC;QACnG,MAAM,WAAW,CAAC,KAAK,CAAC,4KAA4K,CAAC,CAAC;QACtM,MAAM,WAAW,CAAC,KAAK,CAAC,4LAA4L,CAAC,CAAC;IAC1N,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CAAC,+EAA+E,CAAC,CAAC;QACzG,MAAM,WAAW,CAAC,KAAK,CAAC,6EAA6E,CAAC,CAAC;QACvG,MAAM,WAAW,CAAC,KAAK,CAAC,0EAA0E,CAAC,CAAC;QACpG,MAAM,WAAW,CAAC,KAAK,CAAC,4LAA4L,CAAC,CAAC;QACtN,MAAM,WAAW,CAAC,KAAK,CAAC,qEAAqE,CAAC,CAAC;QAC/F,MAAM,WAAW,CAAC,KAAK,CAAC,4KAA4K,CAAC,CAAC;IAC1M,CAAC;CAEJ;AArBD,0DAqBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from \"typeorm\";\r\n\r\nexport class Migrations1741278349607 implements MigrationInterface {\r\n    name = 'Migrations1741278349607'\r\n\r\n    public async up(queryRunner: QueryRunner): Promise<void> {\r\n        await queryRunner.query(`ALTER TABLE \"notification\" DROP CONSTRAINT \"FK_f9cd0107e86bcd1d22a4ed7125b\"`);\r\n        await queryRunner.query(`ALTER TABLE \"notification\" ALTER COLUMN \"contract_id\" SET NOT NULL`);\r\n        await queryRunner.query(`ALTER TABLE \"contract_event\" DROP CONSTRAINT \"FK_196c2c26c5048d8f6760bbc281e\"`);\r\n        await queryRunner.query(`ALTER TABLE \"contract_event\" ALTER COLUMN \"responsible_id\" SET NOT NULL`);\r\n        await queryRunner.query(`ALTER TABLE \"notification\" ADD CONSTRAINT \"FK_f9cd0107e86bcd1d22a4ed7125b\" FOREIGN KEY (\"contract_id\") REFERENCES \"contract\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`);\r\n        await queryRunner.query(`ALTER TABLE \"contract_event\" ADD CONSTRAINT \"FK_196c2c26c5048d8f6760bbc281e\" FOREIGN KEY (\"responsible_id\") REFERENCES \"owner_role_relation\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`);\r\n    }\r\n\r\n    public async down(queryRunner: QueryRunner): Promise<void> {\r\n        await queryRunner.query(`ALTER TABLE \"contract_event\" DROP CONSTRAINT \"FK_196c2c26c5048d8f6760bbc281e\"`);\r\n        await queryRunner.query(`ALTER TABLE \"notification\" DROP CONSTRAINT \"FK_f9cd0107e86bcd1d22a4ed7125b\"`);\r\n        await queryRunner.query(`ALTER TABLE \"contract_event\" ALTER COLUMN \"responsible_id\" DROP NOT NULL`);\r\n        await queryRunner.query(`ALTER TABLE \"contract_event\" ADD CONSTRAINT \"FK_196c2c26c5048d8f6760bbc281e\" FOREIGN KEY (\"responsible_id\") REFERENCES \"owner_role_relation\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`);\r\n        await queryRunner.query(`ALTER TABLE \"notification\" ALTER COLUMN \"contract_id\" DROP NOT NULL`);\r\n        await queryRunner.query(`ALTER TABLE \"notification\" ADD CONSTRAINT \"FK_f9cd0107e86bcd1d22a4ed7125b\" FOREIGN KEY (\"contract_id\") REFERENCES \"contract\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`);\r\n    }\r\n\r\n}\r\n"]}