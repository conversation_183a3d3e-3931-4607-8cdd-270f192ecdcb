{"version": 3, "file": "pix-cashout-celcoin-webhook.service.js", "sourceRoot": "/", "sources": ["modules/webhook/services/pix-cashout-celcoin-webhook.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6CAAmD;AACnD,qGAA4F;AAC5F,2FAAiF;AACjF,qCAAqC;AAK9B,IAAM,+BAA+B,GAArC,MAAM,+BAA+B;IAC1C,YAEU,aAA4C;QAA5C,kBAAa,GAAb,aAAa,CAA+B;IACnD,CAAC;IACJ,KAAK,CAAC,OAAO,CAAC,IAA+B;QAC3C,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;YACnD,KAAK,EAAE;gBACL,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU;aAC3B;SACF,CAAC,CAAC;QAEH,IAAI,WAAW,EAAE,CAAC;YAChB,IAAI,IAAI,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;gBAChC,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE,EAAE;oBAC9C,MAAM,EAAE,+CAAqB,CAAC,IAAI;iBACnC,CAAC,CAAC;YACL,CAAC;YACD,IAAI,IAAI,CAAC,MAAM,KAAK,OAAO,EAAE,CAAC;gBAC5B,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE,EAAE;oBAC9C,MAAM,EAAE,+CAAqB,CAAC,KAAK;iBACpC,CAAC,CAAC;YACL,CAAC;YACD,IAAI,IAAI,CAAC,MAAM,KAAK,YAAY,EAAE,CAAC;gBACjC,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE,EAAE;oBAC9C,MAAM,EAAE,+CAAqB,CAAC,OAAO;iBACtC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;CACF,CAAA;AA9BY,0EAA+B;0CAA/B,+BAA+B;IAD3C,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,sCAAiB,CAAC,CAAA;qCACb,oBAAU;GAHxB,+BAA+B,CA8B3C", "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { TransactionEntity } from 'src/shared/database/typeorm/entities/transaction.entity';\r\nimport { TransactionStatusEnum } from 'src/shared/enums/transaction-status-enum';\r\nimport { Repository } from 'typeorm';\r\n\r\nimport { IPixCashoutCelcoinWebhook } from '../dto/pix-cashout-celcoin-webhook.dto';\r\n\r\n@Injectable()\r\nexport class PixCashoutCelcoinWebhookService {\r\n  constructor(\r\n    @InjectRepository(TransactionEntity)\r\n    private transactionDb: Repository<TransactionEntity>,\r\n  ) {}\r\n  async perform(data: IPixCashoutCelcoinWebhook) {\r\n    const transaction = await this.transactionDb.findOne({\r\n      where: {\r\n        code: data.body.clientCode,\r\n      },\r\n    });\r\n\r\n    if (transaction) {\r\n      if (data.status === 'CONFIRMED') {\r\n        await this.transactionDb.update(transaction.id, {\r\n          status: TransactionStatusEnum.DONE,\r\n        });\r\n      }\r\n      if (data.status === 'ERROR') {\r\n        await this.transactionDb.update(transaction.id, {\r\n          status: TransactionStatusEnum.ERROR,\r\n        });\r\n      }\r\n      if (data.status === 'PROCESSING') {\r\n        await this.transactionDb.update(transaction.id, {\r\n          status: TransactionStatusEnum.PENDENT,\r\n        });\r\n      }\r\n    }\r\n  }\r\n}\r\n"]}