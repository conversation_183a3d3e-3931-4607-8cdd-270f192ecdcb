{"version": 3, "file": "1736209530399-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1736209530399-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAuDnC,CAAC;IArDQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,4cAA4c,CAC7c,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,4FAA4F,CAC7F,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,8EAA8E,CAC/E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,mFAAmF,CACpF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,sEAAsE,CACvE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,8DAA8D,CAC/D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,qEAAqE,CACtE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,mEAAmE,CACpE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,yKAAyK,CAC1K,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,4EAA4E,CAC7E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,yEAAyE,CAC1E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAC;QACxE,MAAM,WAAW,CAAC,KAAK,CACrB,oDAAoD,CACrD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC;QAC1E,MAAM,WAAW,CAAC,KAAK,CACrB,4DAA4D,CAC7D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,oEAAoE,CACrE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,4EAA4E,CAC7E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;IACtD,CAAC;CACF;AAxDD,0DAwDC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1736209530399 implements MigrationInterface {\r\n  name = 'Migrations1736209530399';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"service_fee\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"external_id\" character varying, \"monthly_fee\" numeric NOT NULL, \"fee_accumulation_period\" numeric NOT NULL, \"last_payment_date\" date, \"payment_status\" character varying(50) NOT NULL, \"debt_amount\" numeric, \"created_at\" TIMESTAMP NOT NULL DEFAULT now(), \"updated_at\" TIMESTAMP NOT NULL DEFAULT now(), \"account_id\" uuid, CONSTRAINT \"PK_70d082964aaa4bf193c2b1276e9\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" ADD \"general_transfer_limit\" numeric DEFAULT '50000'`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" ADD \"default_values\" character varying`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" ADD \"active\" boolean NOT NULL DEFAULT false`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account\" ADD \"is_taxable\" boolean NOT NULL DEFAULT true`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account\" ADD \"two_factor_key\" character varying`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account\" ADD \"have_2fa\" boolean NOT NULL DEFAULT false`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"statistic\" ALTER COLUMN \"total_applied\" TYPE numeric`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"service_fee\" ADD CONSTRAINT \"FK_b1553b09af9ce6028995889dffe\" FOREIGN KEY (\"account_id\") REFERENCES \"account\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"service_fee\" DROP CONSTRAINT \"FK_b1553b09af9ce6028995889dffe\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"statistic\" ALTER COLUMN \"total_applied\" TYPE numeric(10,2)`,\r\n    );\r\n    await queryRunner.query(`ALTER TABLE \"account\" DROP COLUMN \"have_2fa\"`);\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account\" DROP COLUMN \"two_factor_key\"`,\r\n    );\r\n    await queryRunner.query(`ALTER TABLE \"account\" DROP COLUMN \"is_taxable\"`);\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" DROP COLUMN \"active\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" DROP COLUMN \"default_values\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" DROP COLUMN \"general_transfer_limit\"`,\r\n    );\r\n    await queryRunner.query(`DROP TABLE \"service_fee\"`);\r\n  }\r\n}\r\n"]}