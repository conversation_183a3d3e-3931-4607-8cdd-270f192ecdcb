{"version": 3, "file": "ica-contract-service.module.js", "sourceRoot": "/", "sources": ["apis/ica-contract-service/ica-contract-service.module.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,yDAAoD;AACpD,2CAAgD;AAEhD,kGAA+F;AAC/F,wFAAkF;AAClF,gFAA8E;AAC9E,gGAA0F;AAC1F,oGAA8F;AAC9F,0GAAoG;AACpG,sGAAmG;AAuB5F,IAAM,wBAAwB,GAA9B,MAAM,wBAAwB;CAAG,CAAA;AAA3B,4DAAwB;mCAAxB,wBAAwB;IAtBpC,IAAA,eAAM,GAAE;IACR,IAAA,eAAM,EAAC;QACN,OAAO,EAAE,CAAC,2BAAW,CAAC,QAAQ,EAAE,CAAC;QACjC,SAAS,EAAE;YACT,kEAA8B;YAC9B,8DAA4B;YAC5B,sDAAwB;YACxB,mEAAgC;YAChC,uEAAkC;YAClC,wEAAiC;YACjC,kDAAwB;SACzB;QACD,OAAO,EAAE;YACP,kEAA8B;YAC9B,8DAA4B;YAC5B,sDAAwB;YACxB,mEAAgC;YAChC,uEAAkC;YAClC,wEAAiC;YACjC,kDAAwB;SACzB;KACF,CAAC;GACW,wBAAwB,CAAG", "sourcesContent": ["import { CacheModule } from '@nestjs/cache-manager';\r\nimport { Global, Module } from '@nestjs/common';\r\n\r\nimport { CreateExistingContractApiService } from './services/create-existing-contract.service';\r\nimport { CreateNewContractService } from './services/create-new-contract.service';\r\nimport { DeleteContractApiService } from './services/delete-contract.service';\r\nimport { DetailsReportInvestorService } from './services/details-report-investor.service';\r\nimport { GenerateReportInvestorsService } from './services/generate-report-investors.service';\r\nimport { RequestInvestorCredentialsService } from './services/request-investor-credentials.service';\r\nimport { ResubmitRejectedContractApiService } from './services/resubmit-rejected-contract.service';\r\n@Global()\r\n@Module({\r\n  imports: [CacheModule.register()],\r\n  providers: [\r\n    GenerateReportInvestorsService,\r\n    DetailsReportInvestorService,\r\n    CreateNewContractService,\r\n    CreateExistingContractApiService,\r\n    ResubmitRejectedContractApiService,\r\n    RequestInvestorCredentialsService,\r\n    DeleteContractApiService,\r\n  ],\r\n  exports: [\r\n    GenerateReportInvestorsService,\r\n    DetailsReportInvestorService,\r\n    CreateNewContractService,\r\n    CreateExistingContractApiService,\r\n    ResubmitRejectedContractApiService,\r\n    RequestInvestorCredentialsService,\r\n    DeleteContractApiService,\r\n  ],\r\n})\r\nexport class IcaContractServiceModule {}\r\n"]}