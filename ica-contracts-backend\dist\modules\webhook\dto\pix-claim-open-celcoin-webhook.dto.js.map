{"version": 3, "file": "pix-claim-open-celcoin-webhook.dto.js", "sourceRoot": "/", "sources": ["modules/webhook/dto/pix-claim-open-celcoin-webhook.dto.ts"], "names": [], "mappings": "", "sourcesContent": ["interface IClaim {\r\n  id: string;\r\n  claimType: string;\r\n  key: string;\r\n  keyType: string;\r\n  claimerAccount: {\r\n    participant: number;\r\n    branch: number;\r\n    account: number;\r\n    accountType: string;\r\n  };\r\n  claimer: {\r\n    type: string;\r\n    taxId: number;\r\n    name: string;\r\n  };\r\n  donorParticipant: number;\r\n  donorAccount: {\r\n    account: number;\r\n    branch: number;\r\n    taxId: number;\r\n    name: string;\r\n  };\r\n  lastModified: string;\r\n  confirmReason?: string;\r\n  cancelReason?: string;\r\n  resolutionPeriodEnd: string;\r\n}\r\n\r\nexport interface IPixDictClaimOpen {\r\n  entity: string;\r\n  createTimestamp: string;\r\n  status: string;\r\n  body: IClaim;\r\n}\r\n"]}