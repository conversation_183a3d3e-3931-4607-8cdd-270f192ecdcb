"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetContractAuditHistoryService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const contract_audit_entity_1 = require("../../../shared/database/typeorm/entities/contract-audit.entity");
const contract_entity_1 = require("../../../shared/database/typeorm/entities/contract.entity");
const owner_role_relation_entity_1 = require("../../../shared/database/typeorm/entities/owner-role-relation.entity");
const typeorm_2 = require("typeorm");
let GetContractAuditHistoryService = class GetContractAuditHistoryService {
    constructor(contractAuditRepository, contractRepository, ownerRoleRelationRepository) {
        this.contractAuditRepository = contractAuditRepository;
        this.contractRepository = contractRepository;
        this.ownerRoleRelationRepository = ownerRoleRelationRepository;
    }
    async execute(contractId, userId) {
        const contract = await this.contractRepository.findOne({
            where: { id: contractId },
            relations: {
                ownerRoleRelation: true,
                investor: true,
            },
        });
        if (!contract) {
            throw new common_1.NotFoundException('Contrato não encontrado');
        }
        const user = await this.ownerRoleRelationRepository.findOne({
            where: [{ ownerId: userId }, { businessId: userId }],
            relations: { role: true },
        });
        if (!user) {
            throw new common_1.UnauthorizedException('Usuário não encontrado');
        }
        const audits = await this.contractAuditRepository.find({
            where: { contractId },
            relations: { auditor: { owner: true } },
            order: { createdAt: 'DESC' },
        });
        return audits.map((audit) => ({
            id: audit.id,
            decision: audit.decision,
            comments: audit.comments,
            rejectionReasons: audit.rejectionReasons,
            auditor: {
                id: audit.auditor.id,
                name: audit.auditor.owner?.name,
            },
            createdAt: audit.createdAt,
        }));
    }
};
exports.GetContractAuditHistoryService = GetContractAuditHistoryService;
exports.GetContractAuditHistoryService = GetContractAuditHistoryService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(contract_audit_entity_1.ContractAuditEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(contract_entity_1.ContractEntity)),
    __param(2, (0, typeorm_1.InjectRepository)(owner_role_relation_entity_1.OwnerRoleRelationEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], GetContractAuditHistoryService);
//# sourceMappingURL=get-contract-audit-history.service.js.map