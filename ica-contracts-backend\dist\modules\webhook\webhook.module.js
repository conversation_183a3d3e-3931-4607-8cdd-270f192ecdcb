"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebhookModule = void 0;
const common_1 = require("@nestjs/common");
const apis_module_1 = require("../../apis/apis.module");
const shared_module_1 = require("../../shared/shared.module");
const webhook_celcoin_controller_1 = require("./controllers/webhook-celcoin.controller");
const billpayment_celcoin_webhook_service_1 = require("./services/billpayment-celcoin-webhook.service");
const celcoin_pix_payment_webhook_service_1 = require("./services/celcoin-pix-payment-webhook.service");
const charge_create_celcoin_webhook_service_1 = require("./services/charge-create-celcoin-webhook.service");
const charge_in_celcoin_webhook_service_1 = require("./services/charge-in-celcoin-webhook.service");
const consult_celcoin_webhook_service_1 = require("./services/consult-celcoin-webhook.service");
const create_account_celcoin_webhook_service_1 = require("./services/create-account-celcoin-webhook.service");
const kyc_celcoin_webhook_service_1 = require("./services/kyc-celcoin-webhook.service");
const pix_cashout_celcoin_webhook_service_1 = require("./services/pix-cashout-celcoin-webhook.service");
const pix_claim_cancelled_celcoin_webhook_service_1 = require("./services/pix-claim-cancelled-celcoin-webhook.service");
const pix_claim_completed_celcoin_webhook_service_1 = require("./services/pix-claim-completed-celcoin-webhook.service");
const pix_claim_confirmed_celcoin_webhook_service_1 = require("./services/pix-claim-confirmed-celcoin-webhook.service");
const pix_claim_open_celcoin_webhook_service_1 = require("./services/pix-claim-open-celcoin-webhook.service");
const pix_claim_waiting_celcoin_webhook_service_1 = require("./services/pix-claim-waiting-celcoin-webhook.service");
const register_celcoin_webhook_service_1 = require("./services/register-celcoin-webhook.service");
const update_celcoin_webhook_service_1 = require("./services/update-celcoin-webhook.service");
let WebhookModule = class WebhookModule {
};
exports.WebhookModule = WebhookModule;
exports.WebhookModule = WebhookModule = __decorate([
    (0, common_1.Module)({
        imports: [shared_module_1.SharedModule, apis_module_1.ApisModule],
        controllers: [webhook_celcoin_controller_1.WebhookCelcoinController],
        providers: [
            create_account_celcoin_webhook_service_1.CreateAccountCelcoinWebhookService,
            pix_cashout_celcoin_webhook_service_1.PixCashoutCelcoinWebhookService,
            celcoin_pix_payment_webhook_service_1.CelcoinPixPaymentWebhookService,
            pix_claim_open_celcoin_webhook_service_1.PixClaimOpenCelcoinWebhookService,
            pix_claim_waiting_celcoin_webhook_service_1.PixClaimWaitingCelcoinWebhookService,
            pix_claim_confirmed_celcoin_webhook_service_1.PixClaimConfirmedCelcoinWebhookService,
            pix_claim_cancelled_celcoin_webhook_service_1.PixClaimCancelledCelcoinWebhookService,
            pix_claim_completed_celcoin_webhook_service_1.PixClaimCompletedCelcoinWebhookService,
            billpayment_celcoin_webhook_service_1.BillPaymentCelcoinWebhookService,
            charge_in_celcoin_webhook_service_1.ChargeInCelcoinWebhookService,
            charge_create_celcoin_webhook_service_1.ChargeCreateCelcoinWebhookService,
            kyc_celcoin_webhook_service_1.KycCelcoinWebhookService,
            register_celcoin_webhook_service_1.RegisterCelcoinWebhookService,
            consult_celcoin_webhook_service_1.ConsultCelcoinWebhookService,
            update_celcoin_webhook_service_1.UpdateCelcoinWebhookService,
        ],
    })
], WebhookModule);
//# sourceMappingURL=webhook.module.js.map