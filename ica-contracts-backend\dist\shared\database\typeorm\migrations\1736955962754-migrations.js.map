{"version": 3, "file": "1736955962754-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1736955962754-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAqEnC,CAAC;IAnEQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,6EAA6E,CAC9E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,8DAA8D,CAC/D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,kDAAkD,CAAC,CAAC;QAC5E,MAAM,WAAW,CAAC,KAAK,CACrB,kEAAkE,CACnE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,yEAAyE,CAC1E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,yDAAyD,CAC1D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,mEAAmE,CACpE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,oEAAoE,CACrE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,8DAA8D,CAC/D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,uMAAuM,CACxM,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wMAAwM,CACzM,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,6EAA6E,CAC9E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,6EAA6E,CAC9E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,+DAA+D,CAChE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,uEAAuE,CACxE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,sEAAsE,CACvE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,yDAAyD,CAC1D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,sDAAsD,CACvD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACzE,MAAM,WAAW,CAAC,KAAK,CACrB,4DAA4D,CAC7D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,2DAA2D,CAC5D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,+LAA+L,CAChM,CAAC;IACJ,CAAC;CACF;AAtED,0DAsEC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1736955962754 implements MigrationInterface {\r\n  name = 'Migrations1736955962754';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"notification\" DROP CONSTRAINT \"FK_227bce406a187fd48c06b102fe0\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"notification\" DROP COLUMN \"owner_role_relation\"`,\r\n    );\r\n    await queryRunner.query(`ALTER TABLE \"notification\" DROP COLUMN \"message\"`);\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"notification\" ADD \"type\" character varying NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"notification\" ADD \"description\" character varying NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"notification\" ADD \"contract_value\" integer`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"notification\" ADD \"user_owner_role_relation_id\" uuid`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"notification\" ADD \"admin_owner_role_relation_id\" uuid`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"notification\" ALTER COLUMN \"title\" SET NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"notification\" ADD CONSTRAINT \"FK_8ca93a4412001fddb88e326b91f\" FOREIGN KEY (\"user_owner_role_relation_id\") REFERENCES \"owner_role_relation\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"notification\" ADD CONSTRAINT \"FK_1786280b5e015f1c1a408b970b7\" FOREIGN KEY (\"admin_owner_role_relation_id\") REFERENCES \"owner_role_relation\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"notification\" DROP CONSTRAINT \"FK_1786280b5e015f1c1a408b970b7\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"notification\" DROP CONSTRAINT \"FK_8ca93a4412001fddb88e326b91f\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"notification\" ALTER COLUMN \"title\" DROP NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"notification\" DROP COLUMN \"admin_owner_role_relation_id\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"notification\" DROP COLUMN \"user_owner_role_relation_id\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"notification\" DROP COLUMN \"contract_value\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"notification\" DROP COLUMN \"description\"`,\r\n    );\r\n    await queryRunner.query(`ALTER TABLE \"notification\" DROP COLUMN \"type\"`);\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"notification\" ADD \"message\" character varying`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"notification\" ADD \"owner_role_relation\" uuid`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"notification\" ADD CONSTRAINT \"FK_227bce406a187fd48c06b102fe0\" FOREIGN KEY (\"owner_role_relation\") REFERENCES \"owner_role_relation\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n}\r\n"]}