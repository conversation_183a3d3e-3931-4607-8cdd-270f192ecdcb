export interface IPixCashoutCelcoinWebhook {
    body: {
        amount: number;
        clientCode: string;
        creditParty: {
            account: string;
            accountType: string;
            bank: string;
            branch: string;
            key: string | null;
            name: string;
            taxId: string;
        };
        currentBalance: number;
        debitParty: {
            account: string;
            accountType: string;
            branch: string;
            name: string;
            taxId: string;
        };
        endToEndId: string;
        id: string;
        initiationType: string;
        oldBalance: number;
        paymentType: string;
        remittanceInformation: string;
        transactionIdentification: string | null;
        transactionType: string;
        urgency: string;
    };
    createTimeStamp: string;
    entity: string;
    error: {
        errorCode: string;
        message: string;
    };
    level: string;
    message: string;
    status: string;
    timestamp: string;
    webhookId: string;
}
