export declare enum PersonType {
    PF = "PF",
    PJ = "PJ"
}
export declare enum ContractType {
    SCP = "SCP",
    MUTUO = "MUTUO"
}
export declare enum PaymentMethod {
    PIX = "pix",
    BANK_TRANSFER = "bank_transfer",
    BOLETO = "boleto"
}
export declare enum InvestorProfile {
    CONSERVATIVE = "conservative",
    MODERATE = "moderate",
    AGGRESSIVE = "aggressive"
}
export declare enum CompanyLegalType {
    MEI = "MEI",
    EI = "EI",
    EIRELI = "EIRELI",
    LTDA = "LTDA",
    SLU = "SLU",
    SA = "SA",
    SS = "SS",
    CONSORCIO = "CONSORCIO"
}
export interface CreateNewContractDTO {
    personType: PersonType;
    contractType: ContractType;
    brokerId: string;
    investment: InvestmentDetailsDTO;
    advisors: AdvisorAssignmentDTO[];
    bankAccount: BankAccountDTO;
    individual?: IndividualDTO;
    company?: CompanyDTO;
}
export interface InvestmentDetailsDTO {
    amount: number;
    monthlyRate: number;
    durationInMonths: number;
    paymentMethod: PaymentMethod;
    startDate?: string;
    endDate: string;
    profile?: InvestorProfile;
    quotaQuantity?: number;
    isDebenture?: boolean;
}
export interface AdvisorAssignmentDTO {
    advisorId: string;
    rate: number;
}
export interface BankAccountDTO {
    bank: string;
    agency: string;
    account: string;
    pix?: string;
}
export interface IndividualDTO {
    fullName: string;
    cpf: string;
    rg: string;
    issuingAgency: string;
    nationality: string;
    occupation: string;
    birthDate: string;
    email: string;
    phone: string;
    motherName: string;
    address: AddressDTO;
}
export interface CompanyDTO {
    corporateName: string;
    cnpj: string;
    type: CompanyLegalType;
    address: AddressDTO;
    representative: IndividualDTO;
}
export interface AddressDTO {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    number: string;
    neighborhood: string;
    complement?: string;
}
