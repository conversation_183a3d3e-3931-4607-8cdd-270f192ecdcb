"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1731614757162 = void 0;
class Migrations1731614757162 {
    constructor() {
        this.name = 'Migrations1731614757162';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "broker_goal" ADD "adminOwnerRoleRelationId" character varying`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "broker_goal" DROP COLUMN "adminOwnerRoleRelationId"`);
    }
}
exports.Migrations1731614757162 = Migrations1731614757162;
//# sourceMappingURL=1731614757162-migrations.js.map