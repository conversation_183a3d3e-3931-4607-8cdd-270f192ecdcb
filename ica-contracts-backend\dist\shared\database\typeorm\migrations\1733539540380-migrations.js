"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1733539540380 = void 0;
class Migrations1733539540380 {
    constructor() {
        this.name = 'Migrations1733539540380';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "addendum" ADD "external_id" character varying`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "addendum" DROP COLUMN "external_id"`);
    }
}
exports.Migrations1733539540380 = Migrations1733539540380;
//# sourceMappingURL=1733539540380-migrations.js.map