import { fastifyRouteAdapter } from '@/main/adapters'
import { makeListContractsController } from '@/main/factories/controllers/contracts/list-contracts-controller.factory'
import { makeRequestInvestorCredentialsController } from '@/main/factories/controllers/contracts/request-investor-credentials-controller.factory'
import type { FastifyInstance } from 'fastify'

export const contractRoutes = async (app: FastifyInstance) => {
  app.get('/contracts', fastifyRouteAdapter(makeListContractsController()))
  app.post(
    '/contracts/:contractId/investor/credentials',
    fastifyRouteAdapter(makeRequestInvestorCredentialsController())
  )
}
