{"version": 3, "file": "1741278700491-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1741278700491-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACI,SAAI,GAAG,yBAAyB,CAAA;IAcpC,CAAC;IAZU,KAAK,CAAC,EAAE,CAAC,WAAwB;QACpC,MAAM,WAAW,CAAC,KAAK,CAAC,6EAA6E,CAAC,CAAC;QACvG,MAAM,WAAW,CAAC,KAAK,CAAC,oEAAoE,CAAC,CAAC;QAC9F,MAAM,WAAW,CAAC,KAAK,CAAC,sLAAsL,CAAC,CAAC;IACpN,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CAAC,6EAA6E,CAAC,CAAC;QACvG,MAAM,WAAW,CAAC,KAAK,CAAC,mEAAmE,CAAC,CAAC;QAC7F,MAAM,WAAW,CAAC,KAAK,CAAC,sLAAsL,CAAC,CAAC;IACpN,CAAC;CAEJ;AAfD,0DAeC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from \"typeorm\";\r\n\r\nexport class Migrations1741278700491 implements MigrationInterface {\r\n    name = 'Migrations1741278700491'\r\n\r\n    public async up(queryRunner: QueryRunner): Promise<void> {\r\n        await queryRunner.query(`ALTER TABLE \"pre_register\" DROP CONSTRAINT \"FK_db44bd7010cefe5ae4a9bb5506f\"`);\r\n        await queryRunner.query(`ALTER TABLE \"pre_register\" ALTER COLUMN \"adviser_id\" DROP NOT NULL`);\r\n        await queryRunner.query(`ALTER TABLE \"pre_register\" ADD CONSTRAINT \"FK_db44bd7010cefe5ae4a9bb5506f\" FOREIGN KEY (\"adviser_id\") REFERENCES \"owner_role_relation\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`);\r\n    }\r\n\r\n    public async down(queryRunner: QueryRunner): Promise<void> {\r\n        await queryRunner.query(`ALTER TABLE \"pre_register\" DROP CONSTRAINT \"FK_db44bd7010cefe5ae4a9bb5506f\"`);\r\n        await queryRunner.query(`ALTER TABLE \"pre_register\" ALTER COLUMN \"adviser_id\" SET NOT NULL`);\r\n        await queryRunner.query(`ALTER TABLE \"pre_register\" ADD CONSTRAINT \"FK_db44bd7010cefe5ae4a9bb5506f\" FOREIGN KEY (\"adviser_id\") REFERENCES \"owner_role_relation\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`);\r\n    }\r\n\r\n}\r\n"]}