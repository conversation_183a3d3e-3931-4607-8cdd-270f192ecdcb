"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1734399301620 = void 0;
class Migrations1734399301620 {
    constructor() {
        this.name = 'Migrations1734399301620';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "files" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "filename" character varying(255) NOT NULL, "url" character varying(500) NOT NULL, "type" character varying(50) NOT NULL, "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_6c16b9093a142e0e7613b04a3d9" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "addendum_files" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "type" character varying(50) NOT NULL, "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "addendumId" integer, "fileId" uuid, CONSTRAINT "PK_ad6c646753270b6d29913531a00" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "addendum" ADD "yield_rate" numeric(5,2) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "addendum_files" ADD CONSTRAINT "FK_2629a7c65ba6d270c2bc2e8b8f2" FOREIGN KEY ("addendumId") REFERENCES "addendum"("id_addendum") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "addendum_files" ADD CONSTRAINT "FK_3f6aab9a35432b7e79f6e6f554f" FOREIGN KEY ("fileId") REFERENCES "files"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "addendum_files" DROP CONSTRAINT "FK_3f6aab9a35432b7e79f6e6f554f"`);
        await queryRunner.query(`ALTER TABLE "addendum_files" DROP CONSTRAINT "FK_2629a7c65ba6d270c2bc2e8b8f2"`);
        await queryRunner.query(`ALTER TABLE "addendum" DROP COLUMN "yield_rate"`);
        await queryRunner.query(`DROP TABLE "addendum_files"`);
        await queryRunner.query(`DROP TABLE "files"`);
    }
}
exports.Migrations1734399301620 = Migrations1734399301620;
//# sourceMappingURL=1734399301620-migrations.js.map