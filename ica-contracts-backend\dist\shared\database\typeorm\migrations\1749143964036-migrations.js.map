{"version": 3, "file": "1749143964036-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1749143964036-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACI,SAAI,GAAG,yBAAyB,CAAA;IAcpC,CAAC;IAZU,KAAK,CAAC,EAAE,CAAC,WAAwB;QACpC,MAAM,WAAW,CAAC,KAAK,CAAC,oUAAoU,CAAC,CAAC;QAC9V,MAAM,WAAW,CAAC,KAAK,CAAC,iLAAiL,CAAC,CAAC;QAC3M,MAAM,WAAW,CAAC,KAAK,CAAC,8LAA8L,CAAC,CAAC;IAC5N,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CAAC,kFAAkF,CAAC,CAAC;QAC5G,MAAM,WAAW,CAAC,KAAK,CAAC,kFAAkF,CAAC,CAAC;QAC5G,MAAM,WAAW,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;IAC9D,CAAC;CAEJ;AAfD,0DAeC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from \"typeorm\";\r\n\r\nexport class Migrations1749143964036 implements MigrationInterface {\r\n    name = 'Migrations1749143964036'\r\n\r\n    public async up(queryRunner: QueryRunner): Promise<void> {\r\n        await queryRunner.query(`CREATE TABLE \"contract_deletion\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"contract_id\" uuid NOT NULL, \"deleted_by_id\" uuid NOT NULL, \"reason\" text NOT NULL, \"deleted_at\" TIMESTAMP NOT NULL DEFAULT now(), \"updated_at\" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT \"PK_c2540b8ae09ef4084ad579c41b4\" PRIMARY KEY (\"id\"))`);\r\n        await queryRunner.query(`ALTER TABLE \"contract_deletion\" ADD CONSTRAINT \"FK_6709416e6166af82ffb1a094fc1\" FOREIGN KEY (\"contract_id\") REFERENCES \"contract\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`);\r\n        await queryRunner.query(`ALTER TABLE \"contract_deletion\" ADD CONSTRAINT \"FK_f7a4a72bfa952689750c9db0399\" FOREIGN KEY (\"deleted_by_id\") REFERENCES \"owner_role_relation\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`);\r\n    }\r\n\r\n    public async down(queryRunner: QueryRunner): Promise<void> {\r\n        await queryRunner.query(`ALTER TABLE \"contract_deletion\" DROP CONSTRAINT \"FK_f7a4a72bfa952689750c9db0399\"`);\r\n        await queryRunner.query(`ALTER TABLE \"contract_deletion\" DROP CONSTRAINT \"FK_6709416e6166af82ffb1a094fc1\"`);\r\n        await queryRunner.query(`DROP TABLE \"contract_deletion\"`);\r\n    }\r\n\r\n}\r\n"]}