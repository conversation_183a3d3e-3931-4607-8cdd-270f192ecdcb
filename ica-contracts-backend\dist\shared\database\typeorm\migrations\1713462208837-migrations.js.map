{"version": 3, "file": "1713462208837-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1713462208837-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAiBnC,CAAC;IAfQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,mjBAAmjB,CACpjB,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,0KAA0K,CAC3K,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,6EAA6E,CAC9E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;IACvD,CAAC;CACF;AAlBD,0DAkBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1713462208837 implements MigrationInterface {\r\n  name = 'Migrations1713462208837';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"favorite_pix\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"account_id\" uuid, \"account_document\" character varying NOT NULL, \"name\" character varying NOT NULL, \"account_number\" character varying NOT NULL, \"account_branch\" character varying NOT NULL, \"account_type\" character varying NOT NULL, \"account_bank\" character varying NOT NULL, \"alias\" character varying, \"updated_at\" TIMESTAMP NOT NULL DEFAULT now(), \"created_at\" TIMESTAMP NOT NULL DEFAULT now(), \"deleted_at\" TIMESTAMP, CONSTRAINT \"PK_8036547b3ab2e4af317048ff597\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"favorite_pix\" ADD CONSTRAINT \"FK_9986c36d5cfcf84bb0f072b62ea\" FOREIGN KEY (\"account_id\") REFERENCES \"account\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"favorite_pix\" DROP CONSTRAINT \"FK_9986c36d5cfcf84bb0f072b62ea\"`,\r\n    );\r\n    await queryRunner.query(`DROP TABLE \"favorite_pix\"`);\r\n  }\r\n}\r\n"]}