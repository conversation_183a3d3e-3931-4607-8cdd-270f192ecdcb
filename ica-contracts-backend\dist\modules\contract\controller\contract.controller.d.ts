import { SortingParam } from 'src/modules/contract/decorators/sorting-params.decorator';
import { IRequestUser } from 'src/shared/interfaces/request-user.interface';
import { AddSignatariesDto } from '../dto/add-signataries.dto';
import { CreateContractAdditiveManualDto } from '../dto/create-contract-additive-manual.dto';
import { CreateContractAdditiveDto } from '../dto/create-contract-additive.dto';
import { CreateNewContractDto } from '../dto/create-contract-manual.dto';
import { CreateContractDto } from '../dto/create-contract.dto';
import { ContractIdParamDto } from '../dto/delete-contract-params.dto';
import { DeleteContractDto } from '../dto/delete-contract.dto';
import { EditContractParamsDto } from '../dto/edit-contract-params.dto';
import { EditNewContractDto } from '../dto/edit-new-contract.dto';
import { GetContractsResponse } from '../dto/get-contracts-response.dto';
import { GetContractsDto } from '../dto/get-contracts.dto';
import { GetContractDto } from '../dto/get-one-contract.dto';
import { ListContractsSuperadminDto } from '../dto/list-contracts-superadmin.dto';
import { RenewContractDto } from '../dto/renew-contract.dto';
import { UploadProofPaymentAddendumDto } from '../dto/upload-proof-payment-addendum.dto';
import { UploadProofPaymentDto } from '../dto/upload-proof-payment.dto';
import { AddSignatoriesService } from '../services/add-signataries.service';
import { CreateContractAdditiveManualService } from '../services/create-contract-additive-manual.service';
import { CreateContractAdditiveService } from '../services/create-contract-additive.service';
import { CreateContractManualService } from '../services/create-contract-manual/create-contract-manual.service';
import { CreateContractService } from '../services/create-contract.service';
import { DeleteContractService } from '../services/delete-contract.service';
import { EditNewContractService } from '../services/editt-new-contract.service';
import { GetContractsByInvestorService } from '../services/get-contract-by-investor.service';
import { GetContractDetailService } from '../services/get-contract-detail.service';
import { GetContractsService } from '../services/get-contracts.service';
import { GetContratAddendumsByIdService } from '../services/get-contrat-addendums-by-id.service';
import { GetOneContractService } from '../services/get-one-contracts.service';
import { ListContractsSuperadminService } from '../services/list-contracts-superadmin.service';
import { RenewContractService } from '../services/renew-contract.service';
import { SendEmailNotificationContract } from '../services/send-notification.service';
import { UploadProofPaymentAddendumService } from '../services/upload-proof-payment-addendum.service';
import { UploadProofPaymentService } from '../services/upload-proof-payment.service';
export declare class ContractController {
    private readonly createContractService;
    private readonly createContractManualService;
    private readonly getContractsService;
    private readonly getOneContractService;
    private readonly addSignatoriesService;
    private readonly renewContractService;
    private readonly getContractsByInvestorService;
    private readonly uploadProofPaymentService;
    private readonly uploadProofPaymentAddendumService;
    private readonly sendNotificationContract;
    private readonly createContractAdditiveService;
    private readonly createContractAdditiveManualService;
    private readonly listContractsSuperadminService;
    private readonly getContratAddendumsByIdService;
    private readonly getContractDetailService;
    private readonly deleteContractService;
    private readonly editNewContractService;
    constructor(createContractService: CreateContractService, createContractManualService: CreateContractManualService, getContractsService: GetContractsService, getOneContractService: GetOneContractService, addSignatoriesService: AddSignatoriesService, renewContractService: RenewContractService, getContractsByInvestorService: GetContractsByInvestorService, uploadProofPaymentService: UploadProofPaymentService, uploadProofPaymentAddendumService: UploadProofPaymentAddendumService, sendNotificationContract: SendEmailNotificationContract, createContractAdditiveService: CreateContractAdditiveService, createContractAdditiveManualService: CreateContractAdditiveManualService, listContractsSuperadminService: ListContractsSuperadminService, getContratAddendumsByIdService: GetContratAddendumsByIdService, getContractDetailService: GetContractDetailService, deleteContractService: DeleteContractService, editNewContractService: EditNewContractService);
    createContract(createContractDto: CreateContractDto, request: IRequestUser): Promise<import("../../../apis/assinatura-rbm/responses/send-document.response").ISendDocumentResponse>;
    createContractManual(createContractDto: CreateNewContractDto, request: IRequestUser): Promise<{
        id: string;
        status: string;
    }>;
    createContractAdditive(createContractDto: CreateContractAdditiveDto): Promise<import("../../../shared/database/typeorm/entities/addendum.entity").AddendumEntity>;
    editNewContract(params: EditContractParamsDto, editNewContractDto: EditNewContractDto): Promise<void>;
    getAllContracts(data: GetContractsDto, sort?: SortingParam): Promise<GetContractsResponse>;
    getContract(query: GetContractDto): Promise<{
        documento: {
            id: string;
            statusAssinatura: string;
            valorInvestimento: number;
            prazoInvestimento: string;
            rendimentoInvestimento: number;
            nomeInvestidor: string;
            documentoInvestidor: string;
            tags: string;
            compradoCom: string;
            cotas: number;
            periodoCarencia: Date;
            consultorResponsavel: string;
            statusContrato: string;
            inicioContrato: Date;
            fimContrato: Date;
            finalizadoEm: Date;
            documentoPdf: string;
            addendum: import("../../../shared/database/typeorm/entities/addendum.entity").AddendumEntity[];
        };
    }>;
    addSignatories(data: AddSignatariesDto): Promise<import("../../../apis/assinatura-rbm/responses/add-many-signatories.response").IAddSignatoriesResponse>;
    renewContract(contractId: string, renewContractDto: RenewContractDto, files: {
        oldContractPdf?: Express.Multer.File[];
        newContractPdf?: Express.Multer.File[];
    }): Promise<import("../../../shared/database/typeorm/entities/contract.entity").ContractEntity>;
    getContracts(investorId: string): Promise<{
        document: string;
        email: string;
        phone: string;
        profile: string;
        advisor: string;
        rg: string;
        motherName: string;
        birthDate: string;
        zipCode: string;
        city: string;
        address: string;
        addressNumber: string;
        complement: string;
        neighborhood: string;
        state: string;
        bank: string;
        accountNumber: string;
        branch: string;
        contracts: {
            id: string;
            investmentValue: number;
            investmentTerm: string;
            investmentYield: number;
            investorName: string;
            investorDocument: string;
            tags: string;
            purchasedWith: string;
            quotesAmount: number;
            gracePeriod: Date;
            responsibleConsultant: string;
            contractStatus: string;
            contractStart: Date;
            contractEnd: Date;
            finalizedAt: Date;
            pdfDocument: string;
            proofPaymentPdf: string;
            advisors: {
                id: string;
                name: string;
                document: string;
                rate: number;
            }[];
            addendum: {
                id: number;
                investmentValue: number;
                investmentYield: number;
                contractStart: Date;
                contractEnd: string;
                contractStatus: import("../../../shared/database/typeorm/entities/addendum.entity").AddendumStatus;
                responsibleConsultant: string;
                files: {
                    type: string;
                    url: string;
                }[];
            }[];
        }[];
    }>;
    uploadProofPayment(body: UploadProofPaymentDto, proofPayment: Express.Multer.File): Promise<void>;
    uploadProofPaymentAddendum(body: UploadProofPaymentAddendumDto, proofPayment: Express.Multer.File): Promise<void>;
    sendNotification(contractId: string): Promise<any>;
    createContractAdditiveManual(files: {
        contractPdf?: Express.Multer.File[];
        proofPayment?: Express.Multer.File[];
    }, createContractDto: CreateContractAdditiveManualDto): Promise<{
        contractId: string;
        yieldRate: number;
        value: number;
        applicationDate: Date;
        status: import("../../../shared/database/typeorm/entities/addendum.entity").AddendumStatus.FULLY_SIGNED;
        reason: string;
        expiresIn: string;
    } & import("../../../shared/database/typeorm/entities/addendum.entity").AddendumEntity>;
    listContractsSuperadmin(data: ListContractsSuperadminDto, sort?: SortingParam): Promise<GetContractsResponse>;
    getContractAddendums(id: string): Promise<{
        addendums: {
            addendumFiles: {
                type: string;
                url: string;
            }[];
            id: number;
            status: import("../../../shared/database/typeorm/entities/addendum.entity").AddendumStatus;
            contractId: string;
            externalId: string;
            contract: import("typeorm").Relation<import("../../../shared/database/typeorm/entities/contract.entity").ContractEntity>;
            applicationDate: Date;
            yieldRate: number;
            value: number;
            reason: string;
            notes: string;
            expiresIn: string;
            createdAt: Date;
            updatedAt: Date;
            addendumNotifications: import("../../../shared/database/typeorm/entities/notification.entity").NotificationEntity[];
        }[];
    }>;
    getContractDetail(id: string): Promise<import("../services/get-contract-detail.service").MappedContractDetail>;
    deleteContract(body: DeleteContractDto, req: Request & IRequestUser, params: ContractIdParamDto): Promise<{
        id: string;
    }>;
}
