import { PixKeyEntity } from 'src/shared/database/typeorm/entities/pix-key.entity';
import { Repository } from 'typeorm';
import { IPixDictClaimOpen } from '../dto/pix-claim-open-celcoin-webhook.dto';
export declare class PixClaimCancelledCelcoinWebhookService {
    private readonly pixKeyRepository;
    constructor(pixKeyRepository: Repository<PixKeyEntity>);
    perform(input: IPixDictClaimOpen): Promise<void>;
}
