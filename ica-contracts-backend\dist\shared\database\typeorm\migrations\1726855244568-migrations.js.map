{"version": 3, "file": "1726855244568-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1726855244568-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAmBnC,CAAC;IAjBQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,mEAAmE,CACpE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,iEAAiE,CAClE,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,6EAA6E,CAC9E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,+EAA+E,CAChF,CAAC;IACJ,CAAC;CACF;AApBD,0DAoBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1726855244568 implements MigrationInterface {\r\n  name = 'Migrations1726855244568';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract\" ALTER COLUMN \"start_contract\" DROP DEFAULT`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract\" ALTER COLUMN \"end_contract\" DROP DEFAULT`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract\" ALTER COLUMN \"end_contract\" SET DEFAULT '2024-09-20'`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract\" ALTER COLUMN \"start_contract\" SET DEFAULT '2024-09-20'`,\r\n    );\r\n  }\r\n}\r\n"]}