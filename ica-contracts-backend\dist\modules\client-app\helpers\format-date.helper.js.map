{"version": 3, "file": "format-date.helper.js", "sourceRoot": "/", "sources": ["modules/client-app/helpers/format-date.helper.ts"], "names": [], "mappings": ";;AAAA,gCAiBC;AAjBD,SAAgB,UAAU,CAAC,IAAmB;IAC5C,IAAI,CAAC;QAEH,MAAM,OAAO,GAAG,IAAI,YAAY,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;QAG7D,MAAM,IAAI,GAAG,OAAO,CAAC,cAAc,EAAE,CAAC;QACtC,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QACjE,MAAM,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAG1D,OAAO,GAAG,GAAG,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;IACnC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QAErD,OAAO,cAAc,CAAC;IACxB,CAAC;AACH,CAAC", "sourcesContent": ["export function formatDate(date: Date | string): string {\r\n  try {\r\n    // Ensure we're working with a Date object\r\n    const dateObj = date instanceof Date ? date : new Date(date);\r\n\r\n    // Format the date as dd/MM/yyyy\r\n    const year = dateObj.getUTCFullYear();\r\n    const month = String(dateObj.getUTCMonth() + 1).padStart(2, '0');\r\n    const day = String(dateObj.getUTCDate()).padStart(2, '0');\r\n\r\n    // Return in dd/MM/yyyy format\r\n    return `${day}/${month}/${year}`;\r\n  } catch (error) {\r\n    console.error('Error formatting date:', date, error);\r\n    // Return a fallback value if date is invalid\r\n    return 'Invalid date';\r\n  }\r\n}\r\n"]}