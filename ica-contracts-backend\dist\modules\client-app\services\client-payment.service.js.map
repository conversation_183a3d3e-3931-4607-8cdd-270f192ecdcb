{"version": 3, "file": "client-payment.service.js", "sourceRoot": "/", "sources": ["modules/client-app/services/client-payment.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA+D;AAC/D,6CAAmD;AACnD,uCAAkC;AAClC,+HAAoH;AACpH,qCAMiB;AAUV,IAAM,oBAAoB,GAA1B,MAAM,oBAAoB;IAC/B,YAEmB,gCAA0E;QAA1E,qCAAgC,GAAhC,gCAAgC,CAA0C;IAC1F,CAAC;IAEJ,KAAK,CAAC,mBAAmB,CACvB,MAAc,EACd,OAAgC;QAEhC,MAAM,WAAW,GAAmD;YAClE,QAAQ,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE;SACvC,CAAC;QAGF,IAAI,OAAO,EAAE,SAAS,IAAI,OAAO,EAAE,OAAO,EAAE,CAAC;YAC3C,WAAW,CAAC,aAAa,GAAG,IAAA,aAAG,EAC7B,CAAC,KAAK,EAAE,EAAE,CAAC,QAAQ,KAAK,mCAAmC,EAC3D;gBACE,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,OAAO,EAAE,OAAO,CAAC,OAAO;aACzB,CACF,CAAC;QACJ,CAAC;aAAM,IAAI,OAAO,EAAE,SAAS,EAAE,CAAC;YAC9B,WAAW,CAAC,aAAa,GAAG,IAAA,yBAAe,EAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACjE,CAAC;aAAM,IAAI,OAAO,EAAE,OAAO,EAAE,CAAC;YAC5B,WAAW,CAAC,aAAa,GAAG,IAAA,yBAAe,EAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC/D,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,gCAAgC,CAAC,IAAI,CAAC;YAChE,KAAK,EAAE,WAAW;YAClB,SAAS,EAAE;gBACT,QAAQ,EAAE;oBACR,WAAW,EAAE,IAAI;iBAClB;gBACD,oBAAoB,EAAE;oBACpB,OAAO,EAAE,IAAI;iBACd;aACF;YACD,KAAK,EAAE;gBACL,aAAa,EAAE,MAAM;aACtB;SACF,CAAC,CAAC;QAEH,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1B,OAAO;gBACL,QAAQ,EAAE,EAAE;gBACZ,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,CAAC;gBACZ,cAAc,EAAE,CAAC;aAClB,CAAC;QACJ,CAAC;QAED,IAAI,cAAc,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;YAC5C,MAAM,MAAM,GAAG,OAAO,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;YACtE,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC;YAE7C,MAAM,eAAe,GAA2B;gBAC9C,EAAE,EAAE,OAAO,CAAC,EAAE;gBACd,aAAa,EAAE,IAAA,iBAAM,EAAC,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,YAAY,CAAC;gBACpE,MAAM,EAAE,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC;gBAClC,MAAM;gBACN,QAAQ,EACN,MAAM,IAAI,OAAO,CAAC,oBAAoB,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,SAAS;oBAC3D,CAAC,CAAC,IAAA,iBAAM,EACJ,IAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,EAC3D,YAAY,CACb;oBACH,CAAC,CAAC,SAAS;aAChB,CAAC;YAEF,OAAO,EAAE,OAAO,EAAE,eAAe,EAAE,MAAM,EAAE,CAAC;QAC9C,CAAC,CAAC,CAAC;QAGH,IAAI,OAAO,EAAE,MAAM,EAAE,CAAC;YACpB,cAAc,GAAG,cAAc,CAAC,MAAM,CACpC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,CACnC,CAAC;QACJ,CAAC;QAED,MAAM,SAAS,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,MAAM,CAAC;QAC3E,MAAM,cAAc,GAAG,cAAc,CAAC,MAAM,CAC1C,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,WAAW,CAChC,CAAC,MAAM,CAAC;QACT,MAAM,KAAK,GAAG,cAAc,CAAC,MAAM,CAAC;QAGpC,MAAM,IAAI,GAAG,OAAO,EAAE,IAAI,IAAI,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QAClE,MAAM,KAAK,GAAG,OAAO,EAAE,KAAK,IAAI,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;QACvE,MAAM,UAAU,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QACtC,MAAM,QAAQ,GAAG,IAAI,GAAG,KAAK,CAAC;QAE9B,MAAM,iBAAiB,GAAG,cAAc;aACrC,KAAK,CAAC,UAAU,EAAE,QAAQ,CAAC;aAC3B,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QAEzB,OAAO;YACL,QAAQ,EAAE,iBAAiB;YAC3B,KAAK;YACL,SAAS;YACT,cAAc;SACf,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,cAAc,CAClB,SAAiB;QAEjB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,gCAAgC,CAAC,OAAO,CAAC;YAClE,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;YACxB,SAAS,EAAE;gBACT,QAAQ,EAAE;oBACR,WAAW,EAAE,IAAI;iBAClB;gBACD,oBAAoB,EAAE;oBACpB,OAAO,EAAE,IAAI;iBACd;aACF;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,0BAAiB,CAAC,0BAA0B,CAAC,CAAC;QAC1D,CAAC;QAED,MAAM,MAAM,GAAG,OAAO,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;QACtE,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC;QAE7C,MAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;QACnD,MAAM,cAAc,GAAG,SAAS,EAAE,eAAe,IAAI,CAAC,CAAC;QAEvD,OAAO;YACL,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,aAAa,EAAE,IAAA,iBAAM,EAAC,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,YAAY,CAAC;YACpE,MAAM,EAAE,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC;YAClC,MAAM;YACN,QAAQ,EACN,MAAM,IAAI,OAAO,CAAC,oBAAoB,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,SAAS;gBAC3D,CAAC,CAAC,IAAA,iBAAM,EACJ,IAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,EAC3D,YAAY,CACb;gBACH,CAAC,CAAC,SAAS;YACf,UAAU,EAAE,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC/B,YAAY,EAAE,0BAA0B;YACxC,cAAc;YACd,IAAI,EAAE,SAAS,EAAE,IAAI,IAAI,EAAE;YAC3B,MAAM,EAAE,SAAS,EAAE,MAAM,IAAI,EAAE;YAC/B,OAAO,EAAE,SAAS,EAAE,OAAO,IAAI,EAAE;YACjC,MAAM,EAAE,EAAE;SACX,CAAC;IACJ,CAAC;CACF,CAAA;AAvJY,oDAAoB;+BAApB,oBAAoB;IADhC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,8DAA4B,CAAC,CAAA;qCACI,oBAAU;GAHpD,oBAAoB,CAuJhC", "sourcesContent": ["import { Injectable, NotFoundException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { format } from 'date-fns';\r\nimport { IncomePaymentScheduledEntity } from 'src/shared/database/typeorm/entities/income-payment-scheduled.entity';\r\nimport {\r\n  Repository,\r\n  MoreThanOrEqual,\r\n  LessThanOrEqual,\r\n  Raw,\r\n  type FindOptionsWhere,\r\n} from 'typeorm';\r\n\r\nimport { ClientPaymentsQueryDto } from '../dto/client-payment.dto';\r\nimport {\r\n  IClientPaymentResponse,\r\n  IClientPaymentsListResponse,\r\n  IClientPaymentDetailResponse,\r\n} from '../responses/client-payment.response';\r\n\r\n@Injectable()\r\nexport class ClientPaymentService {\r\n  constructor(\r\n    @InjectRepository(IncomePaymentScheduledEntity)\r\n    private readonly incomePaymentScheduledRepository: Repository<IncomePaymentScheduledEntity>,\r\n  ) {}\r\n\r\n  async getPaymentsByUserId(\r\n    userId: string,\r\n    filters?: ClientPaymentsQueryDto,\r\n  ): Promise<IClientPaymentsListResponse> {\r\n    const whereClause: FindOptionsWhere<IncomePaymentScheduledEntity> = {\r\n      contract: { investor: { id: userId } },\r\n    };\r\n\r\n    // Aplicar filtros de data\r\n    if (filters?.startDate && filters?.endDate) {\r\n      whereClause.scheduledDate = Raw(\r\n        (alias) => `DATE(${alias}) BETWEEN :startDate AND :endDate`,\r\n        {\r\n          startDate: filters.startDate,\r\n          endDate: filters.endDate,\r\n        },\r\n      );\r\n    } else if (filters?.startDate) {\r\n      whereClause.scheduledDate = MoreThanOrEqual(filters.startDate);\r\n    } else if (filters?.endDate) {\r\n      whereClause.scheduledDate = LessThanOrEqual(filters.endDate);\r\n    }\r\n\r\n    const payments = await this.incomePaymentScheduledRepository.find({\r\n      where: whereClause,\r\n      relations: {\r\n        contract: {\r\n          signataries: true,\r\n        },\r\n        paymentsDistribution: {\r\n          payment: true,\r\n        },\r\n      },\r\n      order: {\r\n        scheduledDate: 'DESC',\r\n      },\r\n    });\r\n\r\n    if (payments.length === 0) {\r\n      return {\r\n        payments: [],\r\n        total: 0,\r\n        totalPaid: 0,\r\n        totalScheduled: 0,\r\n      };\r\n    }\r\n\r\n    let clientPayments = payments.map((payment) => {\r\n      const isPaid = payment.paymentsDistribution?.some((pd) => pd.payment);\r\n      const status = isPaid ? 'PAID' : 'SCHEDULED';\r\n\r\n      const paymentResponse: IClientPaymentResponse = {\r\n        id: payment.id,\r\n        scheduledDate: format(new Date(payment.scheduledDate), 'dd/MM/yyyy'),\r\n        amount: parseFloat(payment.amount),\r\n        status,\r\n        paidDate:\r\n          isPaid && payment.paymentsDistribution[0]?.payment?.createdAt\r\n            ? format(\r\n                new Date(payment.paymentsDistribution[0].payment.createdAt),\r\n                'dd/MM/yyyy',\r\n              )\r\n            : undefined,\r\n      };\r\n\r\n      return { payment: paymentResponse, status };\r\n    });\r\n\r\n    // Aplicar filtro de status\r\n    if (filters?.status) {\r\n      clientPayments = clientPayments.filter(\r\n        (p) => p.status === filters.status,\r\n      );\r\n    }\r\n\r\n    const totalPaid = clientPayments.filter((p) => p.status === 'PAID').length;\r\n    const totalScheduled = clientPayments.filter(\r\n      (p) => p.status === 'SCHEDULED',\r\n    ).length;\r\n    const total = clientPayments.length;\r\n\r\n    // Aplicar paginação\r\n    const page = filters?.page && filters.page > 0 ? filters.page : 1;\r\n    const limit = filters?.limit && filters.limit > 0 ? filters.limit : 10;\r\n    const startIndex = (page - 1) * limit;\r\n    const endIndex = page * limit;\r\n\r\n    const paginatedPayments = clientPayments\r\n      .slice(startIndex, endIndex)\r\n      .map((p) => p.payment);\r\n\r\n    return {\r\n      payments: paginatedPayments,\r\n      total,\r\n      totalPaid,\r\n      totalScheduled,\r\n    };\r\n  }\r\n\r\n  async getPaymentById(\r\n    paymentId: string,\r\n  ): Promise<IClientPaymentDetailResponse> {\r\n    const payment = await this.incomePaymentScheduledRepository.findOne({\r\n      where: { id: paymentId },\r\n      relations: {\r\n        contract: {\r\n          signataries: true,\r\n        },\r\n        paymentsDistribution: {\r\n          payment: true,\r\n        },\r\n      },\r\n    });\r\n\r\n    if (!payment) {\r\n      throw new NotFoundException('Pagamento não encontrado');\r\n    }\r\n\r\n    const isPaid = payment.paymentsDistribution?.some((pd) => pd.payment);\r\n    const status = isPaid ? 'PAID' : 'SCHEDULED';\r\n\r\n    const signatory = payment.contract?.signataries[0];\r\n    const investedAmount = signatory?.investmentValue || 0;\r\n\r\n    return {\r\n      id: payment.id,\r\n      scheduledDate: format(new Date(payment.scheduledDate), 'dd/MM/yyyy'),\r\n      amount: parseFloat(payment.amount),\r\n      status,\r\n      paidDate:\r\n        isPaid && payment.paymentsDistribution[0]?.payment?.createdAt\r\n          ? format(\r\n              new Date(payment.paymentsDistribution[0].payment.createdAt),\r\n              'dd/MM/yyyy',\r\n            )\r\n          : undefined,\r\n      contractId: payment.contract.id,\r\n      contractType: 'Contrato de Investimento',\r\n      investedAmount,\r\n      bank: signatory?.bank || '',\r\n      agency: signatory?.agency || '',\r\n      account: signatory?.account || '',\r\n      pixKey: '',\r\n    };\r\n  }\r\n}\r\n"]}