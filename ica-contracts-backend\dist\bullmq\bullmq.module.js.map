{"version": 3, "file": "bullmq.module.js", "sourceRoot": "/", "sources": ["bullmq/bullmq.module.ts"], "names": [], "mappings": "", "sourcesContent": ["// import { BullModule } from '@nestjs/bull';\r\n// import { Module } from '@nestjs/common';\r\n// import { ConfigModule } from '@nestjs/config';\r\n\r\n// @Module({\r\n//   imports: [\r\n//     ConfigModule.forRoot(),\r\n//     BullModule.forRoot({\r\n//       redis: {\r\n//         host: process.env.REDIS_HOST,\r\n//         port: Number(process.env.REDIS_PORT),\r\n//         password: process.env.REDIS_PASSWORD,\r\n//       },\r\n//     }),\r\n//   ],\r\n//   exports: [BullModule],\r\n// })\r\n// export class BullmqModule {}\r\n"]}