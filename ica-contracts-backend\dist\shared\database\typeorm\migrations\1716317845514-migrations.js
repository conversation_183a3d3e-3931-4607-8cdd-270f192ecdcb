"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations************* = void 0;
class Migrations************* {
    constructor() {
        this.name = 'Migrations*************';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" DROP COLUMN "daily_limit"`);
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" ADD "daily_limit" numeric NOT NULL DEFAULT '1000'`);
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" DROP COLUMN "monthly_limit"`);
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" ADD "monthly_limit" numeric NOT NULL DEFAULT '1000'`);
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" DROP COLUMN "daily_night_limit"`);
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" ADD "daily_night_limit" numeric NOT NULL DEFAULT '1000'`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" DROP COLUMN "daily_night_limit"`);
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" ADD "daily_night_limit" integer NOT NULL DEFAULT '1000'`);
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" DROP COLUMN "monthly_limit"`);
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" ADD "monthly_limit" integer NOT NULL DEFAULT '1000'`);
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" DROP COLUMN "daily_limit"`);
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" ADD "daily_limit" integer NOT NULL DEFAULT '1000'`);
    }
}
exports.Migrations************* = Migrations*************;
//# sourceMappingURL=*************-migrations.js.map