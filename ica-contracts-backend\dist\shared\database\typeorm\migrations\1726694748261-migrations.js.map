{"version": 3, "file": "1726694748261-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1726694748261-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAyBnC,CAAC;IAvBQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,yEAAyE,CAC1E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,yCAAyC,CAAC,CAAC;QACnE,MAAM,WAAW,CAAC,KAAK,CACrB,0EAA0E,CAC3E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,2FAA2F,CAC5F,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,yEAAyE,CAC1E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,yCAAyC,CAAC,CAAC;QACnE,MAAM,WAAW,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAC;QAC3E,MAAM,WAAW,CAAC,KAAK,CACrB,2FAA2F,CAC5F,CAAC;IACJ,CAAC;CACF;AA1BD,0DA0BC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1726694748261 implements MigrationInterface {\r\n  name = 'Migrations1726694748261';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"movement\" DROP CONSTRAINT \"PK_079f005d01ebda984e75c2d67ee\"`,\r\n    );\r\n    await queryRunner.query(`ALTER TABLE \"movement\" DROP COLUMN \"id\"`);\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"movement\" ADD \"id\" uuid NOT NULL DEFAULT uuid_generate_v4()`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"movement\" ADD CONSTRAINT \"PK_079f005d01ebda984e75c2d67ee\" PRIMARY KEY (\"id\")`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"movement\" DROP CONSTRAINT \"PK_079f005d01ebda984e75c2d67ee\"`,\r\n    );\r\n    await queryRunner.query(`ALTER TABLE \"movement\" DROP COLUMN \"id\"`);\r\n    await queryRunner.query(`ALTER TABLE \"movement\" ADD \"id\" SERIAL NOT NULL`);\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"movement\" ADD CONSTRAINT \"PK_079f005d01ebda984e75c2d67ee\" PRIMARY KEY (\"id\")`,\r\n    );\r\n  }\r\n}\r\n"]}