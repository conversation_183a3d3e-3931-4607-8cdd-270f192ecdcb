"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1709598578142 = void 0;
class Migrations1709598578142 {
    constructor() {
        this.name = 'Migrations1709598578142';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "transaction" ADD "end_to_end_id" character varying`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "transaction" DROP COLUMN "end_to_end_id"`);
    }
}
exports.Migrations1709598578142 = Migrations1709598578142;
//# sourceMappingURL=1709598578142-migrations.js.map