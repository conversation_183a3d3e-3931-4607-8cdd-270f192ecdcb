{"version": 3, "file": "1750095193539-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1750095193539-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAkEnC,CAAC;IAjEQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QAEtC,MAAM,WAAW,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC;QAG1E,MAAM,WAAW,CAAC,KAAK,CAAC;;;;aAIf,CAAC,CAAC;QAGX,MAAM,WAAW,CAAC,KAAK,CAAC;;;;aAIf,CAAC,CAAC;QAGX,MAAM,WAAW,CAAC,KAAK,CACrB,yDAAyD,CAC1D,CAAC;QAGF,MAAM,WAAW,CAAC,KAAK,CACrB,mEAAmE,CACpE,CAAC;QAGF,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;;;aAOf,CAAC,CAAC;IACb,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QAExC,MAAM,WAAW,CAAC,KAAK,CACrB,yEAAyE,CAC1E,CAAC;QAGF,MAAM,WAAW,CAAC,KAAK,CACrB,6DAA6D,CAC9D,CAAC;QAGF,MAAM,WAAW,CAAC,KAAK,CAAC;;;;aAIf,CAAC,CAAC;QAGX,MAAM,WAAW,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;QAGzE,MAAM,WAAW,CAAC,KAAK,CACrB,mEAAmE,CACpE,CAAC;IACJ,CAAC;CACF;AAnED,0DAmEC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1750095193539 implements MigrationInterface {\r\n  name = 'Migrations1750095193539';\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    // First, create a temporary column to store the data\r\n    await queryRunner.query(`ALTER TABLE \"business\" ADD \"owner_id_new\" uuid`);\r\n\r\n    // Set empty strings to NULL first\r\n    await queryRunner.query(`\r\n                UPDATE \"business\" \r\n                SET \"owner_id\" = NULL \r\n                WHERE \"owner_id\" = ''\r\n            `);\r\n\r\n    // Copy data from old column to new column, converting to UUID\r\n    await queryRunner.query(`\r\n                UPDATE \"business\" \r\n                SET \"owner_id_new\" = \"owner_id\"::uuid \r\n                WHERE \"owner_id\" IS NOT NULL\r\n            `);\r\n\r\n    // Drop the old column and constraint if it exists\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"business\" DROP COLUMN IF EXISTS \"owner_id\"`,\r\n    );\r\n\r\n    // Rename the new column to the original name\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"business\" RENAME COLUMN \"owner_id_new\" TO \"owner_id\"`,\r\n    );\r\n\r\n    // Add the foreign key constraint\r\n    await queryRunner.query(`\r\n                ALTER TABLE \"business\" \r\n                ADD CONSTRAINT \"FK_da374bf24a4b84a4aa36dfd3e76\" \r\n                FOREIGN KEY (\"owner_id\") \r\n                REFERENCES \"owner\"(\"id\") \r\n                ON DELETE NO ACTION \r\n                ON UPDATE NO ACTION\r\n            `);\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    // Drop the foreign key constraint\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"business\" DROP CONSTRAINT \"FK_da374bf24a4b84a4aa36dfd3e76\"`,\r\n    );\r\n\r\n    // Create a temporary column to store the data\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"business\" ADD \"owner_id_old\" character varying`,\r\n    );\r\n\r\n    // Copy data back to the old column type\r\n    await queryRunner.query(`\r\n                UPDATE \"business\" \r\n                SET \"owner_id_old\" = \"owner_id\"::text \r\n                WHERE \"owner_id\" IS NOT NULL\r\n            `);\r\n\r\n    // Drop the UUID column\r\n    await queryRunner.query(`ALTER TABLE \"business\" DROP COLUMN \"owner_id\"`);\r\n\r\n    // Rename the old column back\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"business\" RENAME COLUMN \"owner_id_old\" TO \"owner_id\"`,\r\n    );\r\n  }\r\n}\r\n"]}