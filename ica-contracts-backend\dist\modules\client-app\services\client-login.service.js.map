{"version": 3, "file": "client-login.service.js", "sourceRoot": "/", "sources": ["modules/client-app/services/client-login.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAIwB;AACxB,qCAAyC;AACzC,6CAAmD;AACnD,iCAAiC;AACjC,+FAAsF;AACtF,yFAAgF;AAChF,iEAAwD;AACxD,qCAAqC;AAIrC,qEAAgE;AAUzD,IAAM,kBAAkB,GAAxB,MAAM,kBAAkB;IAC7B,YAEU,OAAgC,EAEhC,UAAsC,EACtC,UAAsB,EACtB,oBAA0C;QAJ1C,YAAO,GAAP,OAAO,CAAyB;QAEhC,eAAU,GAAV,UAAU,CAA4B;QACtC,eAAU,GAAV,UAAU,CAAY;QACtB,yBAAoB,GAApB,oBAAoB,CAAsB;IACjD,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,IAAoB;QAChC,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACzC,MAAM,IAAI,4BAAmB,CAC3B,6CAA6C,CAC9C,CAAC;QACJ,CAAC;QACD,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACnD,IAAI,MAAc,CAAC;QACnB,IAAI,WAAmB,CAAC;QACxB,IAAI,YAAgC,CAAC;QAErC,IAAI,YAAY,KAAK,KAAK,EAAE,CAAC;YAC3B,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;gBACvC,SAAS,EAAE,CAAC,mBAAmB,EAAE,wBAAwB,EAAE,SAAS,CAAC;gBACrE,KAAK,EAAE;oBACL,GAAG,EAAE,IAAI,CAAC,QAAQ;iBACnB;aACF,CAAC,CAAC;YAEH,IAAI,KAAK,EAAE,CAAC;gBACV,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;oBACnB,MAAM,IAAI,8BAAqB,EAAE,CAAC;gBACpC,CAAC;gBAED,MAAM,KAAK,GAAkB,KAAK,CAAC,iBAAiB,CAAC,GAAG,CACtD,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CACzB,CAAC;gBAGF,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,sBAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACxC,MAAM,IAAI,8BAAqB,CAC7B,2CAA2C,CAC5C,CAAC;gBACJ,CAAC;gBAED,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;oBACtB,MAAM,iBAAiB,GAAG,MAAM,MAAM,CAAC,OAAO,CAC5C,IAAI,CAAC,YAAY,EACjB,KAAK,CAAC,YAAY,CACnB,CAAC;oBACF,IAAI,iBAAiB,EAAE,CAAC;wBACtB,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC;4BAC/B,GAAG,EAAE,KAAK,CAAC,GAAG;4BACd,KAAK,EAAE,KAAK,CAAC,KAAK;4BAClB,EAAE,EAAE,KAAK,CAAC,EAAE;4BACZ,KAAK;yBACN,CAAC,CAAC;wBAEH,MAAM,GAAG,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACzC,CAAC;yBAAM,CAAC;wBACN,MAAM,IAAI,4BAAmB,CAAC,mCAAmC,CAAC,CAAC;oBACrE,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,OAAO,CACxC,IAAI,CAAC,QAAQ,EACb,KAAK,CAAC,QAAQ,CACf,CAAC;oBAEF,IAAI,aAAa,EAAE,CAAC;wBAClB,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC;4BAC/B,GAAG,EAAE,KAAK,CAAC,GAAG;4BACd,KAAK,EAAE,KAAK,CAAC,KAAK;4BAClB,EAAE,EAAE,KAAK,CAAC,EAAE;4BACZ,KAAK;yBACN,CAAC,CAAC;wBACH,YAAY,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC;4BAC7C,EAAE,EAAE,KAAK,CAAC,EAAE;4BACZ,QAAQ,EAAE,KAAK,CAAC,GAAG;4BACnB,IAAI,EAAE,KAAK,CAAC,IAAI;4BAChB,KAAK,EAAE,KAAK,CAAC,KAAK;yBACnB,CAAC,CAAC;wBAEH,MAAM,GAAG,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACzC,CAAC;yBAAM,CAAC;wBACN,MAAM,IAAI,4BAAmB,CAAC,4BAA4B,CAAC,CAAC;oBAC9D,CAAC;gBACH,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,4BAAmB,CAAC,4BAA4B,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC;aAAM,IAAI,YAAY,KAAK,MAAM,EAAE,CAAC;YACnC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;gBAC7C,SAAS,EAAE,CAAC,mBAAmB,EAAE,wBAAwB,EAAE,SAAS,CAAC;gBACrE,KAAK,EAAE;oBACL,IAAI,EAAE,IAAI,CAAC,QAAQ;iBACpB;aACF,CAAC,CAAC;YAEH,IAAI,QAAQ,EAAE,CAAC;gBACb,MAAM,KAAK,GAAkB,QAAQ,CAAC,iBAAiB,CAAC,GAAG,CACzD,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CACzB,CAAC;gBAGF,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,sBAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACxC,MAAM,IAAI,8BAAqB,CAC7B,2CAA2C,CAC5C,CAAC;gBACJ,CAAC;gBAED,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;oBACtB,MAAM,iBAAiB,GAAG,MAAM,MAAM,CAAC,OAAO,CAC5C,IAAI,CAAC,YAAY,EACjB,QAAQ,CAAC,YAAY,CACtB,CAAC;oBACF,IAAI,iBAAiB,EAAE,CAAC;wBACtB,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC;4BAC/B,GAAG,EAAE,QAAQ,CAAC,IAAI;4BAClB,KAAK,EAAE,QAAQ,CAAC,KAAK;4BACrB,EAAE,EAAE,QAAQ,CAAC,EAAE;4BACf,KAAK;yBACN,CAAC,CAAC;wBAEH,MAAM,GAAG,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC5C,CAAC;yBAAM,CAAC;wBACN,MAAM,IAAI,4BAAmB,CAAC,mCAAmC,CAAC,CAAC;oBACrE,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,OAAO,CACxC,IAAI,CAAC,QAAQ,EACb,QAAQ,CAAC,QAAQ,CAClB,CAAC;oBACF,IAAI,aAAa,EAAE,CAAC;wBAClB,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC;4BAC/B,GAAG,EAAE,QAAQ,CAAC,IAAI;4BAClB,KAAK,EAAE,QAAQ,CAAC,KAAK;4BACrB,EAAE,EAAE,QAAQ,CAAC,EAAE;4BACf,KAAK;yBACN,CAAC,CAAC;wBACH,YAAY,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC;4BAC7C,EAAE,EAAE,QAAQ,CAAC,EAAE;4BACf,QAAQ,EAAE,QAAQ,CAAC,IAAI;4BACvB,IAAI,EAAE,QAAQ,CAAC,WAAW;4BAC1B,KAAK,EAAE,QAAQ,CAAC,KAAK;yBACtB,CAAC,CAAC;wBAEH,MAAM,GAAG,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC5C,CAAC;yBAAM,CAAC;wBACN,MAAM,IAAI,4BAAmB,CAAC,4BAA4B,CAAC,CAAC;oBAC9D,CAAC;gBACH,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,4BAAmB,CAAC,4BAA4B,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,4BAAmB,CAAC,4BAA4B,CAAC,CAAC;QAC9D,CAAC;QAGD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAE3E,OAAO;YACL,WAAW;YACX,YAAY;YACZ,OAAO,EAAE,WAAW;SACrB,CAAC;IACJ,CAAC;IAEO,SAAS,CAAC,QAAgB;QAChC,IAAI,QAAQ,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACzB,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,aAAa,CAAC,IAA2B;QAC/C,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE;YACvC,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU;YAC9B,SAAS,EAAE,IAAI;SAChB,CAAC,CAAC;QACH,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,IAKlC;QACC,MAAM,OAAO,GAAG;YACd,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,KAAK,EAAE,IAAI,CAAC,KAAK;SAClB,CAAC;QAEF,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE;YAC1C,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU;YAC9B,SAAS,EAAE,IAAI;SAChB,CAAC,CAAC;QAEH,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QAEhD,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACnD,IAAI,YAAY,KAAK,KAAK,EAAE,CAAC;YAC3B,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE;gBACjC,YAAY,EAAE,UAAU;aACzB,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,YAAY,KAAK,MAAM,EAAE,CAAC;YACnC,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE;gBACpC,YAAY,EAAE,UAAU;aACzB,CAAC,CAAC;QACL,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;CACF,CAAA;AAvNY,gDAAkB;6BAAlB,kBAAkB;IAD9B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,0BAAW,CAAC,CAAA;IAE7B,WAAA,IAAA,0BAAgB,EAAC,gCAAc,CAAC,CAAA;qCADhB,oBAAU;QAEP,oBAAU;QACV,gBAAU;QACA,6CAAoB;GAPzC,kBAAkB,CAuN9B", "sourcesContent": ["import {\r\n  BadRequestException,\r\n  Injectable,\r\n  UnauthorizedException,\r\n} from '@nestjs/common';\r\nimport { JwtService } from '@nestjs/jwt';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport * as bcrypt from 'bcrypt';\r\nimport { BusinessEntity } from 'src/shared/database/typeorm/entities/business.entity';\r\nimport { OwnerEntity } from 'src/shared/database/typeorm/entities/owner.entity';\r\nimport { RolesEnum } from 'src/shared/enums/roles.enum';\r\nimport { Repository } from 'typeorm';\r\n\r\nimport { ClientLoginDto } from '../dto/client-login.dto';\r\nimport { IClientLoginResponse } from '../responses/client-login.response';\r\nimport { ClientAccountService } from './client-account.service';\r\n\r\ninterface IGenerateTokenPayload {\r\n  id: string;\r\n  cpf: string;\r\n  email: string;\r\n  roles: Array<string>;\r\n}\r\n\r\n@Injectable()\r\nexport class ClientLoginService {\r\n  constructor(\r\n    @InjectRepository(OwnerEntity)\r\n    private ownerDb: Repository<OwnerEntity>,\r\n    @InjectRepository(BusinessEntity)\r\n    private businessDb: Repository<BusinessEntity>,\r\n    private jwtService: JwtService,\r\n    private clientAccountService: ClientAccountService,\r\n  ) {}\r\n\r\n  async perform(data: ClientLoginDto): Promise<IClientLoginResponse> {\r\n    if (!data.password && !data.refreshToken) {\r\n      throw new BadRequestException(\r\n        'Password ou RefreshToken deve ser fornecido',\r\n      );\r\n    }\r\n    const typeDocument = this.cpfOrCnpj(data.document);\r\n    let userId: string;\r\n    let accessToken: string;\r\n    let refreshToken: string | undefined;\r\n\r\n    if (typeDocument === 'cpf') {\r\n      const owner = await this.ownerDb.findOne({\r\n        relations: ['ownerRoleRelation', 'ownerRoleRelation.role', 'account'],\r\n        where: {\r\n          cpf: data.document,\r\n        },\r\n      });\r\n\r\n      if (owner) {\r\n        if (!owner.account) {\r\n          throw new UnauthorizedException();\r\n        }\r\n\r\n        const roles: Array<string> = owner.ownerRoleRelation.map(\r\n          (role) => role.role.name,\r\n        );\r\n\r\n        // Verificar se o usuário tem a role \"investor\"\r\n        if (!roles.includes(RolesEnum.INVESTOR)) {\r\n          throw new UnauthorizedException(\r\n            'Acesso permitido apenas para investidores',\r\n          );\r\n        }\r\n\r\n        if (data.refreshToken) {\r\n          const matchRefreshToken = await bcrypt.compare(\r\n            data.refreshToken,\r\n            owner.refreshToken,\r\n          );\r\n          if (matchRefreshToken) {\r\n            accessToken = this.generateToken({\r\n              cpf: owner.cpf,\r\n              email: owner.email,\r\n              id: owner.id,\r\n              roles,\r\n            });\r\n\r\n            userId = owner.ownerRoleRelation[0].id;\r\n          } else {\r\n            throw new BadRequestException('Usuário ou refreshToken inválidos');\r\n          }\r\n        } else {\r\n          const validPassword = await bcrypt.compare(\r\n            data.password,\r\n            owner.password,\r\n          );\r\n\r\n          if (validPassword) {\r\n            accessToken = this.generateToken({\r\n              cpf: owner.cpf,\r\n              email: owner.email,\r\n              id: owner.id,\r\n              roles,\r\n            });\r\n            refreshToken = await this.generateRefreshToken({\r\n              id: owner.id,\r\n              document: owner.cpf,\r\n              name: owner.name,\r\n              email: owner.email,\r\n            });\r\n\r\n            userId = owner.ownerRoleRelation[0].id;\r\n          } else {\r\n            throw new BadRequestException('Usuário ou senha inválidos');\r\n          }\r\n        }\r\n      } else {\r\n        throw new BadRequestException('Usuário ou senha inválidos');\r\n      }\r\n    } else if (typeDocument === 'cnpj') {\r\n      const business = await this.businessDb.findOne({\r\n        relations: ['ownerRoleRelation', 'ownerRoleRelation.role', 'account'],\r\n        where: {\r\n          cnpj: data.document,\r\n        },\r\n      });\r\n\r\n      if (business) {\r\n        const roles: Array<string> = business.ownerRoleRelation.map(\r\n          (role) => role.role.name,\r\n        );\r\n\r\n        // Verificar se o usuário tem a role \"investor\"\r\n        if (!roles.includes(RolesEnum.INVESTOR)) {\r\n          throw new UnauthorizedException(\r\n            'Acesso permitido apenas para investidores',\r\n          );\r\n        }\r\n\r\n        if (data.refreshToken) {\r\n          const matchRefreshToken = await bcrypt.compare(\r\n            data.refreshToken,\r\n            business.refreshToken,\r\n          );\r\n          if (matchRefreshToken) {\r\n            accessToken = this.generateToken({\r\n              cpf: business.cnpj,\r\n              email: business.email,\r\n              id: business.id,\r\n              roles,\r\n            });\r\n\r\n            userId = business.ownerRoleRelation[0].id;\r\n          } else {\r\n            throw new BadRequestException('Usuário ou refreshToken inválidos');\r\n          }\r\n        } else {\r\n          const validPassword = await bcrypt.compare(\r\n            data.password,\r\n            business.password,\r\n          );\r\n          if (validPassword) {\r\n            accessToken = this.generateToken({\r\n              cpf: business.cnpj,\r\n              email: business.email,\r\n              id: business.id,\r\n              roles,\r\n            });\r\n            refreshToken = await this.generateRefreshToken({\r\n              id: business.id,\r\n              document: business.cnpj,\r\n              name: business.companyName,\r\n              email: business.email,\r\n            });\r\n\r\n            userId = business.ownerRoleRelation[0].id;\r\n          } else {\r\n            throw new BadRequestException('Usuário ou senha inválidos');\r\n          }\r\n        }\r\n      } else {\r\n        throw new BadRequestException('Usuário ou senha inválidos');\r\n      }\r\n    } else {\r\n      throw new BadRequestException('Tipo de documento inválido');\r\n    }\r\n\r\n    // Buscar informações da conta do cliente\r\n    const accountInfo = await this.clientAccountService.getAccountInfo(userId);\r\n\r\n    return {\r\n      accessToken,\r\n      refreshToken,\r\n      account: accountInfo,\r\n    };\r\n  }\r\n\r\n  private cpfOrCnpj(document: string): string {\r\n    if (document.length > 11) {\r\n      return 'cnpj';\r\n    }\r\n    return 'cpf';\r\n  }\r\n\r\n  private generateToken(data: IGenerateTokenPayload) {\r\n    const token = this.jwtService.sign(data, {\r\n      secret: process.env.JWT_SECRET,\r\n      expiresIn: 3600,\r\n    });\r\n    return token;\r\n  }\r\n\r\n  private async generateRefreshToken(data: {\r\n    id: string;\r\n    document: string;\r\n    name: string;\r\n    email: string;\r\n  }) {\r\n    const payload = {\r\n      id: data.id,\r\n      document: data.document,\r\n      name: data.name,\r\n      email: data.email,\r\n    };\r\n\r\n    const token = this.jwtService.sign(payload, {\r\n      secret: process.env.JWT_SECRET,\r\n      expiresIn: '1y',\r\n    });\r\n\r\n    const cryptToken = await bcrypt.hash(token, 10);\r\n\r\n    const typeDocument = this.cpfOrCnpj(data.document);\r\n    if (typeDocument === 'cpf') {\r\n      await this.ownerDb.update(data.id, {\r\n        refreshToken: cryptToken,\r\n      });\r\n    } else if (typeDocument === 'cnpj') {\r\n      await this.businessDb.update(data.id, {\r\n        refreshToken: cryptToken,\r\n      });\r\n    }\r\n    return token;\r\n  }\r\n}\r\n"]}