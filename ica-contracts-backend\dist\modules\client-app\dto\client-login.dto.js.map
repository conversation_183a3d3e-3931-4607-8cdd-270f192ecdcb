{"version": 3, "file": "client-login.dto.js", "sourceRoot": "/", "sources": ["modules/client-app/dto/client-login.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,yEAAwD;AACxD,qDAAyE;AAEzE,MAAa,cAAc;CAc1B;AAdD,wCAcC;AAVC;IAHC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;IACV,IAAA,uCAAW,GAAE;;gDACG;AAIjB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;gDACO;AAKlB;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,uBAAK,GAAE;;oDACc", "sourcesContent": ["import { IsCPF<PERSON>r<PERSON>NPJ } from 'brazilian-class-validator';\r\nimport { IsDefined, IsJWT, IsOptional, IsString } from 'class-validator';\r\n\r\nexport class ClientLoginDto {\r\n  @IsDefined()\r\n  @IsString()\r\n  @IsCPFOrCNPJ()\r\n  document: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  password?: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  @IsJWT()\r\n  refreshToken?: string;\r\n}"]}