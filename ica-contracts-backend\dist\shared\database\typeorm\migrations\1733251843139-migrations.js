"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1733251843139 = void 0;
class Migrations1733251843139 {
    constructor() {
        this.name = 'Migrations1733251843139';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "pre_register" ADD "state" character varying`);
        await queryRunner.query(`ALTER TABLE "pre_register" ADD "mother_name" character varying`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "pre_register" DROP COLUMN "mother_name"`);
        await queryRunner.query(`ALTER TABLE "pre_register" DROP COLUMN "state"`);
    }
}
exports.Migrations1733251843139 = Migrations1733251843139;
//# sourceMappingURL=1733251843139-migrations.js.map