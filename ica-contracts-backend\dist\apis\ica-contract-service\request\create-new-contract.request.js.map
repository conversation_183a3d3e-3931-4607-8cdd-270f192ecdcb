{"version": 3, "file": "create-new-contract.request.js", "sourceRoot": "/", "sources": ["apis/ica-contract-service/request/create-new-contract.request.ts"], "names": [], "mappings": ";;;AAAA,IAAY,UAGT;AAHH,WAAY,UAAU;IAClB,uBAAS,CAAA;IACT,uBAAS,CAAA;AACX,CAAC,EAHS,UAAU,0BAAV,UAAU,QAGnB;AAED,IAAY,YAGX;AAHD,WAAY,YAAY;IACtB,2BAAW,CAAA;IACX,+BAAe,CAAA;AACjB,CAAC,EAHW,YAAY,4BAAZ,YAAY,QAGvB;AAED,IAAY,aAIX;AAJD,WAAY,aAAa;IACvB,4BAAW,CAAA;IACX,gDAA+B,CAAA;IAC/B,kCAAiB,CAAA;AACnB,CAAC,EAJW,aAAa,6BAAb,aAAa,QAIxB;AAED,IAAY,eAIX;AAJD,WAAY,eAAe;IACzB,gDAA6B,CAAA;IAC7B,wCAAqB,CAAA;IACrB,4CAAyB,CAAA;AAC3B,CAAC,EAJW,eAAe,+BAAf,eAAe,QAI1B;AAED,IAAY,gBASX;AATD,WAAY,gBAAgB;IAC1B,+BAAW,CAAA;IACX,6BAAS,CAAA;IACT,qCAAiB,CAAA;IACjB,iCAAa,CAAA;IACb,+BAAW,CAAA;IACX,6BAAS,CAAA;IACT,6BAAS,CAAA;IACT,2CAAuB,CAAA;AACzB,CAAC,EATW,gBAAgB,gCAAhB,gBAAgB,QAS3B", "sourcesContent": ["export enum PersonType {\r\n    PF = 'PF',\r\n    PJ = 'PJ',\r\n  }\r\n  \r\n  export enum ContractType {\r\n    SCP = 'SCP',\r\n    MUTUO = 'MUTUO',\r\n  }\r\n  \r\n  export enum PaymentMethod {\r\n    PIX = 'pix',\r\n    BANK_TRANSFER = 'bank_transfer',\r\n    BOLETO = 'boleto',\r\n  }\r\n  \r\n  export enum InvestorProfile {\r\n    CONSERVATIVE = 'conservative',\r\n    MODERATE = 'moderate',\r\n    AGGRESSIVE = 'aggressive',\r\n  }\r\n  \r\n  export enum CompanyLegalType {\r\n    MEI = 'MEI',\r\n    EI = 'EI',\r\n    EIRELI = 'EIRELI',\r\n    LTDA = 'LTDA',\r\n    SLU = 'SLU',\r\n    SA = 'SA',\r\n    SS = 'SS',\r\n    CONSORCIO = 'CONSORCIO',\r\n  }\r\n  \r\n  export interface CreateNewContractDTO {\r\n    personType: PersonType\r\n    contractType: ContractType\r\n    brokerId: string\r\n    investment: InvestmentDetailsDTO\r\n    advisors: AdvisorAssignmentDTO[]\r\n    bankAccount: BankAccountDTO\r\n  \r\n    individual?: IndividualDTO\r\n    company?: CompanyDTO\r\n  }\r\n  \r\n  export interface InvestmentDetailsDTO {\r\n    amount: number\r\n    monthlyRate: number\r\n    durationInMonths: number\r\n    paymentMethod: PaymentMethod\r\n    startDate?: string\r\n    endDate: string\r\n    profile?: InvestorProfile\r\n    quotaQuantity?: number\r\n    isDebenture?: boolean\r\n  }\r\n  \r\n  export interface AdvisorAssignmentDTO {\r\n    advisorId: string\r\n    rate: number\r\n  }\r\n  \r\n  export interface BankAccountDTO {\r\n    bank: string\r\n    agency: string\r\n    account: string\r\n    pix?: string\r\n  }\r\n  \r\n  export interface IndividualDTO {\r\n    fullName: string\r\n    cpf: string\r\n    rg: string\r\n    issuingAgency: string\r\n    nationality: string\r\n    occupation: string\r\n    birthDate: string\r\n    email: string\r\n    phone: string\r\n    motherName: string\r\n    address: AddressDTO\r\n  }\r\n  \r\n  export interface CompanyDTO {\r\n    corporateName: string\r\n    cnpj: string\r\n    type: CompanyLegalType\r\n    address: AddressDTO\r\n    representative: IndividualDTO\r\n  }\r\n  \r\n  export interface AddressDTO {\r\n    street: string\r\n    city: string\r\n    state: string\r\n    postalCode: string\r\n    number: string\r\n    neighborhood: string\r\n    complement?: string\r\n  }\r\n  "]}