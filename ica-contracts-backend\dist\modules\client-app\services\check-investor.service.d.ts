import { BusinessEntity } from 'src/shared/database/typeorm/entities/business.entity';
import { OwnerEntity } from 'src/shared/database/typeorm/entities/owner.entity';
import { Repository } from 'typeorm';
import { CheckInvestorDto } from '../dto/check-investor.dto';
import { ICheckInvestorResponse } from '../responses/check-investor.response';
export declare class CheckInvestorService {
    private ownerDb;
    private businessDb;
    constructor(ownerDb: Repository<OwnerEntity>, businessDb: Repository<BusinessEntity>);
    perform(data: CheckInvestorDto): Promise<ICheckInvestorResponse>;
    private cpfOrCnpj;
}
