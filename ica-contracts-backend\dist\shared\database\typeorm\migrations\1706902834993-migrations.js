"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1706902834993 = void 0;
class Migrations1706902834993 {
    constructor() {
        this.name = 'Migrations1706902834993';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "permission" ADD "role_id" uuid`);
        await queryRunner.query(`ALTER TABLE "permission" ADD CONSTRAINT "FK_383892d758d08d346f837d3d8b7" FOREIGN KEY ("role_id") REFERENCES "role"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "permission" DROP CONSTRAINT "FK_383892d758d08d346f837d3d8b7"`);
        await queryRunner.query(`ALTER TABLE "permission" DROP COLUMN "role_id"`);
    }
}
exports.Migrations1706902834993 = Migrations1706902834993;
//# sourceMappingURL=1706902834993-migrations.js.map