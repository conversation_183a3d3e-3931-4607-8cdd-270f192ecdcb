{"version": 3, "file": "1712851291606-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1712851291606-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IASnC,CAAC;IAPQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAC;IACvE,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CAAC,6CAA6C,CAAC,CAAC;IACzE,CAAC;CACF;AAVD,0DAUC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1712851291606 implements MigrationInterface {\r\n  name = 'Migrations1712851291606';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(`ALTER TABLE \"billet\" ADD \"metadata\" jsonb`);\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(`ALTER TABLE \"billet\" DROP COLUMN \"metadata\"`);\r\n  }\r\n}\r\n"]}