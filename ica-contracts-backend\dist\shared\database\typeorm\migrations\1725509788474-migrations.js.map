{"version": 3, "file": "1725509788474-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1725509788474-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAiCnC,CAAC;IA/BQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,g3BAAg3B,CACj3B,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,mMAAmM,CACpM,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,sLAAsL,CACvL,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,qLAAqL,CACtL,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,sLAAsL,CACvL,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,8EAA8E,CAC/E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,8EAA8E,CAC/E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,6EAA6E,CAC9E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;QACtD,MAAM,WAAW,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;IACvD,CAAC;CACF;AAlCD,0DAkCC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1725509788474 implements MigrationInterface {\r\n  name = 'Migrations1725509788474';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"pre-register\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"name\" character varying NOT NULL, \"rg\" character varying NOT NULL, \"document\" character varying NOT NULL, \"phone_number\" character varying NOT NULL, \"dt_birth\" date NOT NULL, \"email\" character varying NOT NULL, \"zip_code\" character varying NOT NULL, \"neighborhood\" character varying NOT NULL, \"city\" character varying NOT NULL, \"address_complement\" character varying, \"address_number\" character varying NOT NULL, \"investment_value\" numeric NOT NULL, \"investment_term\" character varying NOT NULL, \"investment_yield\" numeric NOT NULL, \"investment_modality\" character varying NOT NULL, \"observations\" character varying NOT NULL, \"status\" character varying NOT NULL, \"adviser_id\" uuid NOT NULL, \"created_at\" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT \"PK_e4c72f402a26abc5ad01f261090\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"wallets-views\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"upper_id\" uuid NOT NULL, \"bottom_id\" uuid NOT NULL, CONSTRAINT \"PK_e339cce0810748d2b6ffa38133d\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pre-register\" ADD CONSTRAINT \"FK_862fec7c8cbcef898f519c5c0ff\" FOREIGN KEY (\"adviser_id\") REFERENCES \"owner_role_relation\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"wallets-views\" ADD CONSTRAINT \"FK_834b2d2f9cd1d6ffaf3214968c6\" FOREIGN KEY (\"upper_id\") REFERENCES \"owner_role_relation\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"wallets-views\" ADD CONSTRAINT \"FK_7cde3a0398069bbf43680cd8d12\" FOREIGN KEY (\"bottom_id\") REFERENCES \"owner_role_relation\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"wallets-views\" DROP CONSTRAINT \"FK_7cde3a0398069bbf43680cd8d12\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"wallets-views\" DROP CONSTRAINT \"FK_834b2d2f9cd1d6ffaf3214968c6\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pre-register\" DROP CONSTRAINT \"FK_862fec7c8cbcef898f519c5c0ff\"`,\r\n    );\r\n    await queryRunner.query(`DROP TABLE \"wallets-views\"`);\r\n    await queryRunner.query(`DROP TABLE \"pre-register\"`);\r\n  }\r\n}\r\n"]}