"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1713285937446 = void 0;
class Migrations1713285937446 {
    constructor() {
        this.name = 'Migrations1713285937446';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "billet" DROP COLUMN "amount"`);
        await queryRunner.query(`ALTER TABLE "billet" ADD "amount" character varying NOT NULL`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "billet" DROP COLUMN "amount"`);
        await queryRunner.query(`ALTER TABLE "billet" ADD "amount" integer NOT NULL`);
    }
}
exports.Migrations1713285937446 = Migrations1713285937446;
//# sourceMappingURL=1713285937446-migrations.js.map