import { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';
import { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';
import { Repository } from 'typeorm';
import { IClientContractResponse, IClientContractAddendumResponse } from '../responses/client-contract.response';
export declare class ClientContractService {
    private readonly contractRepository;
    private readonly ownerRoleRelationRepository;
    private readonly formatDate;
    constructor(contractRepository: Repository<ContractEntity>, ownerRoleRelationRepository: Repository<OwnerRoleRelationEntity>, formatDate: (date: Date | string) => string);
    getContractsByUserId(userId: string): Promise<IClientContractResponse[]>;
    getContractAddendums(contractId: string): Promise<IClientContractAddendumResponse[]>;
}
