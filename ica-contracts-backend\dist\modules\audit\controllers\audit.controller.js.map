{"version": 3, "file": "audit.controller.js", "sourceRoot": "/", "sources": ["modules/audit/controllers/audit.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CASwB;AACxB,gFAA8D;AAC9D,iEAAwD;AACxD,0EAAgE;AAChE,kEAAyD;AAGzD,kEAA6D;AAC7D,+EAA0E;AAC1E,uGAAgG;AAGzF,IAAM,eAAe,GAArB,MAAM,eAAe;IAC1B,YACmB,oBAA0C,EAC1C,8BAA8D;QAD9D,yBAAoB,GAApB,oBAAoB,CAAsB;QAC1C,mCAA8B,GAA9B,8BAA8B,CAAgC;IAC9E,CAAC;IAKE,AAAN,KAAK,CAAC,aAAa,CACT,SAA2B,EACxB,OAAqB;QAEhC,OAAO,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAC5C,SAAS,EACT,OAAO,CAAC,IAAI,CAAC,EAAE,CAChB,CAAC;IACJ,CAAC;IAKK,AAAN,KAAK,CAAC,iBAAiB,CACe,UAAkB,EAC3C,OAAqB;QAEhC,OAAO,IAAI,CAAC,8BAA8B,CAAC,OAAO,CAChD,UAAU,EACV,OAAO,CAAC,IAAI,CAAC,EAAE,CAChB,CAAC;IACJ,CAAC;CACF,CAAA;AA/BY,0CAAe;AASpB;IAHL,IAAA,aAAI,EAAC,UAAU,CAAC;IAChB,IAAA,kBAAS,EAAC,6BAAY,EAAE,sBAAS,CAAC;IAClC,IAAA,uBAAK,EAAC,sBAAS,CAAC,KAAK,EAAE,sBAAS,CAAC,UAAU,CAAC;IAE1C,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,gBAAO,GAAE,CAAA;;qCADS,qCAAgB;;oDAOpC;AAKK;IAHL,IAAA,YAAG,EAAC,sBAAsB,CAAC;IAC3B,IAAA,kBAAS,EAAC,6BAAY,EAAE,sBAAS,CAAC;IAClC,IAAA,uBAAK,EAAC,sBAAS,CAAC,KAAK,EAAE,sBAAS,CAAC,UAAU,EAAE,sBAAS,CAAC,MAAM,EAAE,sBAAS,CAAC,OAAO,EAAE,sBAAS,CAAC,QAAQ,CAAC;IAEnG,WAAA,IAAA,cAAK,EAAC,YAAY,EAAE,sBAAa,CAAC,CAAA;IAClC,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;wDAMX;0BA9BU,eAAe;IAD3B,IAAA,mBAAU,EAAC,OAAO,CAAC;qCAGuB,6CAAoB;QACV,mEAA8B;GAHtE,eAAe,CA+B3B", "sourcesContent": ["import {\r\n  Body,\r\n  Controller,\r\n  Get,\r\n  Param,\r\n  Post,\r\n  Request,\r\n  UseGuards,\r\n  ParseUUIDPipe,\r\n} from '@nestjs/common';\r\nimport { Roles } from 'src/shared/decorators/roles.decorator';\r\nimport { RolesEnum } from 'src/shared/enums/roles.enum';\r\nimport { JwtAuthGuard } from 'src/shared/guards/jwt-auth.guard';\r\nimport { RoleGuard } from 'src/shared/guards/role.guard';\r\nimport { IRequestUser } from 'src/shared/interfaces/request-user.interface';\r\n\r\nimport { AuditContractDto } from '../dto/audit-contract.dto';\r\nimport { AuditContractService } from '../services/audit-contract.service';\r\nimport { GetContractAuditHistoryService } from '../services/get-contract-audit-history.service';\r\n\r\n@Controller('audit')\r\nexport class AuditController {\r\n  constructor(\r\n    private readonly auditContractService: AuditContractService,\r\n    private readonly getContractAuditHistoryService: GetContractAuditHistoryService,\r\n  ) {}\r\n\r\n  @Post('contract')\r\n  @UseGuards(JwtAuthGuard, RoleGuard)\r\n  @Roles(RolesEnum.ADMIN, RolesEnum.SUPERADMIN)\r\n  async auditContract(\r\n    @Body() auditData: AuditContractDto,\r\n    @Request() request: IRequestUser,\r\n  ) {\r\n    return this.auditContractService.auditContract(\r\n      auditData,\r\n      request.user.id,\r\n    );\r\n  }\r\n\r\n  @Get('contract/:contractId')\r\n  @UseGuards(JwtAuthGuard, RoleGuard)\r\n  @Roles(RolesEnum.ADMIN, RolesEnum.SUPERADMIN, RolesEnum.BROKER, RolesEnum.ADVISOR, RolesEnum.INVESTOR)\r\n  async getContractAudits(\r\n    @Param('contractId', ParseUUIDPipe) contractId: string,\r\n    @Request() request: IRequestUser,\r\n  ) {\r\n    return this.getContractAuditHistoryService.execute(\r\n      contractId,\r\n      request.user.id,\r\n    );\r\n  }\r\n}"]}