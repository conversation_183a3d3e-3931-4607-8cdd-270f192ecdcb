{"version": 3, "file": "main.js", "sourceRoot": "/", "sources": ["main.ts"], "names": [], "mappings": ";;AAAA,2CAAqE;AACrE,uCAA2C;AAE3C,6CAAiE;AACjE,gDAAgD;AAEhD,6CAAyC;AAEzC,KAAK,UAAU,SAAS;IACtB,MAAM,GAAG,GAAG,MAAM,kBAAW,CAAC,MAAM,CAAyB,sBAAS,EAAE;QACtE,MAAM,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,CAAC;KACrD,CAAC,CAAC;IAEH,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa;QACxC,GAAG,CAAC,GAAG,CACL,CAAC,OAAO,EAAE,YAAY,CAAC,EACvB,SAAS,CAAC;YACR,SAAS,EAAE,IAAI;YACf,KAAK,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE;SACpE,CAAC,CACH,CAAC;IACJ,GAAG,CAAC,UAAU,CAAC;QACb,cAAc,EAAE,GAAG;QACnB,OAAO,EAAE,wCAAwC;QACjD,MAAM,EAAE,GAAG;KACZ,CAAC,CAAC;IAEH,MAAM,MAAM,GAAG,IAAI,yBAAe,EAAE;SACjC,QAAQ,CAAC,uBAAuB,CAAC;SACjC,cAAc,CACb,2HAA2H,CAC5H;SACA,UAAU,CAAC,KAAK,CAAC;SACjB,MAAM,CAAC,eAAe,CAAC;SACvB,aAAa,CACZ;QACE,IAAI,EAAE,QAAQ;QACd,MAAM,EAAE,QAAQ;QAChB,YAAY,EAAE,KAAK;QACnB,IAAI,EAAE,eAAe;QACrB,WAAW,EAAE,wCAAwC;QACrD,EAAE,EAAE,QAAQ;KACb,EACD,QAAQ,CACT;SACA,KAAK,EAAE,CAAC;IAEX,MAAM,eAAe,GAAG,GAAG,EAAE,CAAC,uBAAa,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;IACxE,uBAAa,CAAC,KAAK,CAAC,MAAM,EAAE,GAAG,EAAE,eAAe,EAAE;QAChD,eAAe,EAAE,cAAc;QAC/B,cAAc,EAAE;YACd,oBAAoB,EAAE,IAAI;YAC1B,wBAAwB,EAAE,CAAC,CAAC;SAC7B;KACF,CAAC,CAAC;IAEH,GAAG,CAAC,UAAU,CAAC;QACb,cAAc,EAAE,GAAG;QACnB,OAAO,EAAE,wCAAwC;QACjD,MAAM,EAAE,GAAG;KACZ,CAAC,CAAC;IAEH,GAAG,CAAC,cAAc,CAChB,IAAI,uBAAc,CAAC;QACjB,SAAS,EAAE,IAAI;QACf,eAAe,EAAE;YACf,MAAM,EAAE,KAAK;YACb,KAAK,EAAE,KAAK;SACb;QAGD,gBAAgB,EAAE,CAAC,gBAAgB,GAAG,EAAE,EAAE,EAAE;YAC1C,MAAM,eAAe,GAAG,CAAC,MAAM,EAAE,UAAU,GAAG,EAAE,EAAY,EAAE;gBAC5D,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;oBAC9B,MAAM,SAAS,GAAG,UAAU;wBAC1B,CAAC,CAAC,GAAG,UAAU,IAAI,KAAK,CAAC,QAAQ,EAAE;wBACnC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC;oBAEnB,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,IAAI,EAAE,CAAC,CAAC,GAAG,CACzD,CAAC,GAAW,EAAE,EAAE;wBAEd,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,IAAI,SAAS,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;oBACzD,CAAC,CACF,CAAC;oBAEF,MAAM,gBAAgB,GAAG,KAAK,CAAC,QAAQ,EAAE,MAAM;wBAC7C,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,QAAQ,EAAE,SAAS,CAAC;wBAC5C,CAAC,CAAC,EAAE,CAAC;oBAEP,OAAO,CAAC,GAAG,QAAQ,EAAE,GAAG,gBAAgB,CAAC,CAAC;gBAC5C,CAAC,CAAC,CAAC;YACL,CAAC,CAAC;YAEF,MAAM,QAAQ,GAAG,eAAe,CAAC,gBAAgB,CAAC,CAAC;YAEnD,OAAO,IAAI,4BAAmB,CAAC,QAAQ,CAAC,CAAC;QAC3C,CAAC;KACF,CAAC,CACH,CAAC;IAEF,MAAM,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC;IAE3C,OAAO,CAAC,GAAG,CAAC,0BAA0B,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC,CAAC;AACpE,CAAC;AAED,SAAS,EAAE,CAAC", "sourcesContent": ["import { BadRequestException, ValidationPipe } from '@nestjs/common';\r\nimport { NestFactory } from '@nestjs/core';\r\nimport { NestExpressApplication } from '@nestjs/platform-express';\r\nimport { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';\r\nimport * as basicAuth from 'express-basic-auth';\r\n\r\nimport { AppModule } from './app.module';\r\n\r\nasync function bootstrap() {\r\n  const app = await NestFactory.create<NestExpressApplication>(AppModule, {\r\n    logger: ['error', 'warn', 'log', 'debug', 'verbose'],\r\n  });\r\n\r\n  if (process.env.NODE_ENV !== 'development')\r\n    app.use(\r\n      ['/docs', '/docs-json'],\r\n      basicAuth({\r\n        challenge: true,\r\n        users: { [process.env.SWAGGER_USER]: process.env.SWAGGER_PASSWORD },\r\n      }),\r\n    );\r\n  app.enableCors({\r\n    allowedHeaders: '*',\r\n    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',\r\n    origin: '*',\r\n  });\r\n\r\n  const config = new DocumentBuilder()\r\n    .setTitle('ICA Contracts Backend')\r\n    .setDescription(\r\n      'O INVEST Contracts Back-end é uma API modular e escalável construída com NESTJS para gerenciar contratos de investimento.',\r\n    )\r\n    .setVersion('1.0')\r\n    .addTag('ica-contracts')\r\n    .addBearerAuth(\r\n      {\r\n        type: 'apiKey',\r\n        scheme: 'bearer',\r\n        bearerFormat: 'JWT',\r\n        name: 'Authorization',\r\n        description: 'Adicionar o conteúdo de Bearer + token',\r\n        in: 'header',\r\n      },\r\n      'Bearer',\r\n    )\r\n    .build();\r\n\r\n  const documentFactory = () => SwaggerModule.createDocument(app, config);\r\n  SwaggerModule.setup('docs', app, documentFactory, {\r\n    jsonDocumentUrl: 'swagger/json',\r\n    swaggerOptions: {\r\n      persistAuthorization: true,\r\n      defaultModelsExpandDepth: -1,\r\n    },\r\n  });\r\n\r\n  app.enableCors({\r\n    allowedHeaders: '*',\r\n    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',\r\n    origin: '*',\r\n  });\r\n\r\n  app.useGlobalPipes(\r\n    new ValidationPipe({\r\n      transform: true,\r\n      validationError: {\r\n        target: false,\r\n        value: false,\r\n      },\r\n\r\n      //função para extrair mensagens de erro de validação\r\n      exceptionFactory: (validationErrors = []) => {\r\n        const extractMessages = (errors, parentPath = ''): string[] => {\r\n          return errors.flatMap((error) => {\r\n            const fieldPath = parentPath\r\n              ? `${parentPath}.${error.property}`\r\n              : error.property;\r\n\r\n            const messages = Object.values(error.constraints || {}).map(\r\n              (msg: string) => {\r\n                // Remove o prefixo do caminho do campo da mensagem, se houver\r\n                return msg.replace(new RegExp(`^${fieldPath}\\\\.`), '');\r\n              },\r\n            );\r\n\r\n            const childrenMessages = error.children?.length\r\n              ? extractMessages(error.children, fieldPath)\r\n              : [];\r\n\r\n            return [...messages, ...childrenMessages];\r\n          });\r\n        };\r\n\r\n        const messages = extractMessages(validationErrors);\r\n\r\n        return new BadRequestException(messages);\r\n      },\r\n    }),\r\n  );\r\n\r\n  await app.listen(process.env.PORT || 3000);\r\n\r\n  console.log(`Server running on port ${process.env.PORT || 3000}`);\r\n}\r\n\r\nbootstrap();\r\n"]}