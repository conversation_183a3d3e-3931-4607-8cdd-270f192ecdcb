{"version": 3, "file": "1706648182227-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1706648182227-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAmBnC,CAAC;IAjBQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,wEAAwE,CACzE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,gEAAgE,CACjE,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,+DAA+D,CAChE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,oEAAoE,CACrE,CAAC;IACJ,CAAC;CACF;AApBD,0DAoBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1706648182227 implements MigrationInterface {\r\n  name = 'Migrations1706648182227';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner\" ALTER COLUMN \"temporary_password\" SET DEFAULT true`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner\" ALTER COLUMN \"refresh_token\" DROP NOT NULL`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner\" ALTER COLUMN \"refresh_token\" SET NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner\" ALTER COLUMN \"temporary_password\" DROP DEFAULT`,\r\n    );\r\n  }\r\n}\r\n"]}