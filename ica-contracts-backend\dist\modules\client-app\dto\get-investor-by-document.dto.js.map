{"version": 3, "file": "get-investor-by-document.dto.js", "sourceRoot": "/", "sources": ["modules/client-app/dto/get-investor-by-document.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,yEAAwD;AACxD,qDAAsD;AAEtD,MAAa,wBAAwB;CAKpC;AALD,4DAKC;AADC;IAHC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;IACV,IAAA,uCAAW,GAAE;;0DACG", "sourcesContent": ["import { IsCPFOrCNPJ } from 'brazilian-class-validator';\r\nimport { IsDefined, IsString } from 'class-validator';\r\n\r\nexport class GetInvestorByDocumentDto {\r\n  @IsDefined()\r\n  @IsString()\r\n  @IsCPFOrCNPJ()\r\n  document: string;\r\n}"]}