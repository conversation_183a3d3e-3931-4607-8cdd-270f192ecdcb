{"version": 3, "file": "1743801861780-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1743801861780-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAenC,CAAC;IAbQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CAAC;;;SAGnB,CAAC,CAAC;IACT,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CAAC;;;SAGnB,CAAC,CAAC;IACT,CAAC;CACF;AAhBD,0DAgBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1743801861780 implements MigrationInterface {\r\n  name = 'Migrations1743801861780';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(`\r\n            ALTER TABLE \"email\" \r\n            ADD COLUMN \"external_id\" character varying\r\n        `);\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(`\r\n            ALTER TABLE \"email\" \r\n            DROP COLUMN \"external_id\"\r\n        `);\r\n  }\r\n}\r\n"]}