{"version": 3, "file": "client-login.controller.js", "sourceRoot": "/", "sources": ["modules/client-app/controllers/client-login.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAOwB;AAExB,8DAAyD;AACzD,2EAAsE;AAG/D,IAAM,qBAAqB,GAA3B,MAAM,qBAAqB;IAChC,YAEmB,kBAAsC;QAAtC,uBAAkB,GAAlB,kBAAkB,CAAoB;IACtD,CAAC;IAGE,AAAN,KAAK,CAAC,KAAK,CAET,IAAoB;QAEpB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC7D,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACrB,MAAM,IAAI,sBAAa,CACrB;gBACE,MAAM,EAAE,mBAAU,CAAC,WAAW;aAC/B,EACD,mBAAU,CAAC,WAAW,EACtB;gBACE,KAAK,EAAE,KAAK;aACb,CACF,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AA3BY,sDAAqB;AAO1B;IADL,IAAA,aAAI,EAAC,OAAO,CAAC;IAEX,WAAA,IAAA,aAAI,GAAE,CAAA;;qCACD,iCAAc;;kDAiBrB;gCA1BU,qBAAqB;IADjC,IAAA,mBAAU,EAAC,aAAa,CAAC;IAGrB,WAAA,IAAA,eAAM,EAAC,yCAAkB,CAAC,CAAA;qCACU,yCAAkB;GAH9C,qBAAqB,CA2BjC", "sourcesContent": ["import {\r\n  Body,\r\n  Controller,\r\n  HttpException,\r\n  HttpStatus,\r\n  Inject,\r\n  Post,\r\n} from '@nestjs/common';\r\n\r\nimport { ClientLoginDto } from '../dto/client-login.dto';\r\nimport { ClientLoginService } from '../services/client-login.service';\r\n\r\n@Controller('client/auth')\r\nexport class ClientLoginController {\r\n  constructor(\r\n    @Inject(ClientLoginService)\r\n    private readonly clientLoginService: ClientLoginService,\r\n  ) {}\r\n\r\n  @Post('login')\r\n  async login(\r\n    @Body()\r\n    body: ClientLoginDto,\r\n  ) {\r\n    try {\r\n      const response = await this.clientLoginService.perform(body);\r\n      return response;\r\n    } catch (error) {\r\n      console.error(error);\r\n      throw new HttpException(\r\n        {\r\n          status: HttpStatus.BAD_REQUEST,\r\n        },\r\n        HttpStatus.BAD_REQUEST,\r\n        {\r\n          cause: error,\r\n        },\r\n      );\r\n    }\r\n  }\r\n}\r\n"]}