{"version": 3, "file": "type-uploads.enum.js", "sourceRoot": "/", "sources": ["shared/enums/type-uploads.enum.ts"], "names": [], "mappings": ";;;AAAA,IAAY,eASX;AATD,WAAY,eAAe;IACzB,4BAAS,CAAA;IACT,8BAAW,CAAA;IACX,8BAAW,CAAA;IACX,0CAAuB,CAAA;IACvB,8BAAW,CAAA;IACX,sDAAmC,CAAA;IACnC,oDAAiC,CAAA;IACjC,sDAAmC,CAAA;AACrC,CAAC,EATW,eAAe,+BAAf,eAAe,QAS1B", "sourcesContent": ["export enum TypeUploadsEnum {\r\n  RG = 'RG',\r\n  CNH = 'CNH',\r\n  RNE = 'RNE',\r\n  CARD_CNPJ = 'CARD_CNPJ',\r\n  MEI = 'MEI',\r\n  SOCIAL_CONTRACT = 'SOCIAL_CONTRACT',\r\n  PARTNERSHIP_CONTRACT = 'CONTRACT',\r\n  PROOF_RESIDENCE = 'PROOF_RESIDENCE',\r\n}\r\n"]}