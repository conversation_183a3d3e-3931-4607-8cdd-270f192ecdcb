"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1732163845536 = void 0;
class Migrations1732163845536 {
    constructor() {
        this.name = 'Migrations1732163845536';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "contract" ADD "type" character varying NOT NULL DEFAULT 'p2p'`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "type"`);
    }
}
exports.Migrations1732163845536 = Migrations1732163845536;
//# sourceMappingURL=1732163845536-migrations.js.map