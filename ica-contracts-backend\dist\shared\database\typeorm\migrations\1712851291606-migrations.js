"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1712851291606 = void 0;
class Migrations1712851291606 {
    constructor() {
        this.name = 'Migrations1712851291606';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "billet" ADD "metadata" jsonb`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "billet" DROP COLUMN "metadata"`);
    }
}
exports.Migrations1712851291606 = Migrations1712851291606;
//# sourceMappingURL=1712851291606-migrations.js.map