import { JwtService } from '@nestjs/jwt';
import { BusinessEntity } from 'src/shared/database/typeorm/entities/business.entity';
import { OwnerEntity } from 'src/shared/database/typeorm/entities/owner.entity';
import { Repository } from 'typeorm';
import { ClientLoginDto } from '../dto/client-login.dto';
import { IClientLoginResponse } from '../responses/client-login.response';
import { ClientAccountService } from './client-account.service';
export declare class ClientLoginService {
    private ownerDb;
    private businessDb;
    private jwtService;
    private clientAccountService;
    constructor(ownerDb: Repository<OwnerEntity>, businessDb: Repository<BusinessEntity>, jwtService: JwtService, clientAccountService: ClientAccountService);
    perform(data: ClientLoginDto): Promise<IClientLoginResponse>;
    private cpfOrCnpj;
    private generateToken;
    private generateRefreshToken;
}
