{"version": 3, "file": "create-business.service.js", "sourceRoot": "/", "sources": ["modules/business/services/create-business.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAiE;AACjE,6CAAmD;AACnD,mCAAkC;AAClC,+FAAsF;AACtF,yFAAgF;AAChF,qCAAqC;AAK9B,IAAM,qBAAqB,GAA3B,MAAM,qBAAqB;IAChC,YAEmB,eAAwC,EAExC,kBAA8C;QAF9C,oBAAe,GAAf,eAAe,CAAyB;QAExC,uBAAkB,GAAlB,kBAAkB,CAA4B;IAC9D,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,KAA+B;QAC3C,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;YAC9D,KAAK,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE;SAC5B,CAAC,CAAC;QAEH,IAAI,kBAAkB;YACpB,MAAM,IAAI,4BAAmB,CAAC,wBAAwB,CAAC,CAAC;QAE1D,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAEzC,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;YACtC,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,GAAG,EAAE,KAAK,CAAC,GAAG;YACd,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,QAAQ;YACR,qBAAqB,EAAE;gBACrB;oBACE,QAAQ,EAAE;wBACR,IAAI,EAAE,KAAK,CAAC,IAAI;wBAChB,WAAW,EAAE,KAAK,CAAC,WAAW;wBAC9B,KAAK,EAAE,KAAK,CAAC,KAAK;qBACnB;iBACF;aACF;SACF,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,gBAAgB;QACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QACvD,MAAM,eAAe,GAAG,IAAA,iBAAQ,EAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;QAC9C,OAAO,eAAe,CAAC;IACzB,CAAC;CACF,CAAA;AA1CY,sDAAqB;gCAArB,qBAAqB;IADjC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,0BAAW,CAAC,CAAA;IAE7B,WAAA,IAAA,0BAAgB,EAAC,gCAAc,CAAC,CAAA;qCADC,oBAAU;QAEP,oBAAU;GALtC,qBAAqB,CA0CjC", "sourcesContent": ["import { BadRequestException, Injectable } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { hashSync } from 'bcrypt';\r\nimport { BusinessEntity } from 'src/shared/database/typeorm/entities/business.entity';\r\nimport { OwnerEntity } from 'src/shared/database/typeorm/entities/owner.entity';\r\nimport { Repository } from 'typeorm';\r\n\r\nimport { CreateBusinessRequestDto } from '../dto/create-business.dto';\r\n\r\n@Injectable()\r\nexport class CreateBusinessService {\r\n  constructor(\r\n    @InjectRepository(OwnerEntity)\r\n    private readonly ownerRepository: Repository<OwnerEntity>,\r\n    @InjectRepository(BusinessEntity)\r\n    private readonly businessRepository: Repository<BusinessEntity>,\r\n  ) {}\r\n\r\n  async perform(input: CreateBusinessRequestDto) {\r\n    const ownerAlreadyExists = await this.businessRepository.exists({\r\n      where: { cnpj: input.cnpj },\r\n    });\r\n\r\n    if (ownerAlreadyExists)\r\n      throw new BadRequestException('Empresa já cadastrada.');\r\n\r\n    const password = this.generatePassword();\r\n\r\n    const owner = this.ownerRepository.save({\r\n      name: input.name,\r\n      cpf: input.cpf,\r\n      email: input.email,\r\n      password,\r\n      ownerBusinessRelation: [\r\n        {\r\n          business: {\r\n            cnpj: input.cnpj,\r\n            companyName: input.companyName,\r\n            email: input.email,\r\n          },\r\n        },\r\n      ],\r\n    });\r\n\r\n    return owner;\r\n  }\r\n\r\n  private generatePassword(): string {\r\n    const password = Math.random().toString(36).slice(-10);\r\n    const encryptPassword = hashSync(password, 1);\r\n    return encryptPassword;\r\n  }\r\n}\r\n"]}