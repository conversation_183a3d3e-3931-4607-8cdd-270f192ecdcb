{"version": 3, "file": "1728335050216-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1728335050216-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAuBnC,CAAC;IArBQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,sbAAsb,CACvb,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,8KAA8K,CAC/K,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,4LAA4L,CAC7L,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,+EAA+E,CAChF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,+EAA+E,CAChF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;IACzD,CAAC;CACF;AAxBD,0DAwBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1728335050216 implements MigrationInterface {\r\n  name = 'Migrations1728335050216';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"contract_event\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"eventType\" character varying(50) NOT NULL, \"eventDate\" date NOT NULL, \"status\" character varying(50), \"comment\" text, \"destinationArea\" character varying(50), \"createdAt\" TIMESTAMP NOT NULL DEFAULT now(), \"updatedAt\" TIMESTAMP NOT NULL DEFAULT now(), \"contract_id\" uuid, \"responsible_id\" uuid, CONSTRAINT \"PK_a0a0fdb2918e838e546c3b5fd01\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract_event\" ADD CONSTRAINT \"FK_654c5b3eae51e91ad465ad7daa0\" FOREIGN KEY (\"contract_id\") REFERENCES \"contract\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract_event\" ADD CONSTRAINT \"FK_196c2c26c5048d8f6760bbc281e\" FOREIGN KEY (\"responsible_id\") REFERENCES \"owner_role_relation\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract_event\" DROP CONSTRAINT \"FK_196c2c26c5048d8f6760bbc281e\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract_event\" DROP CONSTRAINT \"FK_654c5b3eae51e91ad465ad7daa0\"`,\r\n    );\r\n    await queryRunner.query(`DROP TABLE \"contract_event\"`);\r\n  }\r\n}\r\n"]}