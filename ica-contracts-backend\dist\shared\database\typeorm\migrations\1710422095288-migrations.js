"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1710422095288 = void 0;
class Migrations1710422095288 {
    constructor() {
        this.name = 'Migrations1710422095288';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "pix-key" ADD "claim_metadata" character varying`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "pix-key" DROP COLUMN "claim_metadata"`);
    }
}
exports.Migrations1710422095288 = Migrations1710422095288;
//# sourceMappingURL=1710422095288-migrations.js.map