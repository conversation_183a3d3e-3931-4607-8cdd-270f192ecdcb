{"version": 3, "file": "1727131367107-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1727131367107-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAmBnC,CAAC;IAjBQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,6DAA6D,CAC9D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,8DAA8D,CAC/D,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,oDAAoD,CACrD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,mDAAmD,CACpD,CAAC;IACJ,CAAC;CACF;AApBD,0DAoBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1727131367107 implements MigrationInterface {\r\n  name = 'Migrations1727131367107';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract\" ADD \"contract_pdf\" character varying`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract\" ADD \"proof_payment\" character varying`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract\" DROP COLUMN \"proof_payment\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract\" DROP COLUMN \"contract_pdf\"`,\r\n    );\r\n  }\r\n}\r\n"]}