{"version": 3, "file": "get-investor-by-document.controller.js", "sourceRoot": "/", "sources": ["modules/client-app/controllers/get-investor-by-document.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAOwB;AAExB,sFAA+E;AAC/E,mGAA4F;AAGrF,IAAM,+BAA+B,GAArC,MAAM,+BAA+B;IAC1C,YACmB,4BAA0D;QAA1D,iCAA4B,GAA5B,4BAA4B,CAA8B;IAC1E,CAAC;IAGE,AAAN,KAAK,CAAC,qBAAqB,CAChB,MAAgC,EACnB,MAAc;QAEpC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,sBAAa,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC;YACxD,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAC5D,MAAM,EACN,MAAM,CACP,CAAC;YACF,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB;gBACE,MAAM,EAAE,mBAAU,CAAC,WAAW;aAC/B,EACD,mBAAU,CAAC,WAAW,EACtB;gBACE,KAAK,EAAE,KAAK;aACb,CACF,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AAhCY,0EAA+B;AAMpC;IADL,IAAA,YAAG,EAAC,WAAW,CAAC;IAEd,WAAA,IAAA,cAAK,GAAE,CAAA;IACP,WAAA,IAAA,gBAAO,EAAC,WAAW,CAAC,CAAA;;qCADJ,uDAAwB;;4EAwB1C;0CA/BU,+BAA+B;IAD3C,IAAA,mBAAU,EAAC,iBAAiB,CAAC;qCAGqB,+DAA4B;GAFlE,+BAA+B,CAgC3C", "sourcesContent": ["import {\r\n  <PERSON>,\r\n  Get,\r\n  Param,\r\n  Headers,\r\n  HttpException,\r\n  HttpStatus,\r\n} from '@nestjs/common';\r\n\r\nimport { GetInvestorByDocumentDto } from '../dto/get-investor-by-document.dto';\r\nimport { GetInvestorByDocumentService } from '../services/get-investor-by-document.service';\r\n\r\n@Controller('client/investor')\r\nexport class GetInvestorByDocumentController {\r\n  constructor(\r\n    private readonly getInvestorByDocumentService: GetInvestorByDocumentService,\r\n  ) {}\r\n\r\n  @Get(':document')\r\n  async getInvestorByDocument(\r\n    @Param() params: GetInvestorByDocumentDto,\r\n    @Headers('x-api-key') apiKey: string,\r\n  ) {\r\n    try {\r\n      if (!apiKey) {\r\n        throw new HttpException('API key é obrigatória', 401);\r\n      }\r\n\r\n      const result = await this.getInvestorByDocumentService.perform(\r\n        params,\r\n        apiKey,\r\n      );\r\n      return result;\r\n    } catch (error) {\r\n      throw new HttpException(\r\n        {\r\n          status: HttpStatus.BAD_REQUEST,\r\n        },\r\n        HttpStatus.BAD_REQUEST,\r\n        {\r\n          cause: error,\r\n        },\r\n      );\r\n    }\r\n  }\r\n}\r\n"]}