{"version": 3, "file": "1731042896398-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1731042896398-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAuBnC,CAAC;IArBQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,gVAAgV,CACjV,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,iKAAiK,CAClK,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,uKAAuK,CACxK,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,wEAAwE,CACzE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wEAAwE,CACzE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;IAClD,CAAC;CACF;AAxBD,0DAwBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1731042896398 implements MigrationInterface {\r\n  name = 'Migrations1731042896398';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"uploads\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"type\" character varying NOT NULL, \"url\" character varying NOT NULL, \"owner_id\" uuid, \"business_id\" uuid, \"updated_at\" TIMESTAMP NOT NULL DEFAULT now(), \"created_at\" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT \"PK_d1781d1eedd7459314f60f39bd3\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"uploads\" ADD CONSTRAINT \"FK_4dfa98b9e12204ea0f0f712f30c\" FOREIGN KEY (\"owner_id\") REFERENCES \"owner\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"uploads\" ADD CONSTRAINT \"FK_78d9afe9be70c9109d6824b4b58\" FOREIGN KEY (\"business_id\") REFERENCES \"business\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"uploads\" DROP CONSTRAINT \"FK_78d9afe9be70c9109d6824b4b58\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"uploads\" DROP CONSTRAINT \"FK_4dfa98b9e12204ea0f0f712f30c\"`,\r\n    );\r\n    await queryRunner.query(`DROP TABLE \"uploads\"`);\r\n  }\r\n}\r\n"]}