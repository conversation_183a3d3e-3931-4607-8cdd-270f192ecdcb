{"version": 3, "file": "1716151461690-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1716151461690-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAWnC,CAAC;IATQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,4EAA4E,CAC7E,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAC;IACvE,CAAC;CACF;AAZD,0DAYC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1716151461690 implements MigrationInterface {\r\n  name = 'Migrations1716151461690';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"document\" ADD \"type\" character varying NOT NULL DEFAULT 'CNH'`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(`ALTER TABLE \"document\" DROP COLUMN \"type\"`);\r\n  }\r\n}\r\n"]}