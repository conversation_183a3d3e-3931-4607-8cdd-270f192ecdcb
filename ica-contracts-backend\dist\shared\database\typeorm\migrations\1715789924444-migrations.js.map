{"version": 3, "file": "1715789924444-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1715789924444-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAiBnC,CAAC;IAfQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,gWAAgW,CACjW,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,sKAAsK,CACvK,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,yEAAyE,CAC1E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;IACnD,CAAC;CACF;AAlBD,0DAkBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1715789924444 implements MigrationInterface {\r\n  name = 'Migrations1715789924444';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"document\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"account_id\" uuid, \"front\" character varying NOT NULL, \"back\" character varying NOT NULL, \"card_cnpj\" character varying, \"updated_at\" TIMESTAMP NOT NULL DEFAULT now(), \"created_at\" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT \"PK_e57d3357f83f3cdc0acffc3d777\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"document\" ADD CONSTRAINT \"FK_a05e62b6b25192e8ff52fd197ed\" FOREIGN KEY (\"account_id\") REFERENCES \"account\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"document\" DROP CONSTRAINT \"FK_a05e62b6b25192e8ff52fd197ed\"`,\r\n    );\r\n    await queryRunner.query(`DROP TABLE \"document\"`);\r\n  }\r\n}\r\n"]}