{"version": 3, "file": "pix-cashout-celcoin-webhook.dto.js", "sourceRoot": "/", "sources": ["modules/webhook/dto/pix-cashout-celcoin-webhook.dto.ts"], "names": [], "mappings": "", "sourcesContent": ["export interface IPixCashoutCelcoinWebhook {\r\n  body: {\r\n    amount: number;\r\n    clientCode: string;\r\n    creditParty: {\r\n      account: string;\r\n      accountType: string;\r\n      bank: string;\r\n      branch: string;\r\n      key: string | null;\r\n      name: string;\r\n      taxId: string;\r\n    };\r\n    currentBalance: number;\r\n    debitParty: {\r\n      account: string;\r\n      accountType: string;\r\n      branch: string;\r\n      name: string;\r\n      taxId: string;\r\n    };\r\n    endToEndId: string;\r\n    id: string;\r\n    initiationType: string;\r\n    oldBalance: number;\r\n    paymentType: string;\r\n    remittanceInformation: string;\r\n    transactionIdentification: string | null;\r\n    transactionType: string;\r\n    urgency: string;\r\n  };\r\n  createTimeStamp: string;\r\n  entity: string;\r\n  error: {\r\n    errorCode: string;\r\n    message: string;\r\n  };\r\n  level: string;\r\n  message: string;\r\n  status: string;\r\n  timestamp: string;\r\n  webhookId: string;\r\n}\r\n"]}