"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1734580069004 = void 0;
class Migrations1734580069004 {
    constructor() {
        this.name = 'Migrations1734580069004';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "owner_role_relation" ADD "part_percent" numeric(5,4)`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "owner_role_relation" DROP COLUMN "part_percent"`);
    }
}
exports.Migrations1734580069004 = Migrations1734580069004;
//# sourceMappingURL=1734580069004-migrations.js.map