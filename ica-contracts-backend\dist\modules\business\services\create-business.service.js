"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateBusinessService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const bcrypt_1 = require("bcrypt");
const business_entity_1 = require("../../../shared/database/typeorm/entities/business.entity");
const owner_entity_1 = require("../../../shared/database/typeorm/entities/owner.entity");
const typeorm_2 = require("typeorm");
let CreateBusinessService = class CreateBusinessService {
    constructor(ownerRepository, businessRepository) {
        this.ownerRepository = ownerRepository;
        this.businessRepository = businessRepository;
    }
    async perform(input) {
        const ownerAlreadyExists = await this.businessRepository.exists({
            where: { cnpj: input.cnpj },
        });
        if (ownerAlreadyExists)
            throw new common_1.BadRequestException('Empresa já cadastrada.');
        const password = this.generatePassword();
        const owner = this.ownerRepository.save({
            name: input.name,
            cpf: input.cpf,
            email: input.email,
            password,
            ownerBusinessRelation: [
                {
                    business: {
                        cnpj: input.cnpj,
                        companyName: input.companyName,
                        email: input.email,
                    },
                },
            ],
        });
        return owner;
    }
    generatePassword() {
        const password = Math.random().toString(36).slice(-10);
        const encryptPassword = (0, bcrypt_1.hashSync)(password, 1);
        return encryptPassword;
    }
};
exports.CreateBusinessService = CreateBusinessService;
exports.CreateBusinessService = CreateBusinessService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(owner_entity_1.OwnerEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(business_entity_1.BusinessEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], CreateBusinessService);
//# sourceMappingURL=create-business.service.js.map