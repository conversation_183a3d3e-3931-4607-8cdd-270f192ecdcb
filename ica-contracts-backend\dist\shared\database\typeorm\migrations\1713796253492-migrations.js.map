{"version": 3, "file": "1713796253492-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1713796253492-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IA+BnC,CAAC;IA7BQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC;QAC1E,MAAM,WAAW,CAAC,KAAK,CAAC,yCAAyC,CAAC,CAAC;QACnE,MAAM,WAAW,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACzE,MAAM,WAAW,CAAC,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACvE,MAAM,WAAW,CAAC,KAAK,CACrB,mDAAmD,CACpD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;QACtE,MAAM,WAAW,CAAC,KAAK,CAAC,kDAAkD,CAAC,CAAC;QAC5E,MAAM,WAAW,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC;IAC5E,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,mDAAmD,CACpD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,qDAAqD,CACtD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;QACtE,MAAM,WAAW,CAAC,KAAK,CACrB,mDAAmD,CACpD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC;QAC1E,MAAM,WAAW,CAAC,KAAK,CAAC,kDAAkD,CAAC,CAAC;QAC5E,MAAM,WAAW,CAAC,KAAK,CAAC,yCAAyC,CAAC,CAAC;QACnE,MAAM,WAAW,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC;IAC5E,CAAC;CACF;AAhCD,0DAgCC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1713796253492 implements MigrationInterface {\r\n  name = 'Migrations1713796253492';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(`ALTER TABLE \"owner\" ADD \"total_invest\" numeric`);\r\n    await queryRunner.query(`ALTER TABLE \"owner\" ADD \"yield\" numeric`);\r\n    await queryRunner.query(`ALTER TABLE \"owner\" ADD \"start_contract\" date`);\r\n    await queryRunner.query(`ALTER TABLE \"owner\" ADD \"end_contract\" date`);\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"business\" ADD \"total_invest\" numeric`,\r\n    );\r\n    await queryRunner.query(`ALTER TABLE \"business\" ADD \"yield\" numeric`);\r\n    await queryRunner.query(`ALTER TABLE \"business\" ADD \"start_contract\" date`);\r\n    await queryRunner.query(`ALTER TABLE \"business\" ADD \"end_contract\" date`);\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"business\" DROP COLUMN \"end_contract\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"business\" DROP COLUMN \"start_contract\"`,\r\n    );\r\n    await queryRunner.query(`ALTER TABLE \"business\" DROP COLUMN \"yield\"`);\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"business\" DROP COLUMN \"total_invest\"`,\r\n    );\r\n    await queryRunner.query(`ALTER TABLE \"owner\" DROP COLUMN \"end_contract\"`);\r\n    await queryRunner.query(`ALTER TABLE \"owner\" DROP COLUMN \"start_contract\"`);\r\n    await queryRunner.query(`ALTER TABLE \"owner\" DROP COLUMN \"yield\"`);\r\n    await queryRunner.query(`ALTER TABLE \"owner\" DROP COLUMN \"total_invest\"`);\r\n  }\r\n}\r\n"]}