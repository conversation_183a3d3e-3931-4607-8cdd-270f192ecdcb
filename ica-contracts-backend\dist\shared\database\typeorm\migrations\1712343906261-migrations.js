"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations************* = void 0;
class Migrations************* {
    constructor() {
        this.name = 'Migrations*************';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "transaction" ADD "destiny_account_type" character varying`);
        await queryRunner.query(`ALTER TABLE "transaction" ADD "transfer_date" TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "transaction" ADD "destiny_key" character varying`);
        await queryRunner.query(`ALTER TABLE "transaction" ADD "status" character varying NOT NULL DEFAULT 'PENDENT'`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "transaction" DROP COLUMN "status"`);
        await queryRunner.query(`ALTER TABLE "transaction" DROP COLUMN "destiny_key"`);
        await queryRunner.query(`ALTER TABLE "transaction" DROP COLUMN "transfer_date"`);
        await queryRunner.query(`ALTER TABLE "transaction" DROP COLUMN "destiny_account_type"`);
    }
}
exports.Migrations************* = Migrations*************;
//# sourceMappingURL=*************-migrations.js.map