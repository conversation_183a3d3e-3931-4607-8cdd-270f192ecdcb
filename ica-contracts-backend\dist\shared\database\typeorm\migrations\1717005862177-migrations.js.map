{"version": 3, "file": "1717005862177-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1717005862177-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAmBnC,CAAC;IAjBQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,4FAA4F,CAC7F,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,8EAA8E,CAC/E,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,oEAAoE,CACrE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,4EAA4E,CAC7E,CAAC;IACJ,CAAC;CACF;AApBD,0DAoBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1717005862177 implements MigrationInterface {\r\n  name = 'Migrations1717005862177';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" ADD \"general_transfer_limit\" numeric DEFAULT '50000'`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" ADD \"default_values\" character varying`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" DROP COLUMN \"default_values\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" DROP COLUMN \"general_transfer_limit\"`,\r\n    );\r\n  }\r\n}\r\n"]}