import { GetInvestorByDocumentDto } from '../dto/get-investor-by-document.dto';
import { GetInvestorByDocumentService } from '../services/get-investor-by-document.service';
export declare class GetInvestorByDocumentController {
    private readonly getInvestorByDocumentService;
    constructor(getInvestorByDocumentService: GetInvestorByDocumentService);
    getInvestorByDocument(params: GetInvestorByDocumentDto, apiKey: string): Promise<Omit<import("../responses/client-login.response").IClientLoginResponse, "accessToken" | "refreshToken">>;
}
