"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1726694748261 = void 0;
class Migrations1726694748261 {
    constructor() {
        this.name = 'Migrations1726694748261';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "movement" DROP CONSTRAINT "PK_079f005d01ebda984e75c2d67ee"`);
        await queryRunner.query(`ALTER TABLE "movement" DROP COLUMN "id"`);
        await queryRunner.query(`ALTER TABLE "movement" ADD "id" uuid NOT NULL DEFAULT uuid_generate_v4()`);
        await queryRunner.query(`ALTER TABLE "movement" ADD CONSTRAINT "PK_079f005d01ebda984e75c2d67ee" PRIMARY KEY ("id")`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "movement" DROP CONSTRAINT "PK_079f005d01ebda984e75c2d67ee"`);
        await queryRunner.query(`ALTER TABLE "movement" DROP COLUMN "id"`);
        await queryRunner.query(`ALTER TABLE "movement" ADD "id" SERIAL NOT NULL`);
        await queryRunner.query(`ALTER TABLE "movement" ADD CONSTRAINT "PK_079f005d01ebda984e75c2d67ee" PRIMARY KEY ("id")`);
    }
}
exports.Migrations1726694748261 = Migrations1726694748261;
//# sourceMappingURL=1726694748261-migrations.js.map