{"version": 3, "file": "1715347414729-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1715347414729-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAqBnC,CAAC;IAnBQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,+TAA+T,CAChU,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,0VAA0V,CAC3V,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,8JAA8J,CAC/J,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,qEAAqE,CACtE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;QAC9C,MAAM,WAAW,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;IAC/C,CAAC;CACF;AAtBD,0DAsBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1715347414729 implements MigrationInterface {\r\n  name = 'Migrations1715347414729';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"city\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"name\" character varying NOT NULL, \"isActive\" boolean NOT NULL DEFAULT true, \"createdAt\" TIMESTAMP NOT NULL DEFAULT now(), \"updatedAt\" TIMESTAMP NOT NULL DEFAULT now(), \"state_id\" uuid, CONSTRAINT \"PK_b222f51ce26f7e5ca86944a6739\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"state\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"name\" character varying NOT NULL, \"abbreviation\" character varying NOT NULL, \"isActive\" boolean NOT NULL DEFAULT true, \"createdAt\" TIMESTAMP NOT NULL DEFAULT now(), \"updatedAt\" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT \"PK_549ffd046ebab1336c3a8030a12\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"city\" ADD CONSTRAINT \"FK_37ecd8addf395545dcb0242a593\" FOREIGN KEY (\"state_id\") REFERENCES \"state\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"city\" DROP CONSTRAINT \"FK_37ecd8addf395545dcb0242a593\"`,\r\n    );\r\n    await queryRunner.query(`DROP TABLE \"state\"`);\r\n    await queryRunner.query(`DROP TABLE \"city\"`);\r\n  }\r\n}\r\n"]}