import { ContractAuditEntity } from 'src/shared/database/typeorm/entities/contract-audit.entity';
import { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';
import { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';
import { Repository } from 'typeorm';
export declare class GetContractAuditHistoryService {
    private contractAuditRepository;
    private contractRepository;
    private ownerRoleRelationRepository;
    constructor(contractAuditRepository: Repository<ContractAuditEntity>, contractRepository: Repository<ContractEntity>, ownerRoleRelationRepository: Repository<OwnerRoleRelationEntity>);
    execute(contractId: string, userId: string): Promise<{
        id: string;
        decision: import("src/shared/database/typeorm/entities/contract-audit.entity").AuditDecisionEnum;
        comments: string;
        rejectionReasons: Record<string, string>;
        auditor: {
            id: string;
            name: string;
        };
        createdAt: Date;
    }[]>;
}
