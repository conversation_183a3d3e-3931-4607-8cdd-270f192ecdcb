"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1735609131821 = void 0;
class Migrations1735609131821 {
    constructor() {
        this.name = 'Migrations1735609131821';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "report" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "type" character varying NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "owner_role_relation" uuid, "file_id" uuid, CONSTRAINT "PK_99e4d0bea58cba73c57f935a546" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "report" ADD CONSTRAINT "FK_266246ede9f575c5ef3c1c079ec" FOREIGN KEY ("owner_role_relation") REFERENCES "owner_role_relation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "report" ADD CONSTRAINT "FK_1ee07d7eee68a3ea0a9a73e1454" FOREIGN KEY ("file_id") REFERENCES "files"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "report" DROP CONSTRAINT "FK_1ee07d7eee68a3ea0a9a73e1454"`);
        await queryRunner.query(`ALTER TABLE "report" DROP CONSTRAINT "FK_266246ede9f575c5ef3c1c079ec"`);
        await queryRunner.query(`DROP TABLE "report"`);
    }
}
exports.Migrations1735609131821 = Migrations1735609131821;
//# sourceMappingURL=1735609131821-migrations.js.map