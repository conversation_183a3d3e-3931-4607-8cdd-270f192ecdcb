"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1729878522981 = void 0;
class Migrations1729878522981 {
    constructor() {
        this.name = 'Migrations1729878522981';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "contract" ADD "sign_investor" character varying`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "sign_ica" character varying`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "sign_ica"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "sign_investor"`);
    }
}
exports.Migrations1729878522981 = Migrations1729878522981;
//# sourceMappingURL=1729878522981-migrations.js.map