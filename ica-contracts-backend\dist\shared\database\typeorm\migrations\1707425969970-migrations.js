"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1707425969970 = void 0;
class Migrations1707425969970 {
    constructor() {
        this.name = 'Migrations1707425969970';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "advisor" ADD "business_id" uuid`);
        await queryRunner.query(`ALTER TABLE "broker" ADD "business_id" uuid`);
        await queryRunner.query(`ALTER TABLE "superadmin" ADD "business_id" uuid`);
        await queryRunner.query(`ALTER TABLE "admin" ADD "business_id" uuid`);
        await queryRunner.query(`ALTER TABLE "permission" ADD "role_id" uuid`);
        await queryRunner.query(`ALTER TABLE "advisor" ADD CONSTRAINT "FK_1b87021e9e988eb57b23557a779" FOREIGN KEY ("business_id") REFERENCES "business"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "broker" ADD CONSTRAINT "FK_4db78c5e0905ede9a579ed060bd" FOREIGN KEY ("business_id") REFERENCES "business"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "superadmin" ADD CONSTRAINT "FK_de7c99eb422a8e5e506ed99460e" FOREIGN KEY ("business_id") REFERENCES "business"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "admin" ADD CONSTRAINT "FK_08c71558c0fb298ca007dc58367" FOREIGN KEY ("business_id") REFERENCES "business"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "permission" ADD CONSTRAINT "FK_383892d758d08d346f837d3d8b7" FOREIGN KEY ("role_id") REFERENCES "role"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "permission" DROP CONSTRAINT "FK_383892d758d08d346f837d3d8b7"`);
        await queryRunner.query(`ALTER TABLE "admin" DROP CONSTRAINT "FK_08c71558c0fb298ca007dc58367"`);
        await queryRunner.query(`ALTER TABLE "superadmin" DROP CONSTRAINT "FK_de7c99eb422a8e5e506ed99460e"`);
        await queryRunner.query(`ALTER TABLE "broker" DROP CONSTRAINT "FK_4db78c5e0905ede9a579ed060bd"`);
        await queryRunner.query(`ALTER TABLE "advisor" DROP CONSTRAINT "FK_1b87021e9e988eb57b23557a779"`);
        await queryRunner.query(`ALTER TABLE "permission" DROP COLUMN "role_id"`);
        await queryRunner.query(`ALTER TABLE "admin" DROP COLUMN "business_id"`);
        await queryRunner.query(`ALTER TABLE "superadmin" DROP COLUMN "business_id"`);
        await queryRunner.query(`ALTER TABLE "broker" DROP COLUMN "business_id"`);
        await queryRunner.query(`ALTER TABLE "advisor" DROP COLUMN "business_id"`);
    }
}
exports.Migrations1707425969970 = Migrations1707425969970;
//# sourceMappingURL=1707425969970-migrations.js.map