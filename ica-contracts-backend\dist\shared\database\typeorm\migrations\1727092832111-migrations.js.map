{"version": 3, "file": "1727092832111-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1727092832111-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAenC,CAAC;IAbQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACzE,MAAM,WAAW,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACzE,MAAM,WAAW,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACzE,MAAM,WAAW,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;IAC3E,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACzE,MAAM,WAAW,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACzE,MAAM,WAAW,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACzE,MAAM,WAAW,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;IAC3E,CAAC;CACF;AAhBD,0DAgBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1727092832111 implements MigrationInterface {\r\n  name = 'Migrations1727092832111';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(`ALTER TABLE \"income\" DROP COLUMN \"income_day\"`);\r\n    await queryRunner.query(`ALTER TABLE \"income\" ADD \"income_day\" numeric`);\r\n    await queryRunner.query(`ALTER TABLE \"income\" DROP COLUMN \"percentage\"`);\r\n    await queryRunner.query(`ALTER TABLE \"income\" ADD \"percentage\" numeric`);\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(`ALTER TABLE \"income\" DROP COLUMN \"percentage\"`);\r\n    await queryRunner.query(`ALTER TABLE \"income\" ADD \"percentage\" integer`);\r\n    await queryRunner.query(`ALTER TABLE \"income\" DROP COLUMN \"income_day\"`);\r\n    await queryRunner.query(`ALTER TABLE \"income\" ADD \"income_day\" integer`);\r\n  }\r\n}\r\n"]}