"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1727356480959 = void 0;
class Migrations1727356480959 {
    constructor() {
        this.name = 'Migrations1727356480959';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "contract" ALTER COLUMN "status" SET DEFAULT 'aberto'`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "contract" ALTER COLUMN "status" DROP DEFAULT`);
    }
}
exports.Migrations1727356480959 = Migrations1727356480959;
//# sourceMappingURL=1727356480959-migrations.js.map