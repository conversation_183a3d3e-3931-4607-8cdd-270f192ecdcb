"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1736188204075 = void 0;
class Migrations1736188204075 {
    constructor() {
        this.name = 'Migrations1736188204075';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "owner" ADD CONSTRAINT "UQ_4ba355186545479193a3ed22ba0" UNIQUE ("cpf")`);
        await queryRunner.query(`ALTER TABLE "business" ADD CONSTRAINT "UQ_d871b59fcb850c9870d9d69c472" UNIQUE ("cnpj")`);
        await queryRunner.query(`ALTER TABLE "report" DROP CONSTRAINT "FK_266246ede9f575c5ef3c1c079ec"`);
        await queryRunner.query(`ALTER TABLE "report" DROP CONSTRAINT "FK_1ee07d7eee68a3ea0a9a73e1454"`);
        await queryRunner.query(`ALTER TABLE "report" ALTER COLUMN "owner_role_relation" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "report" ALTER COLUMN "file_id" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "report" ADD CONSTRAINT "FK_266246ede9f575c5ef3c1c079ec" FOREIGN KEY ("owner_role_relation") REFERENCES "owner_role_relation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "report" ADD CONSTRAINT "FK_1ee07d7eee68a3ea0a9a73e1454" FOREIGN KEY ("file_id") REFERENCES "files"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "report" DROP CONSTRAINT "FK_1ee07d7eee68a3ea0a9a73e1454"`);
        await queryRunner.query(`ALTER TABLE "report" DROP CONSTRAINT "FK_266246ede9f575c5ef3c1c079ec"`);
        await queryRunner.query(`ALTER TABLE "report" ALTER COLUMN "file_id" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "report" ALTER COLUMN "owner_role_relation" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "report" ADD CONSTRAINT "FK_1ee07d7eee68a3ea0a9a73e1454" FOREIGN KEY ("file_id") REFERENCES "files"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "report" ADD CONSTRAINT "FK_266246ede9f575c5ef3c1c079ec" FOREIGN KEY ("owner_role_relation") REFERENCES "owner_role_relation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "business" DROP CONSTRAINT "UQ_d871b59fcb850c9870d9d69c472"`);
        await queryRunner.query(`ALTER TABLE "owner" DROP CONSTRAINT "UQ_4ba355186545479193a3ed22ba0"`);
    }
}
exports.Migrations1736188204075 = Migrations1736188204075;
//# sourceMappingURL=1736188204075-migrations.js.map