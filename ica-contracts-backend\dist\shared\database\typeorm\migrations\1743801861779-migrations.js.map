{"version": 3, "file": "1743801861779-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1743801861779-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACI,SAAI,GAAG,yBAAyB,CAAA;IAoBpC,CAAC;IAlBU,KAAK,CAAC,EAAE,CAAC,WAAwB;QACpC,MAAM,WAAW,CAAC,KAAK,CAAC,2EAA2E,CAAC,CAAC;QACrG,MAAM,WAAW,CAAC,KAAK,CAAC,4EAA4E,CAAC,CAAC;QACtG,MAAM,WAAW,CAAC,KAAK,CAAC,uFAAuF,CAAC,CAAC;QACjH,MAAM,WAAW,CAAC,KAAK,CAAC,kFAAkF,CAAC,CAAC;QAC5G,MAAM,WAAW,CAAC,KAAK,CAAC,0EAA0E,CAAC,CAAC;QACpG,MAAM,WAAW,CAAC,KAAK,CAAC,2EAA2E,CAAC,CAAC;IACzG,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CAAC,sDAAsD,CAAC,CAAC;QAChF,MAAM,WAAW,CAAC,KAAK,CAAC,mDAAmD,CAAC,CAAC;QAC7E,MAAM,WAAW,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAC;QAC3E,MAAM,WAAW,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACzE,MAAM,WAAW,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAC;QACxE,MAAM,WAAW,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;IACpE,CAAC;CAEJ;AArBD,0DAqBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from \"typeorm\";\r\n\r\nexport class Migrations1743801861779 implements MigrationInterface {\r\n    name = 'Migrations1743801861779'\r\n\r\n    public async up(queryRunner: QueryRunner): Promise<void> {\r\n        await queryRunner.query(`ALTER TABLE \"owner\" ADD \"rg\" character varying NOT NULL DEFAULT '0000000'`);\r\n        await queryRunner.query(`ALTER TABLE \"owner\" ADD \"occupation\" character varying NOT NULL DEFAULT ''`);\r\n        await queryRunner.query(`ALTER TABLE \"owner\" ADD \"nationality\" character varying NOT NULL DEFAULT 'brasileira'`);\r\n        await queryRunner.query(`ALTER TABLE \"owner\" ADD \"issuingAgency\" character varying NOT NULL DEFAULT 'SSP'`);\r\n        await queryRunner.query(`ALTER TABLE \"contract\" ADD \"is_debenture\" boolean NOT NULL DEFAULT false`);\r\n        await queryRunner.query(`ALTER TABLE \"contract\" ADD \"contract_number\" integer NOT NULL DEFAULT '1'`);\r\n    }\r\n\r\n    public async down(queryRunner: QueryRunner): Promise<void> {\r\n        await queryRunner.query(`ALTER TABLE \"contract\" DROP COLUMN \"contract_number\"`);\r\n        await queryRunner.query(`ALTER TABLE \"contract\" DROP COLUMN \"is_debenture\"`);\r\n        await queryRunner.query(`ALTER TABLE \"owner\" DROP COLUMN \"issuingAgency\"`);\r\n        await queryRunner.query(`ALTER TABLE \"owner\" DROP COLUMN \"nationality\"`);\r\n        await queryRunner.query(`ALTER TABLE \"owner\" DROP COLUMN \"occupation\"`);\r\n        await queryRunner.query(`ALTER TABLE \"owner\" DROP COLUMN \"rg\"`);\r\n    }\r\n\r\n}\r\n"]}