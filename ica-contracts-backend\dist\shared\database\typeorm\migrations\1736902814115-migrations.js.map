{"version": 3, "file": "1736902814115-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1736902814115-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAuBnC,CAAC;IArBQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,ibAAib,CAClb,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,8KAA8K,CAC/K,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wLAAwL,CACzL,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,iFAAiF,CAClF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,iFAAiF,CAClF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;IAC3D,CAAC;CACF;AAxBD,0DAwBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1736902814115 implements MigrationInterface {\r\n  name = 'Migrations1736902814115';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"contract_advisor\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"rate\" numeric(5,2) NOT NULL, \"is_active\" boolean NOT NULL DEFAULT true, \"created_at\" TIMESTAMP NOT NULL DEFAULT now(), \"updated_at\" TIMESTAMP NOT NULL DEFAULT now(), \"id_contract\" uuid, \"id_advisor\" uuid, CONSTRAINT \"UQ_57ddfe73a0f966bba89f031cc9d\" UNIQUE (\"id_contract\", \"id_advisor\"), CONSTRAINT \"PK_76d4fa3107ee65be1406e768a30\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract_advisor\" ADD CONSTRAINT \"FK_b3eb1d7d01d429b1fc2ae37c05b\" FOREIGN KEY (\"id_contract\") REFERENCES \"contract\"(\"id\") ON DELETE CASCADE ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract_advisor\" ADD CONSTRAINT \"FK_cba07ac9ce330bc89efb1e82786\" FOREIGN KEY (\"id_advisor\") REFERENCES \"owner_role_relation\"(\"id\") ON DELETE CASCADE ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract_advisor\" DROP CONSTRAINT \"FK_cba07ac9ce330bc89efb1e82786\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract_advisor\" DROP CONSTRAINT \"FK_b3eb1d7d01d429b1fc2ae37c05b\"`,\r\n    );\r\n    await queryRunner.query(`DROP TABLE \"contract_advisor\"`);\r\n  }\r\n}\r\n"]}