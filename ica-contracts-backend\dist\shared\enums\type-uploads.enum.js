"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TypeUploadsEnum = void 0;
var TypeUploadsEnum;
(function (TypeUploadsEnum) {
    TypeUploadsEnum["RG"] = "RG";
    TypeUploadsEnum["CNH"] = "CNH";
    TypeUploadsEnum["RNE"] = "RNE";
    TypeUploadsEnum["CARD_CNPJ"] = "CARD_CNPJ";
    TypeUploadsEnum["MEI"] = "MEI";
    TypeUploadsEnum["SOCIAL_CONTRACT"] = "SOCIAL_CONTRACT";
    TypeUploadsEnum["PARTNERSHIP_CONTRACT"] = "CONTRACT";
    TypeUploadsEnum["PROOF_RESIDENCE"] = "PROOF_RESIDENCE";
})(TypeUploadsEnum || (exports.TypeUploadsEnum = TypeUploadsEnum = {}));
//# sourceMappingURL=type-uploads.enum.js.map