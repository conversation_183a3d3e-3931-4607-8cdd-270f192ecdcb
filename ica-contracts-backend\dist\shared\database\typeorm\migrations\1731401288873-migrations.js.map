{"version": 3, "file": "1731401288873-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1731401288873-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAiCnC,CAAC;IA/BQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,giBAAgiB,CACjiB,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,0gBAA0gB,CAC3gB,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,kMAAkM,CACnM,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,kLAAkL,CACnL,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,iMAAiM,CAClM,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,4EAA4E,CAC7E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,6EAA6E,CAC9E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,6EAA6E,CAC9E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;QACpD,MAAM,WAAW,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;IACvD,CAAC;CACF;AAlCD,0DAkCC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1731401288873 implements MigrationInterface {\r\n  name = 'Migrations1731401288873';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"advisor_goal\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"date_from\" TIMESTAMP NOT NULL, \"date_to\" TIMESTAMP NOT NULL, \"targetAmount\" numeric(10,2) NOT NULL, \"amountAchieved\" numeric(10,2) NOT NULL, \"status\" character varying NOT NULL, \"observations\" character varying NOT NULL, \"name\" character varying NOT NULL, \"createdAt\" TIMESTAMP NOT NULL DEFAULT now(), \"updatedAt\" TIMESTAMP NOT NULL DEFAULT now(), \"owner_role_relation_id\" uuid, \"broker_goal_id\" uuid, CONSTRAINT \"PK_7ca80f8383ddb80051f4fff0132\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"broker_goal\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"date_from\" TIMESTAMP NOT NULL, \"date_to\" TIMESTAMP NOT NULL, \"target_amount\" numeric(10,2) NOT NULL, \"amount_achieved\" numeric(10,2) NOT NULL, \"status\" character varying NOT NULL, \"observations\" character varying NOT NULL, \"name\" character varying NOT NULL, \"createdAt\" TIMESTAMP NOT NULL DEFAULT now(), \"updatedAt\" TIMESTAMP NOT NULL DEFAULT now(), \"owner_role_relation_id\" uuid, CONSTRAINT \"PK_1c40d7ac97468846b4c23834f16\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"advisor_goal\" ADD CONSTRAINT \"FK_885f599e8e2b179496db960c14d\" FOREIGN KEY (\"owner_role_relation_id\") REFERENCES \"owner_role_relation\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"advisor_goal\" ADD CONSTRAINT \"FK_1dc1e0d28fa23237aaae2361fae\" FOREIGN KEY (\"broker_goal_id\") REFERENCES \"broker_goal\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"broker_goal\" ADD CONSTRAINT \"FK_e91985024a98030c3a58c067972\" FOREIGN KEY (\"owner_role_relation_id\") REFERENCES \"owner_role_relation\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"broker_goal\" DROP CONSTRAINT \"FK_e91985024a98030c3a58c067972\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"advisor_goal\" DROP CONSTRAINT \"FK_1dc1e0d28fa23237aaae2361fae\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"advisor_goal\" DROP CONSTRAINT \"FK_885f599e8e2b179496db960c14d\"`,\r\n    );\r\n    await queryRunner.query(`DROP TABLE \"broker_goal\"`);\r\n    await queryRunner.query(`DROP TABLE \"advisor_goal\"`);\r\n  }\r\n}\r\n"]}