"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations************* = void 0;
class Migrations************* {
    constructor() {
        this.name = 'Migrations*************';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "service_fee" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "external_id" character varying, "monthly_fee" numeric NOT NULL, "fee_accumulation_period" numeric NOT NULL, "last_payment_date" date, "payment_status" character varying(50) NOT NULL, "debt_amount" numeric, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "account_id" uuid, CONSTRAINT "PK_70d082964aaa4bf193c2b1276e9" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" ADD "general_transfer_limit" numeric DEFAULT '50000'`);
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" ADD "default_values" character varying`);
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" ADD "active" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "account" ADD "is_taxable" boolean NOT NULL DEFAULT true`);
        await queryRunner.query(`ALTER TABLE "account" ADD "two_factor_key" character varying`);
        await queryRunner.query(`ALTER TABLE "account" ADD "have_2fa" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "statistic" ALTER COLUMN "total_applied" TYPE numeric`);
        await queryRunner.query(`ALTER TABLE "service_fee" ADD CONSTRAINT "FK_b1553b09af9ce6028995889dffe" FOREIGN KEY ("account_id") REFERENCES "account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "service_fee" DROP CONSTRAINT "FK_b1553b09af9ce6028995889dffe"`);
        await queryRunner.query(`ALTER TABLE "statistic" ALTER COLUMN "total_applied" TYPE numeric(10,2)`);
        await queryRunner.query(`ALTER TABLE "account" DROP COLUMN "have_2fa"`);
        await queryRunner.query(`ALTER TABLE "account" DROP COLUMN "two_factor_key"`);
        await queryRunner.query(`ALTER TABLE "account" DROP COLUMN "is_taxable"`);
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" DROP COLUMN "active"`);
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" DROP COLUMN "default_values"`);
        await queryRunner.query(`ALTER TABLE "account_transfer-limits" DROP COLUMN "general_transfer_limit"`);
        await queryRunner.query(`DROP TABLE "service_fee"`);
    }
}
exports.Migrations************* = Migrations*************;
//# sourceMappingURL=*************-migrations.js.map