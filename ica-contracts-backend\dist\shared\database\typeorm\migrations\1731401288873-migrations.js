"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1731401288873 = void 0;
class Migrations1731401288873 {
    constructor() {
        this.name = 'Migrations1731401288873';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "advisor_goal" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "date_from" TIMESTAMP NOT NULL, "date_to" TIMESTAMP NOT NULL, "targetAmount" numeric(10,2) NOT NULL, "amountAchieved" numeric(10,2) NOT NULL, "status" character varying NOT NULL, "observations" character varying NOT NULL, "name" character varying NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "owner_role_relation_id" uuid, "broker_goal_id" uuid, CONSTRAINT "PK_7ca80f8383ddb80051f4fff0132" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "broker_goal" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "date_from" TIMESTAMP NOT NULL, "date_to" TIMESTAMP NOT NULL, "target_amount" numeric(10,2) NOT NULL, "amount_achieved" numeric(10,2) NOT NULL, "status" character varying NOT NULL, "observations" character varying NOT NULL, "name" character varying NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "owner_role_relation_id" uuid, CONSTRAINT "PK_1c40d7ac97468846b4c23834f16" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "advisor_goal" ADD CONSTRAINT "FK_885f599e8e2b179496db960c14d" FOREIGN KEY ("owner_role_relation_id") REFERENCES "owner_role_relation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "advisor_goal" ADD CONSTRAINT "FK_1dc1e0d28fa23237aaae2361fae" FOREIGN KEY ("broker_goal_id") REFERENCES "broker_goal"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "broker_goal" ADD CONSTRAINT "FK_e91985024a98030c3a58c067972" FOREIGN KEY ("owner_role_relation_id") REFERENCES "owner_role_relation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "broker_goal" DROP CONSTRAINT "FK_e91985024a98030c3a58c067972"`);
        await queryRunner.query(`ALTER TABLE "advisor_goal" DROP CONSTRAINT "FK_1dc1e0d28fa23237aaae2361fae"`);
        await queryRunner.query(`ALTER TABLE "advisor_goal" DROP CONSTRAINT "FK_885f599e8e2b179496db960c14d"`);
        await queryRunner.query(`DROP TABLE "broker_goal"`);
        await queryRunner.query(`DROP TABLE "advisor_goal"`);
    }
}
exports.Migrations1731401288873 = Migrations1731401288873;
//# sourceMappingURL=1731401288873-migrations.js.map