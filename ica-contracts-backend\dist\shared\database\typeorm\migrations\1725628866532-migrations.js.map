{"version": 3, "file": "1725628866532-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1725628866532-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAmCnC,CAAC;IAjCQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,oVAAoV,CACrV,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,mDAAmD,CACpD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,4KAA4K,CAC7K,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,kKAAkK,CACnK,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wKAAwK,CACzK,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,yEAAyE,CAC1E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,yEAAyE,CAC1E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,6EAA6E,CAC9E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,sDAAsD,CACvD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;IACnD,CAAC;CACF;AApCD,0DAoCC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1725628866532 implements MigrationInterface {\r\n  name = 'Migrations1725628866532';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"contract\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"external_id\" integer NOT NULL, \"creatorType\" character varying NOT NULL, \"createdAt\" TIMESTAMP NOT NULL DEFAULT now(), \"updatedAt\" TIMESTAMP NOT NULL DEFAULT now(), \"owner_id\" uuid, \"business_id\" uuid, CONSTRAINT \"PK_17c3a89f58a2997276084e706e8\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pre-register\" ADD \"contract_id\" uuid`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pre-register\" ADD CONSTRAINT \"FK_f5dd323431219906bb41f6c1abf\" FOREIGN KEY (\"contract_id\") REFERENCES \"contract\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract\" ADD CONSTRAINT \"FK_9b5bba51f1fc7a6a6ddbfe87c93\" FOREIGN KEY (\"owner_id\") REFERENCES \"owner\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract\" ADD CONSTRAINT \"FK_f1f42c0cd4752f6420997f04f10\" FOREIGN KEY (\"business_id\") REFERENCES \"business\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract\" DROP CONSTRAINT \"FK_f1f42c0cd4752f6420997f04f10\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract\" DROP CONSTRAINT \"FK_9b5bba51f1fc7a6a6ddbfe87c93\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pre-register\" DROP CONSTRAINT \"FK_f5dd323431219906bb41f6c1abf\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pre-register\" DROP COLUMN \"contract_id\"`,\r\n    );\r\n    await queryRunner.query(`DROP TABLE \"contract\"`);\r\n  }\r\n}\r\n"]}