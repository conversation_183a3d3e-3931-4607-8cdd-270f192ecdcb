"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations************* = void 0;
class Migrations************* {
    constructor() {
        this.name = 'Migrations*************';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "transaction" DROP COLUMN "transfer_date"`);
        await queryRunner.query(`ALTER TABLE "transaction" DROP COLUMN "destiny_key"`);
        await queryRunner.query(`ALTER TABLE "transaction" DROP COLUMN "status"`);
        await queryRunner.query(`ALTER TABLE "account" ADD "profile_image" character varying`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "account" DROP COLUMN "profile_image"`);
        await queryRunner.query(`ALTER TABLE "transaction" ADD "status" character varying NOT NULL DEFAULT 'PENDENT'`);
        await queryRunner.query(`ALTER TABLE "transaction" ADD "destiny_key" character varying`);
        await queryRunner.query(`ALTER TABLE "transaction" ADD "transfer_date" TIMESTAMP`);
    }
}
exports.Migrations************* = Migrations*************;
//# sourceMappingURL=*************-migrations.js.map