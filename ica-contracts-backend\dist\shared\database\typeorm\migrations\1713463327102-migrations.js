"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1713463327102 = void 0;
class Migrations1713463327102 {
    constructor() {
        this.name = 'Migrations1713463327102';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "app_version" ADD "ios_url" character varying`);
        await queryRunner.query(`ALTER TABLE "app_version" ADD "android_url" character varying`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "app_version" DROP COLUMN "android_url"`);
        await queryRunner.query(`ALTER TABLE "app_version" DROP COLUMN "ios_url"`);
    }
}
exports.Migrations1713463327102 = Migrations1713463327102;
//# sourceMappingURL=1713463327102-migrations.js.map