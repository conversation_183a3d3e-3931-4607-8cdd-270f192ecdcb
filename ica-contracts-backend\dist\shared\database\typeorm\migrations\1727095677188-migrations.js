"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1727095677188 = void 0;
class Migrations1727095677188 {
    constructor() {
        this.name = 'Migrations1727095677188';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "income" ADD "deleted_at" TIMESTAMP`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "income" DROP COLUMN "deleted_at"`);
    }
}
exports.Migrations1727095677188 = Migrations1727095677188;
//# sourceMappingURL=1727095677188-migrations.js.map