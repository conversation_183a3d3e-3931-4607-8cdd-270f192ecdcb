import { ContractAuditEntity, AuditDecisionEnum } from 'src/shared/database/typeorm/entities/contract-audit.entity';
import { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';
import { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';
import { Repository } from 'typeorm';
import { AuditContractDto } from '../dto/audit-contract.dto';
import { RequestInvestorCredentialsService } from 'src/apis/ica-contract-service/services/request-investor-credentials.service';
export declare class AuditContractService {
    private contractAuditRepository;
    private contractRepository;
    private ownerRoleRelationRepository;
    private requestInvestorCredentialsService;
    constructor(contractAuditRepository: Repository<ContractAuditEntity>, contractRepository: Repository<ContractEntity>, ownerRoleRelationRepository: Repository<OwnerRoleRelationEntity>, requestInvestorCredentialsService: RequestInvestorCredentialsService);
    auditContract(data: AuditContractDto, auditorId: string): Promise<{
        id: string;
        contractId: string;
        decision: AuditDecisionEnum;
        comments: string;
        rejectionReasons: Record<string, string>;
        createdAt: Date;
    }>;
}
