"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations************* = void 0;
class Migrations************* {
    constructor() {
        this.name = 'Migrations*************';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "account" ADD "external_id" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "account" ALTER COLUMN "number" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "account" ALTER COLUMN "branch" DROP NOT NULL`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "account" ALTER COLUMN "branch" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "account" ALTER COLUMN "number" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "account" DROP COLUMN "external_id"`);
    }
}
exports.Migrations************* = Migrations*************;
//# sourceMappingURL=*************-migrations.js.map