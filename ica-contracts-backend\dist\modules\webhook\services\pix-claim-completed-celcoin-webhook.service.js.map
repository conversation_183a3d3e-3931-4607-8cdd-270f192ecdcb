{"version": 3, "file": "pix-claim-completed-celcoin-webhook.service.js", "sourceRoot": "/", "sources": ["modules/webhook/services/pix-claim-completed-celcoin-webhook.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6CAAmD;AACnD,6FAAmF;AACnF,+EAAqE;AACrE,mFAAwE;AACxE,mDAA2C;AAC3C,qCAAqC;AAK9B,IAAM,sCAAsC,GAA5C,MAAM,sCAAsC;IACjD,YAEmB,gBAA0C;QAA1C,qBAAgB,GAAhB,gBAAgB,CAA0B;IAC1D,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,KAAwB;QACpC,eAAM,CAAC,IAAI,CAAC,sDAAsD,EAAE,KAAK,CAAC,CAAC;QAE3E,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;YACjD,KAAK,EAAE;gBACL,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG;gBACnB,OAAO,EAAE,EAAE,MAAM,EAAE,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE;aAClE;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO;QACT,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,CAAC,aAAa;YACnC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC;YAClC,CAAC,CAAC,EAAE,CAAC;QAEP,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAChC,EAAE,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,EACjB;YACE,MAAM,EAAE,sCAAgB,CAAC,SAAS;YAClC,WAAW,EAAE,mCAAe,CAAC,SAAS;YACtC,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC;gBAC5B,GAAG,QAAQ;gBACX,gBAAgB,EAAE,KAAK,CAAC,IAAI;aAC7B,CAAC;SACH,CACF,CAAC;IACJ,CAAC;CACF,CAAA;AApCY,wFAAsC;iDAAtC,sCAAsC;IADlD,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,6BAAY,CAAC,CAAA;qCACI,oBAAU;GAHpC,sCAAsC,CAoClD", "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { PixKeyEntity } from 'src/shared/database/typeorm/entities/pix-key.entity';\r\nimport { ClaimStatusEnum } from 'src/shared/enums/claim-status.enum';\r\nimport { PixKeyStatusEnum } from 'src/shared/enums/pix-key-status.enum';\r\nimport { logger } from 'src/shared/logger';\r\nimport { Repository } from 'typeorm';\r\n\r\nimport { IPixDictClaimOpen } from '../dto/pix-claim-open-celcoin-webhook.dto';\r\n\r\n@Injectable()\r\nexport class PixClaimCompletedCelcoinWebhookService {\r\n  constructor(\r\n    @InjectRepository(PixKeyEntity)\r\n    private readonly pixKeyRepository: Repository<PixKeyEntity>,\r\n  ) {}\r\n\r\n  async perform(input: IPixDictClaimOpen) {\r\n    logger.info('PixClaimCompletedCelcoinWebhookService.perform() -> ', input);\r\n\r\n    const pixKey = await this.pixKeyRepository.findOne({\r\n      where: {\r\n        key: input.body.key,\r\n        account: { number: input.body.claimerAccount.account.toString() },\r\n      },\r\n    });\r\n\r\n    if (!pixKey) {\r\n      return;\r\n    }\r\n\r\n    const metadata = pixKey.claimMetadata\r\n      ? JSON.parse(pixKey.claimMetadata)\r\n      : {};\r\n\r\n    await this.pixKeyRepository.update(\r\n      { id: pixKey.id },\r\n      {\r\n        status: PixKeyStatusEnum.COMPLETED,\r\n        claimStatus: ClaimStatusEnum.COMPLETED,\r\n        claimMetadata: JSON.stringify({\r\n          ...metadata,\r\n          completedWebhook: input.body,\r\n        }),\r\n      },\r\n    );\r\n  }\r\n}\r\n"]}