{"version": 3, "file": "1729878522981-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1729878522981-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAiBnC,CAAC;IAfQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,8DAA8D,CAC/D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,yDAAyD,CAC1D,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACzE,MAAM,WAAW,CAAC,KAAK,CACrB,oDAAoD,CACrD,CAAC;IACJ,CAAC;CACF;AAlBD,0DAkBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1729878522981 implements MigrationInterface {\r\n  name = 'Migrations1729878522981';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract\" ADD \"sign_investor\" character varying`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract\" ADD \"sign_ica\" character varying`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(`ALTER TABLE \"contract\" DROP COLUMN \"sign_ica\"`);\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract\" DROP COLUMN \"sign_investor\"`,\r\n    );\r\n  }\r\n}\r\n"]}