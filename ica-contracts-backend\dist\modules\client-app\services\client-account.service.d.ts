import { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';
import { IncomePaymentScheduledEntity } from 'src/shared/database/typeorm/entities/income-payment-scheduled.entity';
import { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';
import { Repository } from 'typeorm';
import { IClientAccount } from '../responses/client-login.response';
export declare class ClientAccountService {
    private readonly ownerRoleRelationRepository;
    private readonly contractRepository;
    private readonly incomePaymentScheduledRepository;
    private readonly formatDate;
    constructor(ownerRoleRelationRepository: Repository<OwnerRoleRelationEntity>, contractRepository: Repository<ContractEntity>, incomePaymentScheduledRepository: Repository<IncomePaymentScheduledEntity>, formatDate: (date: Date | string) => string);
    getAccountInfo(userRequestId: string): Promise<IClientAccount>;
}
