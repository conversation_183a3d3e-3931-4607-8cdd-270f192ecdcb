"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1727131367107 = void 0;
class Migrations1727131367107 {
    constructor() {
        this.name = 'Migrations1727131367107';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "contract" ADD "contract_pdf" character varying`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "proof_payment" character varying`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "proof_payment"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "contract_pdf"`);
    }
}
exports.Migrations1727131367107 = Migrations1727131367107;
//# sourceMappingURL=1727131367107-migrations.js.map