"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1717521672731 = void 0;
class Migrations1717521672731 {
    constructor() {
        this.name = 'Migrations1717521672731';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "document" ADD "social_contract" character varying`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "document" DROP COLUMN "social_contract"`);
    }
}
exports.Migrations1717521672731 = Migrations1717521672731;
//# sourceMappingURL=1717521672731-migrations.js.map