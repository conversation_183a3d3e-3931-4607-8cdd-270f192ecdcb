{"version": 3, "file": "1712801507068-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1712801507068-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAiBnC,CAAC;IAfQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,shBAAshB,CACvhB,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,0KAA0K,CAC3K,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,6EAA6E,CAC9E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;IACvD,CAAC;CACF;AAlBD,0DAkBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1712801507068 implements MigrationInterface {\r\n  name = 'Migrations1712801507068';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"favorite_ted\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"document\" character varying, \"account_number\" character varying, \"account_branch\" character varying, \"account_bank\" character varying, \"name\" character varying, \"alias\" character varying, \"account_id\" uuid, \"updated_at\" TIMESTAMP NOT NULL DEFAULT now(), \"created_at\" TIMESTAMP NOT NULL DEFAULT now(), \"deleted_at\" TIMESTAMP, CONSTRAINT \"REL_a9320fa074e03dc370b5cd2d77\" UNIQUE (\"account_id\"), CONSTRAINT \"PK_61a15d3413bde23004a5644e57c\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"favorite_ted\" ADD CONSTRAINT \"FK_a9320fa074e03dc370b5cd2d773\" FOREIGN KEY (\"account_id\") REFERENCES \"account\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"favorite_ted\" DROP CONSTRAINT \"FK_a9320fa074e03dc370b5cd2d773\"`,\r\n    );\r\n    await queryRunner.query(`DROP TABLE \"favorite_ted\"`);\r\n  }\r\n}\r\n"]}