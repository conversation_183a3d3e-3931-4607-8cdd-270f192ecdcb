"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1725645708717 = void 0;
class Migrations1725645708717 {
    constructor() {
        this.name = 'Migrations1725645708717';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "FK_f1f42c0cd4752f6420997f04f10"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "FK_9b5bba51f1fc7a6a6ddbfe87c93"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "owner_id"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "business_id"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "creatorType"`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "owner_role_relation" uuid`);
        await queryRunner.query(`ALTER TABLE "contract" ADD CONSTRAINT "FK_f405dde729bbe574001e86ff68a" FOREIGN KEY ("owner_role_relation") REFERENCES "owner_role_relation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "FK_f405dde729bbe574001e86ff68a"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "owner_role_relation"`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "creatorType" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "business_id" uuid`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "owner_id" uuid`);
        await queryRunner.query(`ALTER TABLE "contract" ADD CONSTRAINT "FK_9b5bba51f1fc7a6a6ddbfe87c93" FOREIGN KEY ("owner_id") REFERENCES "owner"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract" ADD CONSTRAINT "FK_f1f42c0cd4752f6420997f04f10" FOREIGN KEY ("business_id") REFERENCES "business"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
}
exports.Migrations1725645708717 = Migrations1725645708717;
//# sourceMappingURL=1725645708717-migrations.js.map