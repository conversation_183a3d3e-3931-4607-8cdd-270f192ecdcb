import { ClientPaymentsQueryDto } from '../dto/client-payment.dto';
import { IClientPaymentsListResponse, IClientPaymentDetailResponse } from '../responses/client-payment.response';
import { ClientPaymentService } from '../services/client-payment.service';
export declare class ClientPaymentController {
    private readonly clientPaymentService;
    constructor(clientPaymentService: ClientPaymentService);
    getPayments(req: {
        user: {
            id: string;
        };
    }, query: ClientPaymentsQueryDto): Promise<IClientPaymentsListResponse>;
    getPaymentById(paymentId: string): Promise<IClientPaymentDetailResponse>;
}
