"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const swagger_1 = require("@nestjs/swagger");
const basicAuth = require("express-basic-auth");
const app_module_1 = require("./app.module");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule, {
        logger: ['error', 'warn', 'log', 'debug', 'verbose'],
    });
    if (process.env.NODE_ENV !== 'development')
        app.use(['/docs', '/docs-json'], basicAuth({
            challenge: true,
            users: { [process.env.SWAGGER_USER]: process.env.SWAGGER_PASSWORD },
        }));
    app.enableCors({
        allowedHeaders: '*',
        methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
        origin: '*',
    });
    const config = new swagger_1.DocumentBuilder()
        .setTitle('ICA Contracts Backend')
        .setDescription('O INVEST Contracts Back-end é uma API modular e escalável construída com NESTJS para gerenciar contratos de investimento.')
        .setVersion('1.0')
        .addTag('ica-contracts')
        .addBearerAuth({
        type: 'apiKey',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'Authorization',
        description: 'Adicionar o conteúdo de Bearer + token',
        in: 'header',
    }, 'Bearer')
        .build();
    const documentFactory = () => swagger_1.SwaggerModule.createDocument(app, config);
    swagger_1.SwaggerModule.setup('docs', app, documentFactory, {
        jsonDocumentUrl: 'swagger/json',
        swaggerOptions: {
            persistAuthorization: true,
            defaultModelsExpandDepth: -1,
        },
    });
    app.enableCors({
        allowedHeaders: '*',
        methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
        origin: '*',
    });
    app.useGlobalPipes(new common_1.ValidationPipe({
        transform: true,
        validationError: {
            target: false,
            value: false,
        },
        exceptionFactory: (validationErrors = []) => {
            const extractMessages = (errors, parentPath = '') => {
                return errors.flatMap((error) => {
                    const fieldPath = parentPath
                        ? `${parentPath}.${error.property}`
                        : error.property;
                    const messages = Object.values(error.constraints || {}).map((msg) => {
                        return msg.replace(new RegExp(`^${fieldPath}\\.`), '');
                    });
                    const childrenMessages = error.children?.length
                        ? extractMessages(error.children, fieldPath)
                        : [];
                    return [...messages, ...childrenMessages];
                });
            };
            const messages = extractMessages(validationErrors);
            return new common_1.BadRequestException(messages);
        },
    }));
    await app.listen(process.env.PORT || 3000);
    console.log(`Server running on port ${process.env.PORT || 3000}`);
}
bootstrap();
//# sourceMappingURL=main.js.map