"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1727355381308 = void 0;
class Migrations1727355381308 {
    constructor() {
        this.name = 'Migrations1727355381308';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "contract" ADD "status" character varying`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "status"`);
    }
}
exports.Migrations1727355381308 = Migrations1727355381308;
//# sourceMappingURL=1727355381308-migrations.js.map