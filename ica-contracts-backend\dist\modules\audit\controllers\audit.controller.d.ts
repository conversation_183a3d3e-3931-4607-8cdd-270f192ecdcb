import { IRequestUser } from 'src/shared/interfaces/request-user.interface';
import { AuditContractDto } from '../dto/audit-contract.dto';
import { AuditContractService } from '../services/audit-contract.service';
import { GetContractAuditHistoryService } from '../services/get-contract-audit-history.service';
export declare class AuditController {
    private readonly auditContractService;
    private readonly getContractAuditHistoryService;
    constructor(auditContractService: AuditContractService, getContractAuditHistoryService: GetContractAuditHistoryService);
    auditContract(auditData: AuditContractDto, request: IRequestUser): Promise<{
        id: string;
        contractId: string;
        decision: import("../../../shared/database/typeorm/entities/contract-audit.entity").AuditDecisionEnum;
        comments: string;
        rejectionReasons: Record<string, string>;
        createdAt: Date;
    }>;
    getContractAudits(contractId: string, request: IRequestUser): Promise<{
        id: string;
        decision: import("../../../shared/database/typeorm/entities/contract-audit.entity").AuditDecisionEnum;
        comments: string;
        rejectionReasons: Record<string, string>;
        auditor: {
            id: string;
            name: string;
        };
        createdAt: Date;
    }[]>;
}
