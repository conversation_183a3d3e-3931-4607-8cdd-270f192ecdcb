import { BusinessEntity } from 'src/shared/database/typeorm/entities/business.entity';
import { OwnerEntity } from 'src/shared/database/typeorm/entities/owner.entity';
import { Repository } from 'typeorm';
import { GetInvestorByDocumentDto } from '../dto/get-investor-by-document.dto';
import { IClientLoginResponse } from '../responses/client-login.response';
import { ClientAccountService } from './client-account.service';
export declare class GetInvestorByDocumentService {
    private ownerDb;
    private businessDb;
    private clientAccountService;
    constructor(ownerDb: Repository<OwnerEntity>, businessDb: Repository<BusinessEntity>, clientAccountService: ClientAccountService);
    perform(data: GetInvestorByDocumentDto, apiKey: string): Promise<Omit<IClientLoginResponse, 'accessToken' | 'refreshToken'>>;
    private cpfOrCnpj;
}
