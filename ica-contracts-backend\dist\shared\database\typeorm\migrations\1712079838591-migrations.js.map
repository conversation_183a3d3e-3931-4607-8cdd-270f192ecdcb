{"version": 3, "file": "1712079838591-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1712079838591-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAanC,CAAC;IAXQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,6DAA6D,CAC9D,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,mDAAmD,CACpD,CAAC;IACJ,CAAC;CACF;AAdD,0DAcC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1712079838591 implements MigrationInterface {\r\n  name = 'Migrations1712079838591';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"billet\" ADD \"transaction_id\" character varying`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"billet\" DROP COLUMN \"transaction_id\"`,\r\n    );\r\n  }\r\n}\r\n"]}