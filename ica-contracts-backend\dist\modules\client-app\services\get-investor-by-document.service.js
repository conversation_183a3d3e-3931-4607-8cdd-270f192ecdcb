"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetInvestorByDocumentService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const business_entity_1 = require("../../../shared/database/typeorm/entities/business.entity");
const owner_entity_1 = require("../../../shared/database/typeorm/entities/owner.entity");
const roles_enum_1 = require("../../../shared/enums/roles.enum");
const typeorm_2 = require("typeorm");
const client_account_service_1 = require("./client-account.service");
let GetInvestorByDocumentService = class GetInvestorByDocumentService {
    constructor(ownerDb, businessDb, clientAccountService) {
        this.ownerDb = ownerDb;
        this.businessDb = businessDb;
        this.clientAccountService = clientAccountService;
    }
    async perform(data, apiKey) {
        const validApiKey = process.env.CLIENT_APP_API_KEY || 'icainvest-api-key';
        if (apiKey !== validApiKey) {
            throw new common_1.UnauthorizedException('Chave de API inválida');
        }
        const { document } = data;
        const typeDocument = this.cpfOrCnpj(document);
        let userId;
        if (typeDocument === 'cpf') {
            const owner = await this.ownerDb.findOne({
                relations: ['ownerRoleRelation', 'ownerRoleRelation.role'],
                where: {
                    cpf: document,
                },
            });
            if (!owner) {
                throw new common_1.NotFoundException('Investidor não encontrado');
            }
            const hasInvestorRole = owner.ownerRoleRelation.some((relation) => relation.role.name === roles_enum_1.RolesEnum.INVESTOR);
            if (!hasInvestorRole) {
                throw new common_1.UnauthorizedException('Acesso permitido apenas para investidores');
            }
            userId = owner.ownerRoleRelation.find((relation) => relation.role.name === roles_enum_1.RolesEnum.INVESTOR)?.id;
        }
        else if (typeDocument === 'cnpj') {
            const business = await this.businessDb.findOne({
                relations: ['ownerRoleRelation', 'ownerRoleRelation.role'],
                where: {
                    cnpj: document,
                },
            });
            if (!business) {
                throw new common_1.NotFoundException('Investidor não encontrado');
            }
            const hasInvestorRole = business.ownerRoleRelation.some((relation) => relation.role.name === roles_enum_1.RolesEnum.INVESTOR);
            if (!hasInvestorRole) {
                throw new common_1.UnauthorizedException('Acesso permitido apenas para investidores');
            }
            userId = business.ownerRoleRelation.find((relation) => relation.role.name === roles_enum_1.RolesEnum.INVESTOR)?.id;
        }
        else {
            throw new common_1.NotFoundException('Tipo de documento inválido');
        }
        if (!userId) {
            throw new common_1.NotFoundException('ID de investidor não encontrado');
        }
        const accountInfo = await this.clientAccountService.getAccountInfo(userId);
        return {
            account: accountInfo,
        };
    }
    cpfOrCnpj(document) {
        if (document.length > 11) {
            return 'cnpj';
        }
        return 'cpf';
    }
};
exports.GetInvestorByDocumentService = GetInvestorByDocumentService;
exports.GetInvestorByDocumentService = GetInvestorByDocumentService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(owner_entity_1.OwnerEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(business_entity_1.BusinessEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        client_account_service_1.ClientAccountService])
], GetInvestorByDocumentService);
//# sourceMappingURL=get-investor-by-document.service.js.map