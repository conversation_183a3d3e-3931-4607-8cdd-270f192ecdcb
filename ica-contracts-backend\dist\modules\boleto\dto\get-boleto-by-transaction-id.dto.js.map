{"version": 3, "file": "get-boleto-by-transaction-id.dto.js", "sourceRoot": "/", "sources": ["modules/boleto/dto/get-boleto-by-transaction-id.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAAuD;AAEvD,MAAa,2BAA2B;CAIvC;AAJD,kEAIC;AADC;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;kEACS", "sourcesContent": ["import { IsNotEmpty, IsString } from 'class-validator';\r\n\r\nexport class GetBoletoByTransactionIdDto {\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  transactionId: string;\r\n}\r\n"]}