{"version": 3, "file": "1708493584449-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1708493584449-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAWnC,CAAC;IATQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,yDAAyD,CAC1D,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;IAC3E,CAAC;CACF;AAZD,0DAYC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1708493584449 implements MigrationInterface {\r\n  name = 'Migrations1708493584449';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"business\" ADD \"owner_id\" character varying`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(`ALTER TABLE \"business\" DROP COLUMN \"owner_id\"`);\r\n  }\r\n}\r\n"]}