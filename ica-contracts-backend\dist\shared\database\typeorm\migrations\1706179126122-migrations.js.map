{"version": 3, "file": "1706179126122-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1706179126122-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAyEnC,CAAC;IAvEQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,yTAAyT,CAC1T,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,yiBAAyiB,CAC1iB,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,0iBAA0iB,CAC3iB,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,8fAA8f,CAC/f,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,qbAAqb,CACtb,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,4lBAA4lB,CAC7lB,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,iLAAiL,CAClL,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,uLAAuL,CACxL,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,iKAAiK,CAClK,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,uKAAuK,CACxK,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,iKAAiK,CAClK,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,uKAAuK,CACxK,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,yKAAyK,CAC1K,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,4EAA4E,CAC7E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wEAAwE,CACzE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wEAAwE,CACzE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wEAAwE,CACzE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wEAAwE,CACzE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wFAAwF,CACzF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wFAAwF,CACzF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;QACpD,MAAM,WAAW,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAChD,MAAM,WAAW,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;QACjD,MAAM,WAAW,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAChD,MAAM,WAAW,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;QAC9C,MAAM,WAAW,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;IAClE,CAAC;CACF;AA1ED,0DA0EC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1706179126122 implements MigrationInterface {\r\n  name = 'Migrations1706179126122';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"owner_business_relation\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"owner_id\" uuid NOT NULL, \"business_id\" uuid NOT NULL, \"role\" character varying NOT NULL, \"created_at\" TIMESTAMP NOT NULL DEFAULT now(), \"deleted_at\" TIMESTAMP, CONSTRAINT \"PK_13fd114daf4ffe5012f5eb826b5\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"owner\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"name\" character varying NOT NULL, \"cpf\" character varying NOT NULL, \"email\" character varying NOT NULL, \"phone\" character varying NOT NULL, \"mother_name\" character varying NOT NULL, \"dt_birth\" date NOT NULL, \"pep\" character varying NOT NULL, \"nickname\" character varying, \"avatar\" character varying, \"updated_at\" TIMESTAMP NOT NULL DEFAULT now(), \"created_at\" TIMESTAMP NOT NULL DEFAULT now(), \"deleted_at\" TIMESTAMP, CONSTRAINT \"PK_8e86b6b9f94aece7d12d465dc0c\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"address\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"owner_id\" uuid, \"business_id\" uuid, \"cep\" character varying NOT NULL, \"street\" character varying NOT NULL, \"neighborhood\" character varying NOT NULL, \"number\" character varying NOT NULL, \"city\" character varying NOT NULL, \"state\" character varying NOT NULL, \"complement\" character varying NOT NULL, \"updated_at\" TIMESTAMP NOT NULL DEFAULT now(), \"created_at\" TIMESTAMP NOT NULL DEFAULT now(), \"deleted_at\" TIMESTAMP, CONSTRAINT \"PK_d92de1f82754668b5f5f5dd4fd5\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"business\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"company_name\" character varying NOT NULL, \"fantasy_name\" character varying NOT NULL, \"cnpj\" character varying NOT NULL, \"type\" character varying NOT NULL, \"size\" character varying NOT NULL, \"email\" character varying NOT NULL, \"dt_opening\" date NOT NULL, \"updated_at\" TIMESTAMP NOT NULL DEFAULT now(), \"created_at\" TIMESTAMP NOT NULL DEFAULT now(), \"deleted_at\" TIMESTAMP, CONSTRAINT \"PK_0bd850da8dafab992e2e9b058e5\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"account\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"owner_id\" uuid, \"business_id\" uuid, \"number\" character varying NOT NULL, \"branch\" character varying NOT NULL, \"status\" character varying NOT NULL, \"type\" character varying NOT NULL, \"updated_at\" TIMESTAMP NOT NULL DEFAULT now(), \"created_at\" TIMESTAMP NOT NULL DEFAULT now(), \"deleted_at\" TIMESTAMP, CONSTRAINT \"PK_54115ee388cdb6d86bb4bf5b2ea\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"transaction\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"value\" character varying NOT NULL, \"account_id\" uuid NOT NULL, \"code\" character varying NOT NULL, \"type\" character varying NOT NULL, \"description\" character varying, \"destiny_account\" character varying, \"destiny_branch\" character varying, \"destiny_bank\" character varying, \"destiny_document\" character varying, \"destiny_name\" character varying, \"updated_at\" TIMESTAMP NOT NULL DEFAULT now(), \"created_at\" TIMESTAMP NOT NULL DEFAULT now(), \"deleted_at\" TIMESTAMP, CONSTRAINT \"PK_89eadb93a89810556e1cbcd6ab9\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner_business_relation\" ADD CONSTRAINT \"FK_989f4ac312bb211adab3d23b804\" FOREIGN KEY (\"owner_id\") REFERENCES \"owner\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner_business_relation\" ADD CONSTRAINT \"FK_e1609daa891183934b6005b2875\" FOREIGN KEY (\"business_id\") REFERENCES \"business\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"address\" ADD CONSTRAINT \"FK_d507cd237dff1fc31f67551c2a1\" FOREIGN KEY (\"owner_id\") REFERENCES \"owner\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"address\" ADD CONSTRAINT \"FK_ac5eea1bbea2cce5156e8f67c27\" FOREIGN KEY (\"business_id\") REFERENCES \"business\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account\" ADD CONSTRAINT \"FK_7e86daab9d155ec4cc3fd654454\" FOREIGN KEY (\"owner_id\") REFERENCES \"owner\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account\" ADD CONSTRAINT \"FK_1c4f6487028b13c8c6b7b17ee83\" FOREIGN KEY (\"business_id\") REFERENCES \"business\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"transaction\" ADD CONSTRAINT \"FK_e2652fa8c16723c83a00fb9b17e\" FOREIGN KEY (\"account_id\") REFERENCES \"account\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"transaction\" DROP CONSTRAINT \"FK_e2652fa8c16723c83a00fb9b17e\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account\" DROP CONSTRAINT \"FK_1c4f6487028b13c8c6b7b17ee83\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account\" DROP CONSTRAINT \"FK_7e86daab9d155ec4cc3fd654454\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"address\" DROP CONSTRAINT \"FK_ac5eea1bbea2cce5156e8f67c27\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"address\" DROP CONSTRAINT \"FK_d507cd237dff1fc31f67551c2a1\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner_business_relation\" DROP CONSTRAINT \"FK_e1609daa891183934b6005b2875\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner_business_relation\" DROP CONSTRAINT \"FK_989f4ac312bb211adab3d23b804\"`,\r\n    );\r\n    await queryRunner.query(`DROP TABLE \"transaction\"`);\r\n    await queryRunner.query(`DROP TABLE \"account\"`);\r\n    await queryRunner.query(`DROP TABLE \"business\"`);\r\n    await queryRunner.query(`DROP TABLE \"address\"`);\r\n    await queryRunner.query(`DROP TABLE \"owner\"`);\r\n    await queryRunner.query(`DROP TABLE \"owner_business_relation\"`);\r\n  }\r\n}\r\n"]}