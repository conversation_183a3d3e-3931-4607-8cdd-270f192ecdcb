"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1711643905358 = void 0;
class Migrations1711643905358 {
    constructor() {
        this.name = 'Migrations1711643905358';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "billet" ADD "status" character varying NOT NULL`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "billet" DROP COLUMN "status"`);
    }
}
exports.Migrations1711643905358 = Migrations1711643905358;
//# sourceMappingURL=1711643905358-migrations.js.map