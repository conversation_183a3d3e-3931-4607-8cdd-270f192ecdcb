export interface IClientPaymentResponse {
    id: string;
    scheduledDate: string;
    amount: number;
    status: 'PAID' | 'SCHEDULED';
    paidDate?: string;
}
export interface IClientPaymentsListResponse {
    payments: IClientPaymentResponse[];
    total: number;
    totalPaid: number;
    totalScheduled: number;
}
export interface IClientPaymentDetailResponse {
    id: string;
    scheduledDate: string;
    amount: number;
    status: 'PAID' | 'SCHEDULED';
    paidDate?: string;
    contractId: string;
    contractType: string;
    investedAmount: number;
    bank?: string;
    agency?: string;
    account?: string;
    pixKey?: string;
}
