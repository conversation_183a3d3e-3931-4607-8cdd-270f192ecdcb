import { ConsultCelcoinWebhookDto } from '../dto/consult-celcoin-webhooks.dto';
import { CreateAccountCelcoinWebhookDto } from '../dto/create-acount-celcoin-webhook.dto';
import { RegisterCelcoinWebhookDto } from '../dto/register-celcoin-webhook.dto';
import { UpdateCelcoinWebhookDto } from '../dto/update-celcoin-webhooks.dto';
import { BillPaymentCelcoinWebhookService } from '../services/billpayment-celcoin-webhook.service';
import { CelcoinPixPaymentWebhookService } from '../services/celcoin-pix-payment-webhook.service';
import { ChargeCreateCelcoinWebhookService } from '../services/charge-create-celcoin-webhook.service';
import { ChargeInCelcoinWebhookService } from '../services/charge-in-celcoin-webhook.service';
import { ConsultCelcoinWebhookService } from '../services/consult-celcoin-webhook.service';
import { CreateAccountCelcoinWebhookService } from '../services/create-account-celcoin-webhook.service';
import { KycCelcoinWebhookService } from '../services/kyc-celcoin-webhook.service';
import { PixCashoutCelcoinWebhookService } from '../services/pix-cashout-celcoin-webhook.service';
import { PixClaimCancelledCelcoinWebhookService } from '../services/pix-claim-cancelled-celcoin-webhook.service';
import { PixClaimCompletedCelcoinWebhookService } from '../services/pix-claim-completed-celcoin-webhook.service';
import { PixClaimConfirmedCelcoinWebhookService } from '../services/pix-claim-confirmed-celcoin-webhook.service';
import { PixClaimOpenCelcoinWebhookService } from '../services/pix-claim-open-celcoin-webhook.service';
import { PixClaimWaitingCelcoinWebhookService } from '../services/pix-claim-waiting-celcoin-webhook.service';
import { RegisterCelcoinWebhookService } from '../services/register-celcoin-webhook.service';
import { UpdateCelcoinWebhookService } from '../services/update-celcoin-webhook.service';
export declare class WebhookCelcoinController {
    private readonly celcoinPixPaymentWebhookService;
    private readonly updateWebhooksService;
    private readonly pixClaimOpenCelcoinWebhookService;
    private readonly billPaymentCelcoinWebhookService;
    private readonly createAccountService;
    private readonly pixCashoutService;
    private readonly chargeInCelcoinWebhookService;
    private readonly chargeCreateCelcoinWebhookService;
    private readonly kycCelcoinWebhookService;
    private readonly registerCelcoinWebhookService;
    private readonly consultCelcoinWebhookService;
    private readonly pixClaimWaitingCelcoinWebhookService;
    private readonly pixClaimConfirmedCelcoinWebhookService;
    private readonly pixClaimCancelledCelcoinWebhookService;
    private readonly pixClaimCompletedCelcoinWebhookService;
    constructor(celcoinPixPaymentWebhookService: CelcoinPixPaymentWebhookService, updateWebhooksService: UpdateCelcoinWebhookService, pixClaimOpenCelcoinWebhookService: PixClaimOpenCelcoinWebhookService, billPaymentCelcoinWebhookService: BillPaymentCelcoinWebhookService, createAccountService: CreateAccountCelcoinWebhookService, pixCashoutService: PixCashoutCelcoinWebhookService, chargeInCelcoinWebhookService: ChargeInCelcoinWebhookService, chargeCreateCelcoinWebhookService: ChargeCreateCelcoinWebhookService, kycCelcoinWebhookService: KycCelcoinWebhookService, registerCelcoinWebhookService: RegisterCelcoinWebhookService, consultCelcoinWebhookService: ConsultCelcoinWebhookService, pixClaimWaitingCelcoinWebhookService: PixClaimWaitingCelcoinWebhookService, pixClaimConfirmedCelcoinWebhookService: PixClaimConfirmedCelcoinWebhookService, pixClaimCancelledCelcoinWebhookService: PixClaimCancelledCelcoinWebhookService, pixClaimCompletedCelcoinWebhookService: PixClaimCompletedCelcoinWebhookService);
    registerWebhook(body: RegisterCelcoinWebhookDto): Promise<void>;
    consultWebhooks(query: ConsultCelcoinWebhookDto): Promise<any>;
    updateWebhooks(body: UpdateCelcoinWebhookDto): Promise<any>;
    createAccount(data: CreateAccountCelcoinWebhookDto): Promise<void>;
    pixCashout(data: any): Promise<void>;
    cashInWebhook(body: any): Promise<void>;
    claimOpen(body: any): Promise<void>;
    claimWaiting(body: any): Promise<void>;
    claimConfirmed(body: any): Promise<void>;
    claimCancelled(body: any): Promise<void>;
    claimCompleted(body: any): Promise<void>;
    billPayment(body: any): Promise<void>;
    chargeIn(body: any): Promise<void>;
    chargeCreate(body: any): Promise<void>;
    kyc(body: any): Promise<void>;
}
