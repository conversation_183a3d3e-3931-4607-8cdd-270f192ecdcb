"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1741961230746 = void 0;
class Migrations1741961230746 {
    constructor() {
        this.name = 'Migrations1741961230746';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "income_payment_scheduled_addendum" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "proportional_days" integer NOT NULL, "income_payment_scheduled_id" uuid, "addendum_id" integer, CONSTRAINT "PK_f1b9d433fc301d7d5672a6fc18f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "income_payment_scheduled" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "scheduled_date" date NOT NULL, "amount" numeric(12,2) NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "contract_id" uuid, CONSTRAINT "PK_0af6ec8d4cb3402f0b044e887cb" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "payment_distribution" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "planned_amount" numeric(12,2) NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "scheduled_payment_id" uuid NOT NULL, "participant_id" uuid NOT NULL, CONSTRAINT "PK_d9a717d5c89f5f8cda298ffc6cf" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "income_payment" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "payment_distribution_id" uuid NOT NULL, "paid_at" date NOT NULL, "paid_amount" numeric(12,2) NOT NULL, "payment_method" character varying, "transaction_reference" character varying, "info" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "file_id" uuid, CONSTRAINT "REL_69fd3a33405e78e32acfb3c4d0" UNIQUE ("payment_distribution_id"), CONSTRAINT "PK_64e0650628dd1a2ae42ca28b0f7" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "income" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "income_day" numeric, "payment_date" TIMESTAMP, "amount" integer, "percentage" numeric, "number_contracts" integer, "number_clients" integer, "contract_type" character varying, "role" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "owner_role_relation" uuid, CONSTRAINT "PK_29a10f17b97568f70cee8586d58" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "income_payment_scheduled_addendum" ADD CONSTRAINT "FK_9a8c92a6289fd28b96c5ca54163" FOREIGN KEY ("income_payment_scheduled_id") REFERENCES "income_payment_scheduled"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "income_payment_scheduled_addendum" ADD CONSTRAINT "FK_8fc4b7fc74077dd64629a3524b7" FOREIGN KEY ("addendum_id") REFERENCES "addendum"("id_addendum") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "income_payment_scheduled" ADD CONSTRAINT "FK_7f6668dd01215fa2445bf8f240a" FOREIGN KEY ("contract_id") REFERENCES "contract"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "payment_distribution" ADD CONSTRAINT "FK_678ded15de538df35bff86369dd" FOREIGN KEY ("scheduled_payment_id") REFERENCES "income_payment_scheduled"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "payment_distribution" ADD CONSTRAINT "FK_2f69b8fe5016246b895ac497ea3" FOREIGN KEY ("participant_id") REFERENCES "owner_role_relation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "income_payment" ADD CONSTRAINT "FK_57052a1821ced5041c5b53bb578" FOREIGN KEY ("file_id") REFERENCES "files"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "income_payment" ADD CONSTRAINT "FK_69fd3a33405e78e32acfb3c4d0e" FOREIGN KEY ("payment_distribution_id") REFERENCES "payment_distribution"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "income" ADD CONSTRAINT "FK_2542605a11a32fcc24bd4c14a58" FOREIGN KEY ("owner_role_relation") REFERENCES "owner_role_relation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "income" DROP CONSTRAINT "FK_2542605a11a32fcc24bd4c14a58"`);
        await queryRunner.query(`ALTER TABLE "income_payment" DROP CONSTRAINT "FK_69fd3a33405e78e32acfb3c4d0e"`);
        await queryRunner.query(`ALTER TABLE "income_payment" DROP CONSTRAINT "FK_57052a1821ced5041c5b53bb578"`);
        await queryRunner.query(`ALTER TABLE "payment_distribution" DROP CONSTRAINT "FK_2f69b8fe5016246b895ac497ea3"`);
        await queryRunner.query(`ALTER TABLE "payment_distribution" DROP CONSTRAINT "FK_678ded15de538df35bff86369dd"`);
        await queryRunner.query(`ALTER TABLE "income_payment_scheduled" DROP CONSTRAINT "FK_7f6668dd01215fa2445bf8f240a"`);
        await queryRunner.query(`ALTER TABLE "income_payment_scheduled_addendum" DROP CONSTRAINT "FK_8fc4b7fc74077dd64629a3524b7"`);
        await queryRunner.query(`ALTER TABLE "income_payment_scheduled_addendum" DROP CONSTRAINT "FK_9a8c92a6289fd28b96c5ca54163"`);
        await queryRunner.query(`DROP TABLE "income"`);
        await queryRunner.query(`DROP TABLE "income_payment"`);
        await queryRunner.query(`DROP TABLE "payment_distribution"`);
        await queryRunner.query(`DROP TABLE "income_payment_scheduled"`);
        await queryRunner.query(`DROP TABLE "income_payment_scheduled_addendum"`);
    }
}
exports.Migrations1741961230746 = Migrations1741961230746;
//# sourceMappingURL=1741961230746-migrations.js.map