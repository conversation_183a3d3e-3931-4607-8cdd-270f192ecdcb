"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1706334616350 = void 0;
class Migrations1706334616350 {
    constructor() {
        this.name = 'Migrations1706334616350';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "owner" ADD "password" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "owner" ADD "temporary_password" boolean NOT NULL`);
        await queryRunner.query(`ALTER TABLE "owner" ADD "refresh_token" character varying NOT NULL`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "owner" DROP COLUMN "refresh_token"`);
        await queryRunner.query(`ALTER TABLE "owner" DROP COLUMN "temporary_password"`);
        await queryRunner.query(`ALTER TABLE "owner" DROP COLUMN "password"`);
    }
}
exports.Migrations1706334616350 = Migrations1706334616350;
//# sourceMappingURL=1706334616350-migrations.js.map