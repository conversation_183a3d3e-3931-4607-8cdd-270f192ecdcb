{"version": 3, "file": "br-holiday.module.js", "sourceRoot": "/", "sources": ["shared/modules/br-holiday.module.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAAgD;AAEhD,0EAAqE;AAO9D,IAAM,eAAe,GAArB,MAAM,eAAe;CAAG,CAAA;AAAlB,0CAAe;0BAAf,eAAe;IAL3B,IAAA,eAAM,GAAE;IACR,IAAA,eAAM,EAAC;QACN,SAAS,EAAE,CAAC,uCAAiB,CAAC;QAC9B,OAAO,EAAE,CAAC,uCAAiB,CAAC;KAC7B,CAAC;GACW,eAAe,CAAG", "sourcesContent": ["import { Global, Module } from '@nestjs/common';\r\n\r\nimport { BrHolidayProvider } from '../providers/br-holiday.provider';\r\n\r\n@Global()\r\n@Module({\r\n  providers: [BrHolidayProvider],\r\n  exports: [BrHolidayProvider],\r\n})\r\nexport class BrHolidayModule {}\r\n"]}