"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClientAppModule = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const typeorm_1 = require("@nestjs/typeorm");
const business_entity_1 = require("../../shared/database/typeorm/entities/business.entity");
const contract_entity_1 = require("../../shared/database/typeorm/entities/contract.entity");
const income_payment_scheduled_entity_1 = require("../../shared/database/typeorm/entities/income-payment-scheduled.entity");
const owner_role_relation_entity_1 = require("../../shared/database/typeorm/entities/owner-role-relation.entity");
const owner_entity_1 = require("../../shared/database/typeorm/entities/owner.entity");
const check_investor_controller_1 = require("./controllers/check-investor.controller");
const client_contract_controller_1 = require("./controllers/client-contract.controller");
const client_login_controller_1 = require("./controllers/client-login.controller");
const client_payment_controller_1 = require("./controllers/client-payment.controller");
const get_investor_by_document_controller_1 = require("./controllers/get-investor-by-document.controller");
const format_date_helper_1 = require("./helpers/format-date.helper");
const check_investor_service_1 = require("./services/check-investor.service");
const client_account_service_1 = require("./services/client-account.service");
const client_contract_service_1 = require("./services/client-contract.service");
const client_login_service_1 = require("./services/client-login.service");
const client_payment_service_1 = require("./services/client-payment.service");
const get_investor_by_document_service_1 = require("./services/get-investor-by-document.service");
let ClientAppModule = class ClientAppModule {
};
exports.ClientAppModule = ClientAppModule;
exports.ClientAppModule = ClientAppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                owner_entity_1.OwnerEntity,
                business_entity_1.BusinessEntity,
                contract_entity_1.ContractEntity,
                income_payment_scheduled_entity_1.IncomePaymentScheduledEntity,
                owner_role_relation_entity_1.OwnerRoleRelationEntity,
            ]),
            jwt_1.JwtModule.register({
                secret: process.env.JWT_SECRET,
                signOptions: { expiresIn: '1h' },
            }),
        ],
        controllers: [
            client_login_controller_1.ClientLoginController,
            client_contract_controller_1.ClientContractController,
            client_payment_controller_1.ClientPaymentController,
            check_investor_controller_1.CheckInvestorController,
            get_investor_by_document_controller_1.GetInvestorByDocumentController,
        ],
        providers: [
            client_login_service_1.ClientLoginService,
            client_account_service_1.ClientAccountService,
            client_contract_service_1.ClientContractService,
            client_payment_service_1.ClientPaymentService,
            check_investor_service_1.CheckInvestorService,
            get_investor_by_document_service_1.GetInvestorByDocumentService,
            {
                provide: 'FORMAT_DATE',
                useValue: format_date_helper_1.formatDate,
            },
        ],
        exports: [
            client_login_service_1.ClientLoginService,
            client_account_service_1.ClientAccountService,
            client_contract_service_1.ClientContractService,
            client_payment_service_1.ClientPaymentService,
        ],
    })
], ClientAppModule);
//# sourceMappingURL=client-app.module.js.map