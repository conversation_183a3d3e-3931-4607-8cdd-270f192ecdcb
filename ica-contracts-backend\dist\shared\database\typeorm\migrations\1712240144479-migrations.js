"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1712240144479 = void 0;
class Migrations1712240144479 {
    constructor() {
        this.name = 'Migrations1712240144479';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "billet" DROP COLUMN "transaction_id"`);
        await queryRunner.query(`ALTER TABLE "transaction" ADD "transfer_date" TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "transaction" ADD "destiny_key" character varying`);
        await queryRunner.query(`ALTER TABLE "transaction" ADD "status" character varying NOT NULL DEFAULT 'PENDENT'`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "transaction" DROP COLUMN "status"`);
        await queryRunner.query(`ALTER TABLE "transaction" DROP COLUMN "destiny_key"`);
        await queryRunner.query(`ALTER TABLE "transaction" DROP COLUMN "transfer_date"`);
        await queryRunner.query(`ALTER TABLE "billet" ADD "transaction_id" character varying`);
    }
}
exports.Migrations1712240144479 = Migrations1712240144479;
//# sourceMappingURL=1712240144479-migrations.js.map