import { ForceSignatureCheckController } from '@/presentation/http/controllers/contracts/force-signature-check.controller'
import { makeProcessAuditSignatureUseCase } from '../../usecases/process-audit-signature.factory'
import { makeProcessInvestorSignatureUseCase } from '../../usecases/process-investor-signature.factory'

export function makeForceSignatureCheckController() {
  const processAuditSignatureUseCase = makeProcessAuditSignatureUseCase()
  const processInvestorSignatureUseCase = makeProcessInvestorSignatureUseCase()
  
  const controller = new ForceSignatureCheckController(
    processAuditSignatureUseCase,
    processInvestorSignatureUseCase
  )

  return controller
}
