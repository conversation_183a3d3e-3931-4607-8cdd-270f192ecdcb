"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1736955962754 = void 0;
class Migrations1736955962754 {
    constructor() {
        this.name = 'Migrations1736955962754';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "notification" DROP CONSTRAINT "FK_227bce406a187fd48c06b102fe0"`);
        await queryRunner.query(`ALTER TABLE "notification" DROP COLUMN "owner_role_relation"`);
        await queryRunner.query(`ALTER TABLE "notification" DROP COLUMN "message"`);
        await queryRunner.query(`ALTER TABLE "notification" ADD "type" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "notification" ADD "description" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "notification" ADD "contract_value" integer`);
        await queryRunner.query(`ALTER TABLE "notification" ADD "user_owner_role_relation_id" uuid`);
        await queryRunner.query(`ALTER TABLE "notification" ADD "admin_owner_role_relation_id" uuid`);
        await queryRunner.query(`ALTER TABLE "notification" ALTER COLUMN "title" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "notification" ADD CONSTRAINT "FK_8ca93a4412001fddb88e326b91f" FOREIGN KEY ("user_owner_role_relation_id") REFERENCES "owner_role_relation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "notification" ADD CONSTRAINT "FK_1786280b5e015f1c1a408b970b7" FOREIGN KEY ("admin_owner_role_relation_id") REFERENCES "owner_role_relation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "notification" DROP CONSTRAINT "FK_1786280b5e015f1c1a408b970b7"`);
        await queryRunner.query(`ALTER TABLE "notification" DROP CONSTRAINT "FK_8ca93a4412001fddb88e326b91f"`);
        await queryRunner.query(`ALTER TABLE "notification" ALTER COLUMN "title" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "notification" DROP COLUMN "admin_owner_role_relation_id"`);
        await queryRunner.query(`ALTER TABLE "notification" DROP COLUMN "user_owner_role_relation_id"`);
        await queryRunner.query(`ALTER TABLE "notification" DROP COLUMN "contract_value"`);
        await queryRunner.query(`ALTER TABLE "notification" DROP COLUMN "description"`);
        await queryRunner.query(`ALTER TABLE "notification" DROP COLUMN "type"`);
        await queryRunner.query(`ALTER TABLE "notification" ADD "message" character varying`);
        await queryRunner.query(`ALTER TABLE "notification" ADD "owner_role_relation" uuid`);
        await queryRunner.query(`ALTER TABLE "notification" ADD CONSTRAINT "FK_227bce406a187fd48c06b102fe0" FOREIGN KEY ("owner_role_relation") REFERENCES "owner_role_relation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
}
exports.Migrations1736955962754 = Migrations1736955962754;
//# sourceMappingURL=1736955962754-migrations.js.map