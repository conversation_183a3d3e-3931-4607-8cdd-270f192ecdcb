"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1733518571010 = void 0;
class Migrations1733518571010 {
    constructor() {
        this.name = 'Migrations1733518571010';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TYPE "public"."addendum_status_enum" AS ENUM('DRAFT', 'SENT_FOR_SIGNATURE', 'PENDING_INVESTOR_SIGNATURE', 'FULLY_SIGNED', 'CANCELED', 'EXPIRED')`);
        await queryRunner.query(`CREATE TABLE "addendum" ("id_addendum" SERIAL NOT NULL, "status" "public"."addendum_status_enum" NOT NULL DEFAULT 'DRAFT', "application_date" date NOT NULL, "addendum_value" numeric(15,2) NOT NULL, "yield_rate" numeric(5,2) NOT NULL, "reason" text, "notes" text, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "contract_id" uuid NOT NULL, CONSTRAINT "PK_831c7c3e13213217a199e22fbc1" PRIMARY KEY ("id_addendum"))`);
        await queryRunner.query(`ALTER TABLE "addendum" ADD CONSTRAINT "FK_5e577f2ba8c17a9b5420dc8dbc0" FOREIGN KEY ("contract_id") REFERENCES "contract"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "addendum" DROP CONSTRAINT "FK_5e577f2ba8c17a9b5420dc8dbc0"`);
        await queryRunner.query(`DROP TABLE "addendum"`);
        await queryRunner.query(`DROP TYPE "public"."addendum_status_enum"`);
    }
}
exports.Migrations1733518571010 = Migrations1733518571010;
//# sourceMappingURL=1733518571010-migrations.js.map