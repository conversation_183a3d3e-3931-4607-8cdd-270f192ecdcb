{"version": 3, "file": "1733251843139-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1733251843139-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAiBnC,CAAC;IAfQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,0DAA0D,CAC3D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,gEAAgE,CACjE,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,sDAAsD,CACvD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC;IAC5E,CAAC;CACF;AAlBD,0DAkBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1733251843139 implements MigrationInterface {\r\n  name = 'Migrations1733251843139';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pre_register\" ADD \"state\" character varying`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pre_register\" ADD \"mother_name\" character varying`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pre_register\" DROP COLUMN \"mother_name\"`,\r\n    );\r\n    await queryRunner.query(`ALTER TABLE \"pre_register\" DROP COLUMN \"state\"`);\r\n  }\r\n}\r\n"]}