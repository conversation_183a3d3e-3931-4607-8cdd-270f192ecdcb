{"version": 3, "file": "investment-extract.dto.js", "sourceRoot": "/", "sources": ["modules/account/dto/investment-extract.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAAiE;AAEjE,MAAa,kBAAkB;CAM9B;AAND,gDAMC;AADC;IAJC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;IACN,IAAA,qBAAG,EAAC,EAAE,CAAC;;iDACM", "sourcesContent": ["import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';\r\n\r\nexport class IInvestmentExtract {\r\n  @IsOptional()\r\n  @IsNumber()\r\n  @Min(1)\r\n  @Max(12)\r\n  month: string;\r\n}\r\n"]}