"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1743801861780 = void 0;
class Migrations1743801861780 {
    constructor() {
        this.name = 'Migrations1743801861780';
    }
    async up(queryRunner) {
        await queryRunner.query(`
            ALTER TABLE "email" 
            ADD COLUMN "external_id" character varying
        `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
            ALTER TABLE "email" 
            DROP COLUMN "external_id"
        `);
    }
}
exports.Migrations1743801861780 = Migrations1743801861780;
//# sourceMappingURL=1743801861780-migrations.js.map