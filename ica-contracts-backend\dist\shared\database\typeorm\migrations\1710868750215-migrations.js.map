{"version": 3, "file": "1710868750215-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1710868750215-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAenC,CAAC;IAbQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,iFAAiF,CAClF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,4DAA4D,CAC7D,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CAAC,kDAAkD,CAAC,CAAC;QAC5E,MAAM,WAAW,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;IACxE,CAAC;CACF;AAhBD,0DAgBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1710868750215 implements MigrationInterface {\r\n  name = 'Migrations1710868750215';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pix-key\" ADD \"status\" character varying NOT NULL DEFAULT 'PENDENT'`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pix-key\" ADD \"claim_status\" character varying`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(`ALTER TABLE \"pix-key\" DROP COLUMN \"claim_status\"`);\r\n    await queryRunner.query(`ALTER TABLE \"pix-key\" DROP COLUMN \"status\"`);\r\n  }\r\n}\r\n"]}