"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1726855244568 = void 0;
class Migrations1726855244568 {
    constructor() {
        this.name = 'Migrations1726855244568';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "contract" ALTER COLUMN "start_contract" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "contract" ALTER COLUMN "end_contract" DROP DEFAULT`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "contract" ALTER COLUMN "end_contract" SET DEFAULT '2024-09-20'`);
        await queryRunner.query(`ALTER TABLE "contract" ALTER COLUMN "start_contract" SET DEFAULT '2024-09-20'`);
    }
}
exports.Migrations1726855244568 = Migrations1726855244568;
//# sourceMappingURL=1726855244568-migrations.js.map