{"version": 3, "file": "1734399301620-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1734399301620-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IA+BnC,CAAC;IA7BQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,0XAA0X,CAC3X,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,qVAAqV,CACtV,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,+DAA+D,CAChE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,oLAAoL,CACrL,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,oKAAoK,CACrK,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,+EAA+E,CAChF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,+EAA+E,CAChF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAC;QAC3E,MAAM,WAAW,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACvD,MAAM,WAAW,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;IAChD,CAAC;CACF;AAhCD,0DAgCC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1734399301620 implements MigrationInterface {\r\n  name = 'Migrations1734399301620';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"files\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"filename\" character varying(255) NOT NULL, \"url\" character varying(500) NOT NULL, \"type\" character varying(50) NOT NULL, \"createdAt\" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), \"updatedAt\" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT \"PK_6c16b9093a142e0e7613b04a3d9\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"addendum_files\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"type\" character varying(50) NOT NULL, \"createdAt\" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), \"updatedAt\" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), \"addendumId\" integer, \"fileId\" uuid, CONSTRAINT \"PK_ad6c646753270b6d29913531a00\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"addendum\" ADD \"yield_rate\" numeric(5,2) NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"addendum_files\" ADD CONSTRAINT \"FK_2629a7c65ba6d270c2bc2e8b8f2\" FOREIGN KEY (\"addendumId\") REFERENCES \"addendum\"(\"id_addendum\") ON DELETE CASCADE ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"addendum_files\" ADD CONSTRAINT \"FK_3f6aab9a35432b7e79f6e6f554f\" FOREIGN KEY (\"fileId\") REFERENCES \"files\"(\"id\") ON DELETE CASCADE ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"addendum_files\" DROP CONSTRAINT \"FK_3f6aab9a35432b7e79f6e6f554f\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"addendum_files\" DROP CONSTRAINT \"FK_2629a7c65ba6d270c2bc2e8b8f2\"`,\r\n    );\r\n    await queryRunner.query(`ALTER TABLE \"addendum\" DROP COLUMN \"yield_rate\"`);\r\n    await queryRunner.query(`DROP TABLE \"addendum_files\"`);\r\n    await queryRunner.query(`DROP TABLE \"files\"`);\r\n  }\r\n}\r\n"]}