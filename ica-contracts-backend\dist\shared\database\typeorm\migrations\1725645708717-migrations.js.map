{"version": 3, "file": "1725645708717-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1725645708717-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAuCnC,CAAC;IArCQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,yEAAyE,CAC1E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,yEAAyE,CAC1E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACzE,MAAM,WAAW,CAAC,KAAK,CAAC,kDAAkD,CAAC,CAAC;QAC5E,MAAM,WAAW,CAAC,KAAK,CAAC,kDAAkD,CAAC,CAAC;QAC5E,MAAM,WAAW,CAAC,KAAK,CACrB,uDAAuD,CACxD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,2LAA2L,CAC5L,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,yEAAyE,CAC1E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,0DAA0D,CAC3D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,qEAAqE,CACtE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACzE,MAAM,WAAW,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;QACtE,MAAM,WAAW,CAAC,KAAK,CACrB,kKAAkK,CACnK,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wKAAwK,CACzK,CAAC;IACJ,CAAC;CACF;AAxCD,0DAwCC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1725645708717 implements MigrationInterface {\r\n  name = 'Migrations1725645708717';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract\" DROP CONSTRAINT \"FK_f1f42c0cd4752f6420997f04f10\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract\" DROP CONSTRAINT \"FK_9b5bba51f1fc7a6a6ddbfe87c93\"`,\r\n    );\r\n    await queryRunner.query(`ALTER TABLE \"contract\" DROP COLUMN \"owner_id\"`);\r\n    await queryRunner.query(`ALTER TABLE \"contract\" DROP COLUMN \"business_id\"`);\r\n    await queryRunner.query(`ALTER TABLE \"contract\" DROP COLUMN \"creatorType\"`);\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract\" ADD \"owner_role_relation\" uuid`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract\" ADD CONSTRAINT \"FK_f405dde729bbe574001e86ff68a\" FOREIGN KEY (\"owner_role_relation\") REFERENCES \"owner_role_relation\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract\" DROP CONSTRAINT \"FK_f405dde729bbe574001e86ff68a\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract\" DROP COLUMN \"owner_role_relation\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract\" ADD \"creatorType\" character varying NOT NULL`,\r\n    );\r\n    await queryRunner.query(`ALTER TABLE \"contract\" ADD \"business_id\" uuid`);\r\n    await queryRunner.query(`ALTER TABLE \"contract\" ADD \"owner_id\" uuid`);\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract\" ADD CONSTRAINT \"FK_9b5bba51f1fc7a6a6ddbfe87c93\" FOREIGN KEY (\"owner_id\") REFERENCES \"owner\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract\" ADD CONSTRAINT \"FK_f1f42c0cd4752f6420997f04f10\" FOREIGN KEY (\"business_id\") REFERENCES \"business\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n}\r\n"]}