{"version": 3, "file": "audit-contract.dto.js", "sourceRoot": "/", "sources": ["modules/audit/dto/audit-contract.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AACA,qDAQyB;AACzB,2GAA+F;AAK/F,MAAa,gBAAgB;CAiB5B;AAjBD,4CAiBC;AAdC;IAFC,IAAA,wBAAM,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,yBAAyB,EAAE,CAAC;IACjD,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,8BAA8B,EAAE,CAAC;;oDACrC;AAInB;IAFC,IAAA,wBAAM,EAAC,yCAAiB,EAAE,EAAE,OAAO,EAAE,kBAAkB,EAAE,CAAC;IAC1D,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC;;kDACrB;AAI5B;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;kDACK;AAKlB;IAHC,IAAA,4BAAU,EAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,yCAAiB,CAAC,QAAQ,CAAC;IAC5D,IAAA,0BAAQ,EAAC,EAAE,OAAO,EAAE,yCAAyC,EAAE,CAAC;IAChE,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,gEAAgE,EAAE,CAAC;;0DAChD", "sourcesContent": ["import { Type } from 'class-transformer';\r\nimport {\r\n  IsEnum,\r\n  IsNotEmpty,\r\n  IsObject,\r\n  IsOptional,\r\n  IsString,\r\n  IsUUID,\r\n  ValidateIf,\r\n} from 'class-validator';\r\nimport { AuditDecisionEnum } from 'src/shared/database/typeorm/entities/contract-audit.entity';\r\n\r\n/**\r\n * DTO para a auditoria de contratos\r\n */\r\nexport class AuditContractDto {\r\n  @IsUUID(4, { message: 'ID do contrato inválido' })\r\n  @IsNotEmpty({ message: 'ID do contrato é obrigatório' })\r\n  contractId: string;\r\n\r\n  @IsEnum(AuditDecisionEnum, { message: 'Decisão inválida' })\r\n  @IsNotEmpty({ message: 'Decisão é obrigatória' })\r\n  decision: AuditDecisionEnum;\r\n\r\n  @IsString()\r\n  @IsOptional()\r\n  comments?: string;\r\n\r\n  @ValidateIf((o) => o.decision === AuditDecisionEnum.REJECTED)\r\n  @IsObject({ message: 'Motivos da rejeição devem ser um objeto' })\r\n  @IsNotEmpty({ message: 'Motivos da rejeição são obrigatórios para contratos rejeitados' })\r\n  rejectionReasons?: Record<string, string>;\r\n}"]}