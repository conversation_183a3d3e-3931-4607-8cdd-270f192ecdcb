"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1713815769604 = void 0;
class Migrations1713815769604 {
    constructor() {
        this.name = 'Migrations1713815769604';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "statistic" RENAME COLUMN "jsonb" TO "states_data"`);
        await queryRunner.query(`ALTER TABLE "statistic" DROP COLUMN "states_data"`);
        await queryRunner.query(`ALTER TABLE "statistic" ADD "states_data" jsonb NOT NULL`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "statistic" DROP COLUMN "states_data"`);
        await queryRunner.query(`ALTER TABLE "statistic" ADD "states_data" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "statistic" RENAME COLUMN "states_data" TO "jsonb"`);
    }
}
exports.Migrations1713815769604 = Migrations1713815769604;
//# sourceMappingURL=1713815769604-migrations.js.map