{"version": 3, "file": "client-account.service.js", "sourceRoot": "/", "sources": ["modules/client-app/services/client-account.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,6CAAmD;AACnD,+FAAsF;AACtF,+FAAsF;AACtF,+HAAoH;AACpH,qHAA0G;AAC1G,qFAA2E;AAC3E,qCAAqC;AAK9B,IAAM,oBAAoB,GAA1B,MAAM,oBAAoB;IAC/B,YAEmB,2BAAgE,EAEhE,kBAA8C,EAE9C,gCAA0E,EAE1E,UAA2C;QAN3C,gCAA2B,GAA3B,2BAA2B,CAAqC;QAEhE,uBAAkB,GAAlB,kBAAkB,CAA4B;QAE9C,qCAAgC,GAAhC,gCAAgC,CAA0C;QAE1E,eAAU,GAAV,UAAU,CAAiC;IAC3D,CAAC;IAEJ,KAAK,CAAC,cAAc,CAAC,aAAqB;QAGxC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC;YACjE,KAAK,EAAE;gBACL,EAAE,OAAO,EAAE,aAAa,EAAE;gBAC1B,EAAE,UAAU,EAAE,aAAa,EAAE;gBAC7B,EAAE,EAAE,EAAE,aAAa,EAAE;aACtB;YACD,SAAS,EAAE;gBACT,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;SACF,CAAC,CAAC;QACH,MAAM,MAAM,GAAG,WAAW,CAAC,EAAE,CAAC;QAC9B,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAEpB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;YACnD,KAAK,EAAE;gBACL,QAAQ,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;gBACxB,MAAM,EAAE,yCAAkB,CAAC,MAAM;aAClC;YACD,SAAS,EAAE;gBACT,WAAW,EAAE,IAAI;gBACjB,QAAQ,EAAE,EAAE,aAAa,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE;aAC5C;YACD,KAAK,EAAE;gBACL,SAAS,EAAE,KAAK;aACjB;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzC,OAAO;gBACL,IAAI,EAAE,EAAE;gBACR,KAAK,EAAE,CAAC;gBACR,WAAW,EAAE,EAAE;gBACf,YAAY,EAAE,EAAE;gBAChB,aAAa,EAAE,CAAC;gBAChB,aAAa,EAAE,CAAC;gBAChB,SAAS,EAAE,EAAE;aACd,CAAC;QACJ,CAAC;QAED,MAAM,IAAI,GACR,WAAW,EAAE,KAAK,EAAE,IAAI,IAAI,WAAW,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAE,CAAC;QAGvE,MAAM,eAAe,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE;YACjD,MAAM,eAAe,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,eAAe,IAAI,CAAC,CAAC;YAEtE,OAAO;gBACL,EAAE,EAAE,QAAQ,CAAC,EAAE;gBACf,IAAI,EAAE,SAAkB;gBACxB,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,aAAa,CAAC;gBAC7C,QAAQ,EAAE,eAAe;gBACzB,QAAQ,EAAE,QAAQ,CAAC,WAAW,IAAI,EAAE;aACrC,CAAC;QACJ,CAAC,CAAC,CAAC;QAGH,MAAM,iBAAiB,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE,CACvD,QAAQ,CAAC,QAAQ;aACd,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,MAAM,KAAK,gCAAc,CAAC,YAAY,CAAC;aACrE,MAAM,CACL,CAAC,QAAQ,EAAE,EAAE,CACX,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAC9D;aACA,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;YAClB,EAAE,EAAE,QAAQ,CAAC,EAAE;YACf,IAAI,EAAE,UAAmB;YACzB,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,eAAe,CAAC;YAC/C,QAAQ,EAAE,QAAQ,CAAC,KAAK;YACxB,QAAQ,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,GAAG,IAAI,EAAE;SACrD,CAAC,CAAC,CACN,CAAC;QAGF,MAAM,YAAY,GAAG,CAAC,GAAG,eAAe,EAAE,GAAG,iBAAiB,CAAC,CAAC;QAChE,MAAM,aAAa,GAAG,YAAY,CAAC,MAAM,CACvC,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE,CAAC,KAAK,GAAG,QAAQ,CAAC,QAAQ,EAC9C,CAAC,CACF,CAAC;QAGF,MAAM,eAAe,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;QAC3C,MAAM,YAAY,GAAG,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;QAGjE,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;QACzB,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,gCAAgC;aACrE,kBAAkB,CAAC,SAAS,CAAC;aAC7B,kBAAkB,CAAC,kBAAkB,EAAE,UAAU,CAAC;aAClD,kBAAkB,CAAC,mBAAmB,EAAE,UAAU,CAAC;aACnD,KAAK,CAAC,uBAAuB,EAAE,EAAE,MAAM,EAAE,CAAC;aAC1C,QAAQ,CAAC,iCAAiC,EAAE,EAAE,KAAK,EAAE,CAAC;aACtD,OAAO,CAAC,uBAAuB,EAAE,KAAK,CAAC;aACvC,MAAM,EAAE,CAAC;QAEZ,MAAM,WAAW,GAAG,oBAAoB;YACtC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,aAAa,CAAC;YACrD,CAAC,CAAC,EAAE,CAAC;QAGP,MAAM,eAAe,GACnB,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE,eAAe,IAAI,CAAC,CAAC;QAGxE,MAAM,aAAa,GAAG,aAAa,GAAG,CAAC,eAAe,GAAG,GAAG,CAAC,CAAC;QAE9D,OAAO;YACL,IAAI;YACJ,KAAK,EAAE,eAAe;YACtB,WAAW;YACX,YAAY;YACZ,aAAa;YACb,aAAa;YACb,SAAS,EAAE,YAAY;SACxB,CAAC;IACJ,CAAC;CACF,CAAA;AAnIY,oDAAoB;+BAApB,oBAAoB;IADhC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,oDAAuB,CAAC,CAAA;IAEzC,WAAA,IAAA,0BAAgB,EAAC,gCAAc,CAAC,CAAA;IAEhC,WAAA,IAAA,0BAAgB,EAAC,8DAA4B,CAAC,CAAA;IAE9C,WAAA,IAAA,eAAM,EAAC,aAAa,CAAC,CAAA;qCALwB,oBAAU;QAEnB,oBAAU;QAEI,oBAAU;GAPpD,oBAAoB,CAmIhC", "sourcesContent": ["import { Inject, Injectable } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { AddendumStatus } from 'src/shared/database/typeorm/entities/addendum.entity';\r\nimport { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';\r\nimport { IncomePaymentScheduledEntity } from 'src/shared/database/typeorm/entities/income-payment-scheduled.entity';\r\nimport { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';\r\nimport { ContractStatusEnum } from 'src/shared/enums/contract-status.enum';\r\nimport { Repository } from 'typeorm';\r\n\r\nimport { IClientAccount } from '../responses/client-login.response';\r\n\r\n@Injectable()\r\nexport class ClientAccountService {\r\n  constructor(\r\n    @InjectRepository(OwnerRoleRelationEntity)\r\n    private readonly ownerRoleRelationRepository: Repository<OwnerRoleRelationEntity>,\r\n    @InjectRepository(ContractEntity)\r\n    private readonly contractRepository: Repository<ContractEntity>,\r\n    @InjectRepository(IncomePaymentScheduledEntity)\r\n    private readonly incomePaymentScheduledRepository: Repository<IncomePaymentScheduledEntity>,\r\n    @Inject('FORMAT_DATE')\r\n    private readonly formatDate: (date: Date | string) => string,\r\n  ) {}\r\n\r\n  async getAccountInfo(userRequestId: string): Promise<IClientAccount> {\r\n    // Buscar informações do usuário\r\n\r\n    const userProfile = await this.ownerRoleRelationRepository.findOne({\r\n      where: [\r\n        { ownerId: userRequestId },\r\n        { businessId: userRequestId },\r\n        { id: userRequestId },\r\n      ],\r\n      relations: {\r\n        owner: true,\r\n        business: true,\r\n      },\r\n    });\r\n    const userId = userProfile.id;\r\n    console.log(userId);\r\n    // Buscar contratos do investidor com status ACTIVE\r\n    const contracts = await this.contractRepository.find({\r\n      where: {\r\n        investor: { id: userId },\r\n        status: ContractStatusEnum.ACTIVE, // Only get contracts with ACTIVE status\r\n      },\r\n      relations: {\r\n        signataries: true,\r\n        addendum: { addendumFiles: { file: true } },\r\n      },\r\n      order: {\r\n        createdAt: 'ASC',\r\n      },\r\n    });\r\n\r\n    if (!contracts || contracts.length === 0) {\r\n      return {\r\n        name: '',\r\n        yield: 0,\r\n        nextPayment: '',\r\n        contractDate: '',\r\n        totalInvested: 0,\r\n        paymentAmount: 0,\r\n        contracts: [],\r\n      };\r\n    }\r\n\r\n    const name =\r\n      userProfile?.owner?.name || userProfile?.business?.companyName || '';\r\n\r\n    // Transformar contratos no formato esperado\r\n    const clientContracts = contracts.map((contract) => {\r\n      const investmentValue = contract.signataries[0]?.investmentValue || 0;\r\n\r\n      return {\r\n        id: contract.id,\r\n        type: 'starter' as const,\r\n        date: this.formatDate(contract.startContract),\r\n        invested: investmentValue,\r\n        contract: contract.contractPdf || '',\r\n      };\r\n    });\r\n\r\n    // Adicionar aditivos como contratos do tipo \"additive\" (apenas os com status FULLY_SIGNED)\r\n    const additiveContracts = contracts.flatMap((contract) =>\r\n      contract.addendum\r\n        .filter((addendum) => addendum.status === AddendumStatus.FULLY_SIGNED) // Only include addendums with FULLY_SIGNED status\r\n        .filter(\r\n          (addendum) =>\r\n            addendum.addendumFiles && addendum.addendumFiles.length > 0,\r\n        ) // Only include addendums with files\r\n        .map((addendum) => ({\r\n          id: addendum.id,\r\n          type: 'additive' as const,\r\n          date: this.formatDate(addendum.applicationDate),\r\n          invested: addendum.value,\r\n          contract: addendum.addendumFiles[0]?.file?.url || '',\r\n        })),\r\n    );\r\n\r\n    // Calcular totais\r\n    const allContracts = [...clientContracts, ...additiveContracts];\r\n    const totalInvested = allContracts.reduce(\r\n      (total, contract) => total + contract.invested,\r\n      0,\r\n    );\r\n\r\n    // Encontrar a data do primeiro contrato (tipo starter)\r\n    const starterContract = clientContracts[0];\r\n    const contractDate = starterContract ? starterContract.date : '';\r\n\r\n    // Buscar o próximo pagamento programado\r\n    const today = new Date();\r\n    const nextPaymentScheduled = await this.incomePaymentScheduledRepository\r\n      .createQueryBuilder('payment')\r\n      .innerJoinAndSelect('payment.contract', 'contract')\r\n      .innerJoinAndSelect('contract.investor', 'investor')\r\n      .where('investor.id = :userId', { userId })\r\n      .andWhere('payment.scheduledDate >= :today', { today })\r\n      .orderBy('payment.scheduledDate', 'ASC')\r\n      .getOne();\r\n\r\n    const nextPayment = nextPaymentScheduled\r\n      ? this.formatDate(nextPaymentScheduled.scheduledDate)\r\n      : '';\r\n\r\n    // Usar o rendimento do último contrato ou aditivo\r\n    const yieldPercentage =\r\n      contracts[contracts.length - 1]?.signataries[0]?.investmentYield || 0;\r\n\r\n    // Calcular valor do pagamento baseado no rendimento e valor total\r\n    const paymentAmount = totalInvested * (yieldPercentage / 100);\r\n\r\n    return {\r\n      name,\r\n      yield: yieldPercentage,\r\n      nextPayment,\r\n      contractDate,\r\n      totalInvested,\r\n      paymentAmount,\r\n      contracts: allContracts,\r\n    };\r\n  }\r\n}\r\n"]}