"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const contract_audit_entity_1 = require("../../shared/database/typeorm/entities/contract-audit.entity");
const contract_entity_1 = require("../../shared/database/typeorm/entities/contract.entity");
const owner_role_relation_entity_1 = require("../../shared/database/typeorm/entities/owner-role-relation.entity");
const contract_advisor_entity_1 = require("../../shared/database/typeorm/entities/contract-advisor.entity");
const shared_module_1 = require("../../shared/shared.module");
const notification_module_1 = require("../notifications/notification.module");
const audit_controller_1 = require("./controllers/audit.controller");
const audit_contract_service_1 = require("./services/audit-contract.service");
const get_contract_audit_history_service_1 = require("./services/get-contract-audit-history.service");
let AuditModule = class AuditModule {
};
exports.AuditModule = AuditModule;
exports.AuditModule = AuditModule = __decorate([
    (0, common_1.Module)({
        imports: [
            shared_module_1.SharedModule,
            typeorm_1.TypeOrmModule.forFeature([
                contract_audit_entity_1.ContractAuditEntity,
                contract_entity_1.ContractEntity,
                owner_role_relation_entity_1.OwnerRoleRelationEntity,
                contract_advisor_entity_1.ContractAdvisorEntity,
            ]),
            notification_module_1.NotificationModule,
        ],
        controllers: [audit_controller_1.AuditController],
        providers: [audit_contract_service_1.AuditContractService, get_contract_audit_history_service_1.GetContractAuditHistoryService],
        exports: [audit_contract_service_1.AuditContractService, get_contract_audit_history_service_1.GetContractAuditHistoryService],
    })
], AuditModule);
//# sourceMappingURL=audit.module.js.map