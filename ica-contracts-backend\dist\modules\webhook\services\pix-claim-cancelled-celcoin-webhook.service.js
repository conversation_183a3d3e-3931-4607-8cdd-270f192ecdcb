"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PixClaimCancelledCelcoinWebhookService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const pix_key_entity_1 = require("../../../shared/database/typeorm/entities/pix-key.entity");
const claim_status_enum_1 = require("../../../shared/enums/claim-status.enum");
const pix_key_status_enum_1 = require("../../../shared/enums/pix-key-status.enum");
const logger_1 = require("../../../shared/logger");
const typeorm_2 = require("typeorm");
let PixClaimCancelledCelcoinWebhookService = class PixClaimCancelledCelcoinWebhookService {
    constructor(pixKeyRepository) {
        this.pixKeyRepository = pixKeyRepository;
    }
    async perform(input) {
        logger_1.logger.info('PixClaimCancelledCelcoinWebhookService.perform() -> ', input);
        const pixKey = await this.pixKeyRepository.findOne({
            where: {
                key: input.body.key,
                account: { number: input.body.claimerAccount.account.toString() },
            },
        });
        if (!pixKey) {
            return;
        }
        const metadata = pixKey.claimMetadata
            ? JSON.parse(pixKey.claimMetadata)
            : {};
        await this.pixKeyRepository.update({ id: pixKey.id }, {
            status: pix_key_status_enum_1.PixKeyStatusEnum.CANCELLED,
            claimStatus: claim_status_enum_1.ClaimStatusEnum.CANCELLED,
            claimMetadata: JSON.stringify({
                ...metadata,
                cancelledWebhook: input.body,
            }),
        });
    }
};
exports.PixClaimCancelledCelcoinWebhookService = PixClaimCancelledCelcoinWebhookService;
exports.PixClaimCancelledCelcoinWebhookService = PixClaimCancelledCelcoinWebhookService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(pix_key_entity_1.PixKeyEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], PixClaimCancelledCelcoinWebhookService);
//# sourceMappingURL=pix-claim-cancelled-celcoin-webhook.service.js.map