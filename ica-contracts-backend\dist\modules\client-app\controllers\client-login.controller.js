"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClientLoginController = void 0;
const common_1 = require("@nestjs/common");
const client_login_dto_1 = require("../dto/client-login.dto");
const client_login_service_1 = require("../services/client-login.service");
let ClientLoginController = class ClientLoginController {
    constructor(clientLoginService) {
        this.clientLoginService = clientLoginService;
    }
    async login(body) {
        try {
            const response = await this.clientLoginService.perform(body);
            return response;
        }
        catch (error) {
            console.error(error);
            throw new common_1.HttpException({
                status: common_1.HttpStatus.BAD_REQUEST,
            }, common_1.HttpStatus.BAD_REQUEST, {
                cause: error,
            });
        }
    }
};
exports.ClientLoginController = ClientLoginController;
__decorate([
    (0, common_1.Post)('login'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [client_login_dto_1.ClientLoginDto]),
    __metadata("design:returntype", Promise)
], ClientLoginController.prototype, "login", null);
exports.ClientLoginController = ClientLoginController = __decorate([
    (0, common_1.Controller)('client/auth'),
    __param(0, (0, common_1.Inject)(client_login_service_1.ClientLoginService)),
    __metadata("design:paramtypes", [client_login_service_1.ClientLoginService])
], ClientLoginController);
//# sourceMappingURL=client-login.controller.js.map