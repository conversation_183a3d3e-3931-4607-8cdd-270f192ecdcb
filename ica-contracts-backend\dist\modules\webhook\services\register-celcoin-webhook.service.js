"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RegisterCelcoinWebhookService = void 0;
const common_1 = require("@nestjs/common");
const register_webhook_service_1 = require("../../../apis/celcoin/services/register-webhook.service");
let RegisterCelcoinWebhookService = class RegisterCelcoinWebhookService {
    constructor(registerWebhookService) {
        this.registerWebhookService = registerWebhookService;
    }
    async perform(input) {
        return this.registerWebhookService.perform(input);
    }
};
exports.RegisterCelcoinWebhookService = RegisterCelcoinWebhookService;
exports.RegisterCelcoinWebhookService = RegisterCelcoinWebhookService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [register_webhook_service_1.RegisterWebhookService])
], RegisterCelcoinWebhookService);
//# sourceMappingURL=register-celcoin-webhook.service.js.map