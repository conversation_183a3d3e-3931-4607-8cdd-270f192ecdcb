{"version": 3, "file": "1708538218473-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1708538218473-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IA2CnC,CAAC;IAzCQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,oFAAoF,CACrF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,+UAA+U,CAChV,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,6DAA6D,CAC9D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,yDAAyD,CAC1D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACzE,MAAM,WAAW,CAAC,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACvE,MAAM,WAAW,CAAC,KAAK,CACrB,6JAA6J,CAC9J,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,oEAAoE,CACrE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,uDAAuD,CACxD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,kEAAkE,CACnE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,+EAA+E,CAChF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,0DAA0D,CAC3D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;QAC5C,MAAM,WAAW,CAAC,KAAK,CACrB,mLAAmL,CACpL,CAAC;IACJ,CAAC;CACF;AA5CD,0DA4CC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1708538218473 implements MigrationInterface {\r\n  name = 'Migrations1708538218473';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner_role_relation\" DROP CONSTRAINT \"FK_469c060f976a087f2668fec5d04\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"otp\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"code\" character varying NOT NULL, \"expires_at\" TIMESTAMP NOT NULL, \"updated_at\" TIMESTAMP NOT NULL DEFAULT now(), \"created_at\" TIMESTAMP NOT NULL DEFAULT now(), \"deleted_at\" TIMESTAMP, \"owner_id\" uuid, CONSTRAINT \"PK_32556d9d7b22031d7d0e1fd6723\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner_role_relation\" DROP COLUMN \"business_id\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"business\" DROP COLUMN \"temporary_password\"`,\r\n    );\r\n    await queryRunner.query(`ALTER TABLE \"business\" DROP COLUMN \"password\"`);\r\n    await queryRunner.query(`ALTER TABLE \"business\" DROP COLUMN \"avatar\"`);\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"otp\" ADD CONSTRAINT \"FK_32edb1f3da9f9b7cc25fd91d56f\" FOREIGN KEY (\"owner_id\") REFERENCES \"owner\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"otp\" DROP CONSTRAINT \"FK_32edb1f3da9f9b7cc25fd91d56f\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"business\" ADD \"avatar\" character varying`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"business\" ADD \"password\" character varying NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"business\" ADD \"temporary_password\" boolean NOT NULL DEFAULT true`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner_role_relation\" ADD \"business_id\" uuid`,\r\n    );\r\n    await queryRunner.query(`DROP TABLE \"otp\"`);\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner_role_relation\" ADD CONSTRAINT \"FK_469c060f976a087f2668fec5d04\" FOREIGN KEY (\"business_id\") REFERENCES \"business\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n}\r\n"]}