{"version": 3, "file": "1713453663727-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1713453663727-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAyBnC,CAAC;IAvBQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,6EAA6E,CAC9E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,6EAA6E,CAC9E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,0KAA0K,CAC3K,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,6EAA6E,CAC9E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,kGAAkG,CACnG,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,0KAA0K,CAC3K,CAAC;IACJ,CAAC;CACF;AA1BD,0DA0BC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1713453663727 implements MigrationInterface {\r\n  name = 'Migrations1713453663727';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"favorite_ted\" DROP CONSTRAINT \"FK_a9320fa074e03dc370b5cd2d773\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"favorite_ted\" DROP CONSTRAINT \"REL_a9320fa074e03dc370b5cd2d77\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"favorite_ted\" ADD CONSTRAINT \"FK_a9320fa074e03dc370b5cd2d773\" FOREIGN KEY (\"account_id\") REFERENCES \"account\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"favorite_ted\" DROP CONSTRAINT \"FK_a9320fa074e03dc370b5cd2d773\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"favorite_ted\" ADD CONSTRAINT \"REL_a9320fa074e03dc370b5cd2d77\" UNIQUE (\"account_id\")`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"favorite_ted\" ADD CONSTRAINT \"FK_a9320fa074e03dc370b5cd2d773\" FOREIGN KEY (\"account_id\") REFERENCES \"account\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n}\r\n"]}