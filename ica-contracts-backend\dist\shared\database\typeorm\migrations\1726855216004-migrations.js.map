{"version": 3, "file": "1726855216004-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1726855216004-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAmBnC,CAAC;IAjBQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,gGAAgG,CACjG,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,8FAA8F,CAC/F,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,mDAAmD,CACpD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,qDAAqD,CACtD,CAAC;IACJ,CAAC;CACF;AApBD,0DAoBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1726855216004 implements MigrationInterface {\r\n  name = 'Migrations1726855216004';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract\" ADD \"start_contract\" date NOT NULL DEFAULT '\"2024-09-20T18:00:18.619Z\"'`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract\" ADD \"end_contract\" date NOT NULL DEFAULT '\"2024-09-20T18:00:18.619Z\"'`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract\" DROP COLUMN \"end_contract\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"contract\" DROP COLUMN \"start_contract\"`,\r\n    );\r\n  }\r\n}\r\n"]}