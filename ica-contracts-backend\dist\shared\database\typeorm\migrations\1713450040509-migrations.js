"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1713450040509 = void 0;
class Migrations1713450040509 {
    constructor() {
        this.name = 'Migrations1713450040509';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "app_version" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "android_version" character varying NOT NULL DEFAULT '0', "ios_version" character varying NOT NULL DEFAULT '0', "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_f2573b981a7eac664875e7483ac" PRIMARY KEY ("id"))`);
    }
    async down(queryRunner) {
        await queryRunner.query(`DROP TABLE "app_version"`);
    }
}
exports.Migrations1713450040509 = Migrations1713450040509;
//# sourceMappingURL=1713450040509-migrations.js.map