{"version": 3, "file": "client-payment.dto.js", "sourceRoot": "/", "sources": ["modules/client-app/dto/client-payment.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,yDAAyC;AACzC,qDAMyB;AAEzB,MAAa,sBAAsB;CAwBlC;AAxBD,wDAwBC;AArBC;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,GAAE;;yDACI;AAInB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,GAAE;;uDACE;AAIjB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,sBAAI,EAAC,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;;sDACE;AAM9B;IAJC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;;oDACO;AAMd;IAJC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;;qDACQ", "sourcesContent": ["import { Type } from 'class-transformer';\r\nimport {\r\n  IsOptional,\r\n  IsDateString,\r\n  IsIn,\r\n  IsN<PERSON>ber,\r\n  Min,\r\n} from 'class-validator';\r\n\r\nexport class ClientPaymentsQueryDto {\r\n  @IsOptional()\r\n  @IsDateString()\r\n  startDate?: string;\r\n\r\n  @IsOptional()\r\n  @IsDateString()\r\n  endDate?: string;\r\n\r\n  @IsOptional()\r\n  @IsIn(['PAID', 'SCHEDULED'])\r\n  status?: 'PAID' | 'SCHEDULED';\r\n\r\n  @IsOptional()\r\n  @Type(() => Number)\r\n  @IsNumber()\r\n  @Min(1)\r\n  page?: number;\r\n\r\n  @IsOptional()\r\n  @Type(() => Number)\r\n  @IsNumber()\r\n  @Min(1)\r\n  limit?: number;\r\n}"]}