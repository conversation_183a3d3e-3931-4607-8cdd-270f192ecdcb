"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1708493584449 = void 0;
class Migrations1708493584449 {
    constructor() {
        this.name = 'Migrations1708493584449';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "business" ADD "owner_id" character varying`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "business" DROP COLUMN "owner_id"`);
    }
}
exports.Migrations1708493584449 = Migrations1708493584449;
//# sourceMappingURL=1708493584449-migrations.js.map