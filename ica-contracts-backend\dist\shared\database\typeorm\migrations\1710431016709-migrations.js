"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1710431016709 = void 0;
class Migrations1710431016709 {
    constructor() {
        this.name = 'Migrations1710431016709';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "transaction" ADD "tranferMetada" character varying`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "transaction" DROP COLUMN "tranferMetada"`);
    }
}
exports.Migrations1710431016709 = Migrations1710431016709;
//# sourceMappingURL=1710431016709-migrations.js.map