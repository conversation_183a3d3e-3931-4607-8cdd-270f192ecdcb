"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1741278349607 = void 0;
class Migrations1741278349607 {
    constructor() {
        this.name = 'Migrations1741278349607';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "notification" DROP CONSTRAINT "FK_f9cd0107e86bcd1d22a4ed7125b"`);
        await queryRunner.query(`ALTER TABLE "notification" ALTER COLUMN "contract_id" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "contract_event" DROP CONSTRAINT "FK_196c2c26c5048d8f6760bbc281e"`);
        await queryRunner.query(`ALTER TABLE "contract_event" ALTER COLUMN "responsible_id" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "notification" ADD CONSTRAINT "FK_f9cd0107e86bcd1d22a4ed7125b" FOREIGN KEY ("contract_id") REFERENCES "contract"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract_event" ADD CONSTRAINT "FK_196c2c26c5048d8f6760bbc281e" FOREIGN KEY ("responsible_id") REFERENCES "owner_role_relation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "contract_event" DROP CONSTRAINT "FK_196c2c26c5048d8f6760bbc281e"`);
        await queryRunner.query(`ALTER TABLE "notification" DROP CONSTRAINT "FK_f9cd0107e86bcd1d22a4ed7125b"`);
        await queryRunner.query(`ALTER TABLE "contract_event" ALTER COLUMN "responsible_id" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "contract_event" ADD CONSTRAINT "FK_196c2c26c5048d8f6760bbc281e" FOREIGN KEY ("responsible_id") REFERENCES "owner_role_relation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "notification" ALTER COLUMN "contract_id" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "notification" ADD CONSTRAINT "FK_f9cd0107e86bcd1d22a4ed7125b" FOREIGN KEY ("contract_id") REFERENCES "contract"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
}
exports.Migrations1741278349607 = Migrations1741278349607;
//# sourceMappingURL=1741278349607-migrations.js.map