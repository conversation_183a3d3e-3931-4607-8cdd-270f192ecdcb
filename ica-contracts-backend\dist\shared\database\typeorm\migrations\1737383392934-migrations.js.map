{"version": 3, "file": "1737383392934-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1737383392934-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACI,SAAI,GAAG,yBAAyB,CAAA;IAcpC,CAAC;IAZU,KAAK,CAAC,EAAE,CAAC,WAAwB;QACpC,MAAM,WAAW,CAAC,KAAK,CAAC,sDAAsD,CAAC,CAAC;QAChF,MAAM,WAAW,CAAC,KAAK,CAAC,mDAAmD,CAAC,CAAC;QAC7E,MAAM,WAAW,CAAC,KAAK,CAAC,4KAA4K,CAAC,CAAC;IAC1M,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CAAC,6EAA6E,CAAC,CAAC;QACvG,MAAM,WAAW,CAAC,KAAK,CAAC,sDAAsD,CAAC,CAAC;QAChF,MAAM,WAAW,CAAC,KAAK,CAAC,yEAAyE,CAAC,CAAC;IACvG,CAAC;CAEJ;AAfD,0DAeC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from \"typeorm\";\r\n\r\nexport class Migrations1737383392934 implements MigrationInterface {\r\n    name = 'Migrations1737383392934'\r\n\r\n    public async up(queryRunner: QueryRunner): Promise<void> {\r\n        await queryRunner.query(`ALTER TABLE \"notification\" DROP COLUMN \"contract_id\"`);\r\n        await queryRunner.query(`ALTER TABLE \"notification\" ADD \"contract_id\" uuid`);\r\n        await queryRunner.query(`ALTER TABLE \"notification\" ADD CONSTRAINT \"FK_f9cd0107e86bcd1d22a4ed7125b\" FOREIGN KEY (\"contract_id\") REFERENCES \"contract\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`);\r\n    }\r\n\r\n    public async down(queryRunner: QueryRunner): Promise<void> {\r\n        await queryRunner.query(`ALTER TABLE \"notification\" DROP CONSTRAINT \"FK_f9cd0107e86bcd1d22a4ed7125b\"`);\r\n        await queryRunner.query(`ALTER TABLE \"notification\" DROP COLUMN \"contract_id\"`);\r\n        await queryRunner.query(`ALTER TABLE \"notification\" ADD \"contract_id\" character varying NOT NULL`);\r\n    }\r\n\r\n}\r\n"]}