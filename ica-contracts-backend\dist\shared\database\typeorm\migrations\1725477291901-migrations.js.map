{"version": 3, "file": "1725477291901-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1725477291901-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IA2BnC,CAAC;IAzBQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,oDAAoD,CACrD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wEAAwE,CACzE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wFAAwF,CACzF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,qLAAqL,CACtL,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,wFAAwF,CACzF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,qLAAqL,CACtL,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAC;QAC3E,MAAM,WAAW,CAAC,KAAK,CAAC,0CAA0C,CAAC,CAAC;IACtE,CAAC;CACF;AA5BD,0DA4BC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1725477291901 implements MigrationInterface {\r\n  name = 'Migrations1725477291901';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account\" ADD \"bank\" character varying`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account\" ADD \"is_external\" boolean NOT NULL DEFAULT false`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" DROP CONSTRAINT \"FK_51fc575749fb624f8debaab8c5c\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" ADD CONSTRAINT \"FK_51fc575749fb624f8debaab8c5c\" FOREIGN KEY (\"account_id\") REFERENCES \"account\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" DROP CONSTRAINT \"FK_51fc575749fb624f8debaab8c5c\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"account_transfer-limits\" ADD CONSTRAINT \"FK_51fc575749fb624f8debaab8c5c\" FOREIGN KEY (\"account_id\") REFERENCES \"account\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(`ALTER TABLE \"account\" DROP COLUMN \"is_external\"`);\r\n    await queryRunner.query(`ALTER TABLE \"account\" DROP COLUMN \"bank\"`);\r\n  }\r\n}\r\n"]}