"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClientLoginService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const typeorm_1 = require("@nestjs/typeorm");
const bcrypt = require("bcrypt");
const business_entity_1 = require("../../../shared/database/typeorm/entities/business.entity");
const owner_entity_1 = require("../../../shared/database/typeorm/entities/owner.entity");
const roles_enum_1 = require("../../../shared/enums/roles.enum");
const typeorm_2 = require("typeorm");
const client_account_service_1 = require("./client-account.service");
let ClientLoginService = class ClientLoginService {
    constructor(ownerDb, businessDb, jwtService, clientAccountService) {
        this.ownerDb = ownerDb;
        this.businessDb = businessDb;
        this.jwtService = jwtService;
        this.clientAccountService = clientAccountService;
    }
    async perform(data) {
        if (!data.password && !data.refreshToken) {
            throw new common_1.BadRequestException('Password ou RefreshToken deve ser fornecido');
        }
        const typeDocument = this.cpfOrCnpj(data.document);
        let userId;
        let accessToken;
        let refreshToken;
        if (typeDocument === 'cpf') {
            const owner = await this.ownerDb.findOne({
                relations: ['ownerRoleRelation', 'ownerRoleRelation.role', 'account'],
                where: {
                    cpf: data.document,
                },
            });
            if (owner) {
                if (!owner.account) {
                    throw new common_1.UnauthorizedException();
                }
                const roles = owner.ownerRoleRelation.map((role) => role.role.name);
                if (!roles.includes(roles_enum_1.RolesEnum.INVESTOR)) {
                    throw new common_1.UnauthorizedException('Acesso permitido apenas para investidores');
                }
                if (data.refreshToken) {
                    const matchRefreshToken = await bcrypt.compare(data.refreshToken, owner.refreshToken);
                    if (matchRefreshToken) {
                        accessToken = this.generateToken({
                            cpf: owner.cpf,
                            email: owner.email,
                            id: owner.id,
                            roles,
                        });
                        userId = owner.ownerRoleRelation[0].id;
                    }
                    else {
                        throw new common_1.BadRequestException('Usuário ou refreshToken inválidos');
                    }
                }
                else {
                    const validPassword = await bcrypt.compare(data.password, owner.password);
                    if (validPassword) {
                        accessToken = this.generateToken({
                            cpf: owner.cpf,
                            email: owner.email,
                            id: owner.id,
                            roles,
                        });
                        refreshToken = await this.generateRefreshToken({
                            id: owner.id,
                            document: owner.cpf,
                            name: owner.name,
                            email: owner.email,
                        });
                        userId = owner.ownerRoleRelation[0].id;
                    }
                    else {
                        throw new common_1.BadRequestException('Usuário ou senha inválidos');
                    }
                }
            }
            else {
                throw new common_1.BadRequestException('Usuário ou senha inválidos');
            }
        }
        else if (typeDocument === 'cnpj') {
            const business = await this.businessDb.findOne({
                relations: ['ownerRoleRelation', 'ownerRoleRelation.role', 'account'],
                where: {
                    cnpj: data.document,
                },
            });
            if (business) {
                const roles = business.ownerRoleRelation.map((role) => role.role.name);
                if (!roles.includes(roles_enum_1.RolesEnum.INVESTOR)) {
                    throw new common_1.UnauthorizedException('Acesso permitido apenas para investidores');
                }
                if (data.refreshToken) {
                    const matchRefreshToken = await bcrypt.compare(data.refreshToken, business.refreshToken);
                    if (matchRefreshToken) {
                        accessToken = this.generateToken({
                            cpf: business.cnpj,
                            email: business.email,
                            id: business.id,
                            roles,
                        });
                        userId = business.ownerRoleRelation[0].id;
                    }
                    else {
                        throw new common_1.BadRequestException('Usuário ou refreshToken inválidos');
                    }
                }
                else {
                    const validPassword = await bcrypt.compare(data.password, business.password);
                    if (validPassword) {
                        accessToken = this.generateToken({
                            cpf: business.cnpj,
                            email: business.email,
                            id: business.id,
                            roles,
                        });
                        refreshToken = await this.generateRefreshToken({
                            id: business.id,
                            document: business.cnpj,
                            name: business.companyName,
                            email: business.email,
                        });
                        userId = business.ownerRoleRelation[0].id;
                    }
                    else {
                        throw new common_1.BadRequestException('Usuário ou senha inválidos');
                    }
                }
            }
            else {
                throw new common_1.BadRequestException('Usuário ou senha inválidos');
            }
        }
        else {
            throw new common_1.BadRequestException('Tipo de documento inválido');
        }
        const accountInfo = await this.clientAccountService.getAccountInfo(userId);
        return {
            accessToken,
            refreshToken,
            account: accountInfo,
        };
    }
    cpfOrCnpj(document) {
        if (document.length > 11) {
            return 'cnpj';
        }
        return 'cpf';
    }
    generateToken(data) {
        const token = this.jwtService.sign(data, {
            secret: process.env.JWT_SECRET,
            expiresIn: 3600,
        });
        return token;
    }
    async generateRefreshToken(data) {
        const payload = {
            id: data.id,
            document: data.document,
            name: data.name,
            email: data.email,
        };
        const token = this.jwtService.sign(payload, {
            secret: process.env.JWT_SECRET,
            expiresIn: '1y',
        });
        const cryptToken = await bcrypt.hash(token, 10);
        const typeDocument = this.cpfOrCnpj(data.document);
        if (typeDocument === 'cpf') {
            await this.ownerDb.update(data.id, {
                refreshToken: cryptToken,
            });
        }
        else if (typeDocument === 'cnpj') {
            await this.businessDb.update(data.id, {
                refreshToken: cryptToken,
            });
        }
        return token;
    }
};
exports.ClientLoginService = ClientLoginService;
exports.ClientLoginService = ClientLoginService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(owner_entity_1.OwnerEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(business_entity_1.BusinessEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        jwt_1.JwtService,
        client_account_service_1.ClientAccountService])
], ClientLoginService);
//# sourceMappingURL=client-login.service.js.map