{"version": 3, "file": "data-source.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/data-source.ts"], "names": [], "mappings": ";;;AAAA,mCAAgC;AAChC,qCAAwD;AAExD,IAAA,eAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;AAEZ,QAAA,YAAY,GAAsB;IAC7C,IAAI,EAAE,UAAU;IAChB,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO;IACzB,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC;IACjC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO;IAC7B,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO;IAC7B,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO;IAC7B,WAAW,EAAE,KAAK;IAClB,UAAU,EAAE,CAAC,GAAG,SAAS,kBAAkB,CAAC;IAC5C,QAAQ,EAAE,CAAC,GAAG,SAAS,gBAAgB,CAAC;IACxC,GAAG,EAAE;QACH,kBAAkB,EAAE,KAAK;KAC1B;CACF,CAAC;AAEF,MAAM,UAAU,GAAG,IAAI,oBAAU,CAAC,oBAAY,CAAC,CAAC;AAChD,kBAAe,UAAU,CAAC", "sourcesContent": ["import { config } from 'dotenv';\r\nimport { DataSource, DataSourceOptions } from 'typeorm';\r\n\r\nconfig({ path: '.env' });\r\n\r\nexport const dbDataSource: DataSourceOptions = {\r\n  type: 'postgres',\r\n  host: process.env.DB_HOST,\r\n  port: Number(process.env.DB_PORT),\r\n  username: process.env.DB_USER,\r\n  password: process.env.DB_PASS,\r\n  database: process.env.DB_NAME,\r\n  synchronize: false,\r\n  migrations: [`${__dirname}/migrations/*.ts`],\r\n  entities: [`${__dirname}/entities/*.ts`],\r\n  ssl: {\r\n    rejectUnauthorized: false,\r\n  },\r\n};\r\n\r\nconst dataSource = new DataSource(dbDataSource);\r\nexport default dataSource;\r\n"]}