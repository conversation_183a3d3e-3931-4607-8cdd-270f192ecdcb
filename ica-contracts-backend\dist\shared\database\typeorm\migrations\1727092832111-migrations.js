"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1727092832111 = void 0;
class Migrations1727092832111 {
    constructor() {
        this.name = 'Migrations1727092832111';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "income" DROP COLUMN "income_day"`);
        await queryRunner.query(`ALTER TABLE "income" ADD "income_day" numeric`);
        await queryRunner.query(`ALTER TABLE "income" DROP COLUMN "percentage"`);
        await queryRunner.query(`ALTER TABLE "income" ADD "percentage" numeric`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "income" DROP COLUMN "percentage"`);
        await queryRunner.query(`ALTER TABLE "income" ADD "percentage" integer`);
        await queryRunner.query(`ALTER TABLE "income" DROP COLUMN "income_day"`);
        await queryRunner.query(`ALTER TABLE "income" ADD "income_day" integer`);
    }
}
exports.Migrations1727092832111 = Migrations1727092832111;
//# sourceMappingURL=1727092832111-migrations.js.map