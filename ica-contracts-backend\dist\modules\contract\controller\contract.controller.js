"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContractController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const swagger_1 = require("@nestjs/swagger");
const sorting_params_swagger_decorator_1 = require("../decorators/sorting-params-swagger.decorator");
const sorting_params_decorator_1 = require("../decorators/sorting-params.decorator");
const roles_decorator_1 = require("../../../shared/decorators/roles.decorator");
const roles_enum_1 = require("../../../shared/enums/roles.enum");
const jwt_auth_guard_1 = require("../../../shared/guards/jwt-auth.guard");
const role_guard_1 = require("../../../shared/guards/role.guard");
const add_signataries_dto_1 = require("../dto/add-signataries.dto");
const create_contract_additive_manual_dto_1 = require("../dto/create-contract-additive-manual.dto");
const create_contract_additive_dto_1 = require("../dto/create-contract-additive.dto");
const create_contract_manual_dto_1 = require("../dto/create-contract-manual.dto");
const create_contract_dto_1 = require("../dto/create-contract.dto");
const delete_contract_params_dto_1 = require("../dto/delete-contract-params.dto");
const delete_contract_response_dto_1 = require("../dto/delete-contract-response.dto");
const delete_contract_dto_1 = require("../dto/delete-contract.dto");
const edit_contract_params_dto_1 = require("../dto/edit-contract-params.dto");
const edit_new_contract_dto_1 = require("../dto/edit-new-contract.dto");
const get_contracts_response_dto_1 = require("../dto/get-contracts-response.dto");
const get_contracts_dto_1 = require("../dto/get-contracts.dto");
const get_one_contract_dto_1 = require("../dto/get-one-contract.dto");
const list_contracts_superadmin_dto_1 = require("../dto/list-contracts-superadmin.dto");
const renew_contract_dto_1 = require("../dto/renew-contract.dto");
const upload_proof_payment_addendum_dto_1 = require("../dto/upload-proof-payment-addendum.dto");
const upload_proof_payment_dto_1 = require("../dto/upload-proof-payment.dto");
const contract_sort_enum_1 = require("../enums/contract-sort.enum");
const add_signataries_service_1 = require("../services/add-signataries.service");
const create_contract_additive_manual_service_1 = require("../services/create-contract-additive-manual.service");
const create_contract_additive_service_1 = require("../services/create-contract-additive.service");
const create_contract_manual_service_1 = require("../services/create-contract-manual/create-contract-manual.service");
const create_contract_service_1 = require("../services/create-contract.service");
const delete_contract_service_1 = require("../services/delete-contract.service");
const editt_new_contract_service_1 = require("../services/editt-new-contract.service");
const get_contract_by_investor_service_1 = require("../services/get-contract-by-investor.service");
const get_contract_detail_service_1 = require("../services/get-contract-detail.service");
const get_contracts_service_1 = require("../services/get-contracts.service");
const get_contrat_addendums_by_id_service_1 = require("../services/get-contrat-addendums-by-id.service");
const get_one_contracts_service_1 = require("../services/get-one-contracts.service");
const list_contracts_superadmin_service_1 = require("../services/list-contracts-superadmin.service");
const renew_contract_service_1 = require("../services/renew-contract.service");
const send_notification_service_1 = require("../services/send-notification.service");
const upload_proof_payment_addendum_service_1 = require("../services/upload-proof-payment-addendum.service");
const upload_proof_payment_service_1 = require("../services/upload-proof-payment.service");
let ContractController = class ContractController {
    constructor(createContractService, createContractManualService, getContractsService, getOneContractService, addSignatoriesService, renewContractService, getContractsByInvestorService, uploadProofPaymentService, uploadProofPaymentAddendumService, sendNotificationContract, createContractAdditiveService, createContractAdditiveManualService, listContractsSuperadminService, getContratAddendumsByIdService, getContractDetailService, deleteContractService, editNewContractService) {
        this.createContractService = createContractService;
        this.createContractManualService = createContractManualService;
        this.getContractsService = getContractsService;
        this.getOneContractService = getOneContractService;
        this.addSignatoriesService = addSignatoriesService;
        this.renewContractService = renewContractService;
        this.getContractsByInvestorService = getContractsByInvestorService;
        this.uploadProofPaymentService = uploadProofPaymentService;
        this.uploadProofPaymentAddendumService = uploadProofPaymentAddendumService;
        this.sendNotificationContract = sendNotificationContract;
        this.createContractAdditiveService = createContractAdditiveService;
        this.createContractAdditiveManualService = createContractAdditiveManualService;
        this.listContractsSuperadminService = listContractsSuperadminService;
        this.getContratAddendumsByIdService = getContratAddendumsByIdService;
        this.getContractDetailService = getContractDetailService;
        this.deleteContractService = deleteContractService;
        this.editNewContractService = editNewContractService;
    }
    async createContract(createContractDto, request) {
        const token = request.headers['authorization']?.replace('Bearer ', '') || '';
        return this.createContractService.perform(createContractDto, token);
    }
    async createContractManual(createContractDto, request) {
        console.log('----------dto----------');
        console.log(createContractDto);
        const data = await this.createContractManualService.perform(createContractDto, request.user.id);
        return data;
    }
    async createContractAdditive(createContractDto) {
        return this.createContractAdditiveService.perform(createContractDto);
    }
    async editNewContract(params, editNewContractDto) {
        return await this.editNewContractService.perform(editNewContractDto, params.id);
    }
    async getAllContracts(data, sort) {
        return this.getContractsService.perform({ ...data, sort });
    }
    async getContract(query) {
        return await this.getOneContractService.perform(query);
    }
    async addSignatories(data) {
        return this.addSignatoriesService.perform(data);
    }
    async renewContract(contractId, renewContractDto, files) {
        const oldContractPdfFile = files.oldContractPdf
            ? files.oldContractPdf[0]
            : null;
        const newContractPdfFile = files.newContractPdf
            ? files.newContractPdf[0]
            : null;
        return this.renewContractService.perform(contractId, renewContractDto, oldContractPdfFile, newContractPdfFile);
    }
    async getContracts(investorId) {
        try {
            const contracts = await this.getContractsByInvestorService.perform(investorId);
            return contracts;
        }
        catch (error) {
            console.error(error);
            throw new common_1.HttpException({ error: error.response?.data || error.message }, error.status || error.response?.statusCode || 500);
        }
    }
    async uploadProofPayment(body, proofPayment) {
        try {
            return this.uploadProofPaymentService.perform(body, proofPayment);
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response.data || error.message }, error.status || error.response.statusCode);
        }
    }
    async uploadProofPaymentAddendum(body, proofPayment) {
        try {
            return this.uploadProofPaymentAddendumService.perform(body, proofPayment);
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response.data || error.message }, error.status || error.response.statusCode);
        }
    }
    async sendNotification(contractId) {
        try {
            return this.sendNotificationContract.perform(contractId);
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response.data || error.message }, error.status || error.response.statusCode);
        }
    }
    async createContractAdditiveManual(files, createContractDto) {
        const contractPdfFile = files.contractPdf ? files.contractPdf[0] : null;
        const proofPaymentFile = files.proofPayment ? files.proofPayment[0] : null;
        return this.createContractAdditiveManualService.perform(createContractDto, [
            contractPdfFile,
            proofPaymentFile,
        ]);
    }
    async listContractsSuperadmin(data, sort) {
        return await this.listContractsSuperadminService.perform({ ...data, sort });
    }
    async getContractAddendums(id) {
        return this.getContratAddendumsByIdService.execute(id);
    }
    async getContractDetail(id) {
        return this.getContractDetailService.perform(id);
    }
    async deleteContract(body, req, params) {
        return await this.deleteContractService.perform(body, req.user.id, params.id);
    }
};
exports.ContractController = ContractController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_guard_1.RoleGuard),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.ADVISOR, roles_enum_1.RolesEnum.BROKER, roles_enum_1.RolesEnum.SUPERADMIN),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_contract_dto_1.CreateContractDto, Object]),
    __metadata("design:returntype", Promise)
], ContractController.prototype, "createContract", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: 'Rota para criar um novo contrato',
        description: 'Retorna o id do novo contrato criado',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'id do contrato criado',
        type: get_contracts_response_dto_1.GetContractsResponse,
    }),
    (0, swagger_1.ApiBody)({ type: create_contract_manual_dto_1.CreateNewContractDto }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Requisição inválida.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Não autorizado.' }),
    (0, swagger_1.ApiBearerAuth)('Bearer'),
    (0, common_1.Post)('manual'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_guard_1.RoleGuard),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.ADVISOR, roles_enum_1.RolesEnum.BROKER, roles_enum_1.RolesEnum.ADMIN),
    (0, common_1.HttpCode)(201),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_contract_manual_dto_1.CreateNewContractDto, Object]),
    __metadata("design:returntype", Promise)
], ContractController.prototype, "createContractManual", null);
__decorate([
    (0, common_1.Post)('additive'),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.SUPERADMIN, roles_enum_1.RolesEnum.ADVISOR, roles_enum_1.RolesEnum.BROKER),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_guard_1.RoleGuard),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_contract_additive_dto_1.CreateContractAdditiveDto]),
    __metadata("design:returntype", Promise)
], ContractController.prototype, "createContractAdditive", null);
__decorate([
    (0, common_1.Put)(':id/edit'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_guard_1.RoleGuard),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.BROKER, roles_enum_1.RolesEnum.ADVISOR),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ whitelist: true })),
    (0, swagger_1.ApiOperation)({
        summary: 'Rota para editar um contrato',
        description: 'Edita um contrato existente',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Contrato editado com sucesso',
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Requisição inválida.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Não autorizado.' }),
    (0, swagger_1.ApiBearerAuth)('Bearer'),
    (0, swagger_1.ApiBody)({ type: edit_new_contract_dto_1.EditNewContractDto }),
    __param(0, (0, common_1.Param)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [edit_contract_params_dto_1.EditContractParamsDto,
        edit_new_contract_dto_1.EditNewContractDto]),
    __metadata("design:returntype", Promise)
], ContractController.prototype, "editNewContract", null);
__decorate([
    (0, common_1.Get)('list-contracts'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.ADVISOR, roles_enum_1.RolesEnum.BROKER),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ whitelist: true })),
    (0, swagger_1.ApiOperation)({
        summary: 'Rota para listar contratos de um usuário com role broker ou advisor',
        description: 'Retorna uma lista paginada de contratos filtrados pelos parâmetros de consulta informados.',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Lista paginada de contratos',
        type: get_contracts_response_dto_1.GetContractsResponse,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Requisição inválida.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Não autorizado.' }),
    (0, sorting_params_swagger_decorator_1.ApiSortingQuery)(Object.values(contract_sort_enum_1.ContractSortFieldEnum)),
    (0, swagger_1.ApiBearerAuth)('Bearer'),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, sorting_params_decorator_1.SortingParams)(Object.values(contract_sort_enum_1.ContractSortFieldEnum))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_contracts_dto_1.GetContractsDto, Object]),
    __metadata("design:returntype", Promise)
], ContractController.prototype, "getAllContracts", null);
__decorate([
    (0, common_1.Get)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_one_contract_dto_1.GetContractDto]),
    __metadata("design:returntype", Promise)
], ContractController.prototype, "getContract", null);
__decorate([
    (0, common_1.Post)('add-signatories'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [add_signataries_dto_1.AddSignatariesDto]),
    __metadata("design:returntype", Promise)
], ContractController.prototype, "addSignatories", null);
__decorate([
    (0, common_1.Patch)('renew/:id'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileFieldsInterceptor)([
        { name: 'oldContractPdf', maxCount: 1 },
        { name: 'newContractPdf', maxCount: 1 },
    ])),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.UploadedFiles)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, renew_contract_dto_1.RenewContractDto, Object]),
    __metadata("design:returntype", Promise)
], ContractController.prototype, "renewContract", null);
__decorate([
    (0, common_1.Get)(':investorId'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_guard_1.RoleGuard),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.SUPERADMIN, roles_enum_1.RolesEnum.ADMIN, roles_enum_1.RolesEnum.BROKER, roles_enum_1.RolesEnum.ADVISOR),
    __param(0, (0, common_1.Param)('investorId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ContractController.prototype, "getContracts", null);
__decorate([
    (0, common_1.Put)('upload/proof-payment'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file')),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.UploadedFile)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [upload_proof_payment_dto_1.UploadProofPaymentDto, Object]),
    __metadata("design:returntype", Promise)
], ContractController.prototype, "uploadProofPayment", null);
__decorate([
    (0, common_1.Post)('addendum/proof-payment'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('proofPayment')),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.UploadedFile)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [upload_proof_payment_addendum_dto_1.UploadProofPaymentAddendumDto, Object]),
    __metadata("design:returntype", Promise)
], ContractController.prototype, "uploadProofPaymentAddendum", null);
__decorate([
    (0, common_1.Post)('send-notification/:contractId'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Param)('contractId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ContractController.prototype, "sendNotification", null);
__decorate([
    (0, common_1.Post)('additive-manual'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileFieldsInterceptor)([
        { name: 'contractPdf', maxCount: 1 },
        { name: 'proofPayment', maxCount: 1 },
    ])),
    __param(0, (0, common_1.UploadedFiles)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_contract_additive_manual_dto_1.CreateContractAdditiveManualDto]),
    __metadata("design:returntype", Promise)
], ContractController.prototype, "createContractAdditiveManual", null);
__decorate([
    (0, common_1.Get)('list-contracts/superadmin'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_guard_1.RoleGuard),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.SUPERADMIN),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ whitelist: true })),
    (0, swagger_1.ApiOperation)({
        summary: 'Rota para listar contratos de um usuário com role superadmin',
        description: 'Retorna uma lista paginada de contratos filtrados pelos parâmetros de consulta informados.',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Lista paginada de contratos',
        type: get_contracts_response_dto_1.GetContractsResponse,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Requisição inválida.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Não autorizado.' }),
    (0, swagger_1.ApiBearerAuth)('Bearer'),
    (0, sorting_params_swagger_decorator_1.ApiSortingQuery)(Object.values(contract_sort_enum_1.ContractSortFieldEnum)),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, sorting_params_decorator_1.SortingParams)(Object.values(contract_sort_enum_1.ContractSortFieldEnum))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [list_contracts_superadmin_dto_1.ListContractsSuperadminDto, Object]),
    __metadata("design:returntype", Promise)
], ContractController.prototype, "listContractsSuperadmin", null);
__decorate([
    (0, common_1.Get)(':id/addendum'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ContractController.prototype, "getContractAddendums", null);
__decorate([
    (0, common_1.Get)('get-detail/:id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_guard_1.RoleGuard),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.SUPERADMIN, roles_enum_1.RolesEnum.ADVISOR, roles_enum_1.RolesEnum.BROKER),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ContractController.prototype, "getContractDetail", null);
__decorate([
    (0, common_1.Post)(':id/delete'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_guard_1.RoleGuard),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.BROKER, roles_enum_1.RolesEnum.SUPERADMIN),
    (0, swagger_1.ApiOperation)({
        summary: 'Rota para deletar um contrato',
        description: 'Permite o deletamento de contratos nos seguintes status: rascunho, aguardando auditoria, rejeitado, expirado por auditoria, expirado por investidor ou expirado por falha de prova de pagamento (Super Admin). Broker pode deletar apenas contratos em rascunho.',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Contrato deletado com sucesso',
        type: delete_contract_response_dto_1.DeleteContractResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Contrato não pode ser deletado ou já foi deletado',
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: 'Usuário não autorizado para deletar contratos',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Contrato não encontrado',
    }),
    (0, swagger_1.ApiBearerAuth)('Bearer'),
    (0, swagger_1.ApiBody)({ type: delete_contract_dto_1.DeleteContractDto }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __param(2, (0, common_1.Param)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [delete_contract_dto_1.DeleteContractDto, Object, delete_contract_params_dto_1.ContractIdParamDto]),
    __metadata("design:returntype", Promise)
], ContractController.prototype, "deleteContract", null);
exports.ContractController = ContractController = __decorate([
    (0, swagger_1.ApiTags)('Contract'),
    (0, common_1.Controller)('contract'),
    __metadata("design:paramtypes", [create_contract_service_1.CreateContractService,
        create_contract_manual_service_1.CreateContractManualService,
        get_contracts_service_1.GetContractsService,
        get_one_contracts_service_1.GetOneContractService,
        add_signataries_service_1.AddSignatoriesService,
        renew_contract_service_1.RenewContractService,
        get_contract_by_investor_service_1.GetContractsByInvestorService,
        upload_proof_payment_service_1.UploadProofPaymentService,
        upload_proof_payment_addendum_service_1.UploadProofPaymentAddendumService,
        send_notification_service_1.SendEmailNotificationContract,
        create_contract_additive_service_1.CreateContractAdditiveService,
        create_contract_additive_manual_service_1.CreateContractAdditiveManualService,
        list_contracts_superadmin_service_1.ListContractsSuperadminService,
        get_contrat_addendums_by_id_service_1.GetContratAddendumsByIdService,
        get_contract_detail_service_1.GetContractDetailService,
        delete_contract_service_1.DeleteContractService,
        editt_new_contract_service_1.EditNewContractService])
], ContractController);
//# sourceMappingURL=contract.controller.js.map