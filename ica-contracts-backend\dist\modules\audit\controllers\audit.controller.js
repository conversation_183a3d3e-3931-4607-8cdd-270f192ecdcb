"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditController = void 0;
const common_1 = require("@nestjs/common");
const roles_decorator_1 = require("../../../shared/decorators/roles.decorator");
const roles_enum_1 = require("../../../shared/enums/roles.enum");
const jwt_auth_guard_1 = require("../../../shared/guards/jwt-auth.guard");
const role_guard_1 = require("../../../shared/guards/role.guard");
const audit_contract_dto_1 = require("../dto/audit-contract.dto");
const audit_contract_service_1 = require("../services/audit-contract.service");
const get_contract_audit_history_service_1 = require("../services/get-contract-audit-history.service");
let AuditController = class AuditController {
    constructor(auditContractService, getContractAuditHistoryService) {
        this.auditContractService = auditContractService;
        this.getContractAuditHistoryService = getContractAuditHistoryService;
    }
    async auditContract(auditData, request) {
        return this.auditContractService.auditContract(auditData, request.user.id);
    }
    async getContractAudits(contractId, request) {
        return this.getContractAuditHistoryService.execute(contractId, request.user.id);
    }
};
exports.AuditController = AuditController;
__decorate([
    (0, common_1.Post)('contract'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_guard_1.RoleGuard),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.ADMIN, roles_enum_1.RolesEnum.SUPERADMIN),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [audit_contract_dto_1.AuditContractDto, Object]),
    __metadata("design:returntype", Promise)
], AuditController.prototype, "auditContract", null);
__decorate([
    (0, common_1.Get)('contract/:contractId'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_guard_1.RoleGuard),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.ADMIN, roles_enum_1.RolesEnum.SUPERADMIN, roles_enum_1.RolesEnum.BROKER, roles_enum_1.RolesEnum.ADVISOR, roles_enum_1.RolesEnum.INVESTOR),
    __param(0, (0, common_1.Param)('contractId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AuditController.prototype, "getContractAudits", null);
exports.AuditController = AuditController = __decorate([
    (0, common_1.Controller)('audit'),
    __metadata("design:paramtypes", [audit_contract_service_1.AuditContractService,
        get_contract_audit_history_service_1.GetContractAuditHistoryService])
], AuditController);
//# sourceMappingURL=audit.controller.js.map