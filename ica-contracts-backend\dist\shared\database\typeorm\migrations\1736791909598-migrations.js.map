{"version": 3, "file": "1736791909598-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1736791909598-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAmBnC,CAAC;IAjBQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,mFAAmF,CACpF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,mFAAmF,CACpF,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,kFAAkF,CACnF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,kFAAkF,CACnF,CAAC;IACJ,CAAC;CACF;AApBD,0DAoBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1736791909598 implements MigrationInterface {\r\n  name = 'Migrations1736791909598';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"advisor_goal\" RENAME COLUMN \"amountAchieved\" TO \"perspective_amount\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"broker_goal\" RENAME COLUMN \"amount_achieved\" TO \"perspective_amount\"`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"broker_goal\" RENAME COLUMN \"perspectiveAmount\" TO \"amount_achieved\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"advisor_goal\" RENAME COLUMN \"perspectiveAmount\" TO \"amountAchieved\"`,\r\n    );\r\n  }\r\n}\r\n"]}