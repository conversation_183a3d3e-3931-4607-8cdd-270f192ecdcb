{"version": 3, "file": "1707160914285-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1707160914285-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IA+FnC,CAAC;IA7FQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,2EAA2E,CAC5E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,+LAA+L,CAChM,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,2KAA2K,CAC5K,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,yKAAyK,CAC1K,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,4JAA4J,CAC7J,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,6KAA6K,CAC9K,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC;QAC1E,MAAM,WAAW,CAAC,KAAK,CACrB,oKAAoK,CACrK,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,kKAAkK,CACnK,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,sKAAsK,CACvK,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,mKAAmK,CACpK,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,iKAAiK,CAClK,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,gKAAgK,CACjK,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,gKAAgK,CACjK,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,oKAAoK,CACrK,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,yKAAyK,CAC1K,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,+JAA+J,CAChK,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,sEAAsE,CACvE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,sEAAsE,CACvE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,2EAA2E,CAC5E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,uEAAuE,CACxE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,uEAAuE,CACxE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wEAAwE,CACzE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wEAAwE,CACzE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,yEAAyE,CAC1E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,yEAAyE,CAC1E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,yEAAyE,CAC1E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACvE,MAAM,WAAW,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;QAC9C,MAAM,WAAW,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;QACnD,MAAM,WAAW,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;QAC/C,MAAM,WAAW,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAChD,MAAM,WAAW,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;QACjD,MAAM,WAAW,CAAC,KAAK,CACrB,kKAAkK,CACnK,CAAC;IACJ,CAAC;CACF;AAhGD,0DAgGC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1707160914285 implements MigrationInterface {\r\n  name = 'Migrations1707160914285';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"permission\" DROP CONSTRAINT \"FK_383892d758d08d346f837d3d8b7\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"investor\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"broker_id\" uuid, \"owner_id\" uuid, \"advisor_id\" uuid, CONSTRAINT \"PK_c60a173349549955c39d3703551\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"advisor\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"broker_id\" uuid, \"owner_id\" uuid, CONSTRAINT \"PK_41bd0367a08f2d8c34560a2785e\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"broker\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"admin_id\" uuid, \"owner_id\" uuid, CONSTRAINT \"PK_06617ad8cb3dc7339492a5c995d\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"superadmin\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"owner_id\" uuid, CONSTRAINT \"PK_34da9117b572e9b32a8d829ae84\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"admin\" (\"id\" uuid NOT NULL DEFAULT uuid_generate_v4(), \"superadmin_id\" uuid, \"owner_id\" uuid, CONSTRAINT \"PK_e032310bcef831fb83101899b10\" PRIMARY KEY (\"id\"))`,\r\n    );\r\n    await queryRunner.query(`ALTER TABLE \"permission\" DROP COLUMN \"role_id\"`);\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"investor\" ADD CONSTRAINT \"FK_5aafdad076d9c769718a1a049a2\" FOREIGN KEY (\"broker_id\") REFERENCES \"broker\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"investor\" ADD CONSTRAINT \"FK_2f542bded5a8b34cc53d24d1c5c\" FOREIGN KEY (\"owner_id\") REFERENCES \"owner\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"investor\" ADD CONSTRAINT \"FK_4a98ce7d73b154d63f0aac4889e\" FOREIGN KEY (\"advisor_id\") REFERENCES \"advisor\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"advisor\" ADD CONSTRAINT \"FK_158b4652b9a7c747359c9fe2f09\" FOREIGN KEY (\"broker_id\") REFERENCES \"broker\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"advisor\" ADD CONSTRAINT \"FK_498ad66ddc714e114a26f43533d\" FOREIGN KEY (\"owner_id\") REFERENCES \"owner\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"broker\" ADD CONSTRAINT \"FK_7d049f6cef07839db176c50774c\" FOREIGN KEY (\"admin_id\") REFERENCES \"admin\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"broker\" ADD CONSTRAINT \"FK_9b02767be2adfea38e3fdff47c9\" FOREIGN KEY (\"owner_id\") REFERENCES \"owner\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"superadmin\" ADD CONSTRAINT \"FK_a8f475d250f5bb4ab10977e4ea2\" FOREIGN KEY (\"owner_id\") REFERENCES \"owner\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"admin\" ADD CONSTRAINT \"FK_758bd6ff951dc166552e14a9393\" FOREIGN KEY (\"superadmin_id\") REFERENCES \"superadmin\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"admin\" ADD CONSTRAINT \"FK_b8f113de6f23998118435fa3b98\" FOREIGN KEY (\"owner_id\") REFERENCES \"owner\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"admin\" DROP CONSTRAINT \"FK_b8f113de6f23998118435fa3b98\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"admin\" DROP CONSTRAINT \"FK_758bd6ff951dc166552e14a9393\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"superadmin\" DROP CONSTRAINT \"FK_a8f475d250f5bb4ab10977e4ea2\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"broker\" DROP CONSTRAINT \"FK_9b02767be2adfea38e3fdff47c9\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"broker\" DROP CONSTRAINT \"FK_7d049f6cef07839db176c50774c\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"advisor\" DROP CONSTRAINT \"FK_498ad66ddc714e114a26f43533d\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"advisor\" DROP CONSTRAINT \"FK_158b4652b9a7c747359c9fe2f09\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"investor\" DROP CONSTRAINT \"FK_4a98ce7d73b154d63f0aac4889e\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"investor\" DROP CONSTRAINT \"FK_2f542bded5a8b34cc53d24d1c5c\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"investor\" DROP CONSTRAINT \"FK_5aafdad076d9c769718a1a049a2\"`,\r\n    );\r\n    await queryRunner.query(`ALTER TABLE \"permission\" ADD \"role_id\" uuid`);\r\n    await queryRunner.query(`DROP TABLE \"admin\"`);\r\n    await queryRunner.query(`DROP TABLE \"superadmin\"`);\r\n    await queryRunner.query(`DROP TABLE \"broker\"`);\r\n    await queryRunner.query(`DROP TABLE \"advisor\"`);\r\n    await queryRunner.query(`DROP TABLE \"investor\"`);\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"permission\" ADD CONSTRAINT \"FK_383892d758d08d346f837d3d8b7\" FOREIGN KEY (\"role_id\") REFERENCES \"role\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n}\r\n"]}