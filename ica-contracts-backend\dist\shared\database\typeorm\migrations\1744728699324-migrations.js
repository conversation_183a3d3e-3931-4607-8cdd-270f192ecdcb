"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1744728699324 = void 0;
class Migrations1744728699324 {
    constructor() {
        this.name = 'Migrations1744728699324';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TYPE "public"."contract_audit_decision_enum" AS ENUM('APPROVED', 'REJECTED')`);
        await queryRunner.query(`CREATE TABLE "contract_audit" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "contract_id" uuid NOT NULL, "auditor_id" uuid NOT NULL, "decision" "public"."contract_audit_decision_enum" NOT NULL, "comments" text, "rejection_reasons" jsonb, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_9d297ce3bd10f69e3da1eb0f67f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "contract_audit" ADD CONSTRAINT "FK_896afab3853dbb209938a2cd0a5" FOREIGN KEY ("contract_id") REFERENCES "contract"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contract_audit" ADD CONSTRAINT "FK_a413e0b00469e3ac43ef85f6642" FOREIGN KEY ("auditor_id") REFERENCES "owner_role_relation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "contract_audit" DROP CONSTRAINT "FK_a413e0b00469e3ac43ef85f6642"`);
        await queryRunner.query(`ALTER TABLE "contract_audit" DROP CONSTRAINT "FK_896afab3853dbb209938a2cd0a5"`);
        await queryRunner.query(`DROP TABLE "contract_audit"`);
        await queryRunner.query(`DROP TYPE "public"."contract_audit_decision_enum"`);
    }
}
exports.Migrations1744728699324 = Migrations1744728699324;
//# sourceMappingURL=1744728699324-migrations.js.map