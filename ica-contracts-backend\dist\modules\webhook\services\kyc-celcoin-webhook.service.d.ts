import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { BusinessEntity } from 'src/shared/database/typeorm/entities/business.entity';
import { OwnerEntity } from 'src/shared/database/typeorm/entities/owner.entity';
import { EmailService } from 'src/shared/email/email.service';
import { Repository } from 'typeorm';
import { IKycCelcoinWebhookDto } from '../dto/kyc-celcoin-webhook.dto';
export declare class KycCelcoinWebhookService {
    private accountDb;
    private ownerDb;
    private businessDb;
    private readonly emailService;
    constructor(accountDb: Repository<AccountEntity>, ownerDb: Repository<OwnerEntity>, businessDb: Repository<BusinessEntity>, emailService: EmailService);
    perform(input: IKycCelcoinWebhookDto): Promise<void>;
}
