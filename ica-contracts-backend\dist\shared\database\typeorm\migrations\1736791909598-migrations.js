"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1736791909598 = void 0;
class Migrations1736791909598 {
    constructor() {
        this.name = 'Migrations1736791909598';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "advisor_goal" RENAME COLUMN "amountAchieved" TO "perspective_amount"`);
        await queryRunner.query(`ALTER TABLE "broker_goal" RENAME COLUMN "amount_achieved" TO "perspective_amount"`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "broker_goal" RENAME COLUMN "perspectiveAmount" TO "amount_achieved"`);
        await queryRunner.query(`ALTER TABLE "advisor_goal" RENAME COLUMN "perspectiveAmount" TO "amountAchieved"`);
    }
}
exports.Migrations1736791909598 = Migrations1736791909598;
//# sourceMappingURL=1736791909598-migrations.js.map