{"version": 3, "file": "1734085737849-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1734085737849-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAqBnC,CAAC;IAnBQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,mEAAmE,CACpE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAC;QAC3E,MAAM,WAAW,CAAC,KAAK,CACrB,qEAAqE,CACtE,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAC;QAC3E,MAAM,WAAW,CAAC,KAAK,CACrB,+DAA+D,CAChE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,mEAAmE,CACpE,CAAC;IACJ,CAAC;CACF;AAtBD,0DAsBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1734085737849 implements MigrationInterface {\r\n  name = 'Migrations1734085737849';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"addendum\" RENAME COLUMN \"yield_rate\" TO \"expires_in\"`,\r\n    );\r\n    await queryRunner.query(`ALTER TABLE \"addendum\" DROP COLUMN \"expires_in\"`);\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"addendum\" ADD \"expires_in\" date NOT NULL DEFAULT now()`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(`ALTER TABLE \"addendum\" DROP COLUMN \"expires_in\"`);\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"addendum\" ADD \"expires_in\" numeric(5,2) NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"addendum\" RENAME COLUMN \"expires_in\" TO \"yield_rate\"`,\r\n    );\r\n  }\r\n}\r\n"]}