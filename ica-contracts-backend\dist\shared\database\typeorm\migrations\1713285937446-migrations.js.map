{"version": 3, "file": "1713285937446-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1713285937446-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAenC,CAAC;IAbQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAC;QACrE,MAAM,WAAW,CAAC,KAAK,CACrB,8DAA8D,CAC/D,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAC;QACrE,MAAM,WAAW,CAAC,KAAK,CACrB,oDAAoD,CACrD,CAAC;IACJ,CAAC;CACF;AAhBD,0DAgBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1713285937446 implements MigrationInterface {\r\n  name = 'Migrations1713285937446';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(`ALTER TABLE \"billet\" DROP COLUMN \"amount\"`);\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"billet\" ADD \"amount\" character varying NOT NULL`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(`ALTER TABLE \"billet\" DROP COLUMN \"amount\"`);\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"billet\" ADD \"amount\" integer NOT NULL`,\r\n    );\r\n  }\r\n}\r\n"]}