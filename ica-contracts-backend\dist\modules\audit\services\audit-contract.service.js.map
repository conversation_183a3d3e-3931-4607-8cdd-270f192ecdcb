{"version": 3, "file": "audit-contract.service.js", "sourceRoot": "/", "sources": ["modules/audit/services/audit-contract.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAMwB;AACxB,6CAAmD;AACnD,2GAGoE;AACpE,+FAAsF;AACtF,qHAA0G;AAC1G,qFAA2E;AAC3E,iEAAwD;AACxD,qCAAyC;AAGzC,2IAAgI;AAGzH,IAAM,oBAAoB,GAA1B,MAAM,oBAAoB;IAC/B,YAEU,uBAAwD,EAGxD,kBAA8C,EAG9C,2BAAgE,EAEhE,iCAAoE;QARpE,4BAAuB,GAAvB,uBAAuB,CAAiC;QAGxD,uBAAkB,GAAlB,kBAAkB,CAA4B;QAG9C,gCAA2B,GAA3B,2BAA2B,CAAqC;QAEhE,sCAAiC,GAAjC,iCAAiC,CAAmC;IAC3E,CAAC;IAQJ,KAAK,CAAC,aAAa,CAAC,IAAsB,EAAE,SAAiB;QAE3D,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;YACrD,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE;YAC9B,SAAS,EAAE;gBACT,iBAAiB,EAAE,IAAI;gBACvB,QAAQ,EAAE,IAAI;aACf;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,0BAAiB,CAAC,yBAAyB,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,QAAQ,CAAC,MAAM,KAAK,yCAAkB,CAAC,cAAc,EAAE,CAAC;YAC1D,MAAM,IAAI,qCAA4B,CACpC,mDAAmD,CACpD,CAAC;QACJ,CAAC;QAGD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC;YAC7D,KAAK,EAAE;gBACL;oBACE,OAAO,EAAE,SAAS;oBAClB,IAAI,EAAE,EAAE,IAAI,EAAE,IAAA,YAAE,EAAC,CAAC,sBAAS,CAAC,UAAU,EAAE,sBAAS,CAAC,KAAK,CAAC,CAAC,EAAE;iBAC5D;gBACD;oBACE,UAAU,EAAE,SAAS;oBACrB,IAAI,EAAE,EAAE,IAAI,EAAE,IAAA,YAAE,EAAC,CAAC,sBAAS,CAAC,UAAU,EAAE,sBAAS,CAAC,KAAK,CAAC,CAAC,EAAE;iBAC5D;aACF;YACD,SAAS,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;SAC1B,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,0BAAiB,CAAC,wBAAwB,CAAC,CAAC;QACxD,CAAC;QAGD,IACE,IAAI,CAAC,QAAQ,KAAK,yCAAiB,CAAC,QAAQ;YAC5C,CAAC,CAAC,IAAI,CAAC,gBAAgB;gBACrB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,EAClD,CAAC;YACD,MAAM,IAAI,4BAAmB,CAC3B,wEAAwE,CACzE,CAAC;QACJ,CAAC;QAGD,MAAM,KAAK,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC;YAChD,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,OAAO,CAAC,EAAE;YACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,gBAAgB,EACd,IAAI,CAAC,QAAQ,KAAK,yCAAiB,CAAC,QAAQ;gBAC1C,CAAC,CAAC,IAAI,CAAC,gBAAgB;gBACvB,CAAC,CAAC,IAAI;SACX,CAAC,CAAC;QAGH,IAAI,IAAI,CAAC,QAAQ,KAAK,yCAAiB,CAAC,QAAQ,EAAE,CAAC;YACjD,QAAQ,CAAC,MAAM,GAAG,yCAAkB,CAAC,MAAM,CAAC;QAC9C,CAAC;aAAM,CAAC;YACN,QAAQ,CAAC,MAAM,GAAG,yCAAkB,CAAC,iBAAiB,CAAC;QACzD,CAAC;QAGD,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,EAAE;YAChD,MAAM,EAAE,QAAQ,CAAC,MAAM;SACxB,CAAC,CAAC;QACH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAElE,IAAI,UAAU,CAAC,QAAQ,KAAK,yCAAiB,CAAC,QAAQ,EAAE,CAAC;YACvD,MAAM,IAAI,CAAC,iCAAiC,CAAC,OAAO,CAAC;gBACnD,UAAU,EAAE,IAAI,CAAC,UAAU;aAC5B,CAAC,CAAC;QACL,CAAC;QAED,OAAO;YACL,EAAE,EAAE,UAAU,CAAC,EAAE;YACjB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,SAAS,EAAE,UAAU,CAAC,SAAS;SAChC,CAAC;IACJ,CAAC;CACF,CAAA;AA9GY,oDAAoB;+BAApB,oBAAoB;IADhC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,2CAAmB,CAAC,CAAA;IAGrC,WAAA,IAAA,0BAAgB,EAAC,gCAAc,CAAC,CAAA;IAGhC,WAAA,IAAA,0BAAgB,EAAC,oDAAuB,CAAC,CAAA;qCALT,oBAAU;QAGf,oBAAU;QAGD,oBAAU;QAEJ,wEAAiC;GAXnE,oBAAoB,CA8GhC", "sourcesContent": ["import {\r\n  BadRequestException,\r\n  Injectable,\r\n  NotFoundException,\r\n  UnauthorizedException,\r\n  UnprocessableEntityException,\r\n} from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport {\r\n  ContractAuditEntity,\r\n  AuditDecisionEnum,\r\n} from 'src/shared/database/typeorm/entities/contract-audit.entity';\r\nimport { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';\r\nimport { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';\r\nimport { ContractStatusEnum } from 'src/shared/enums/contract-status.enum';\r\nimport { RolesEnum } from 'src/shared/enums/roles.enum';\r\nimport { In, Repository } from 'typeorm';\r\n\r\nimport { AuditContractDto } from '../dto/audit-contract.dto';\r\nimport { RequestInvestorCredentialsService } from 'src/apis/ica-contract-service/services/request-investor-credentials.service';\r\n\r\n@Injectable()\r\nexport class AuditContractService {\r\n  constructor(\r\n    @InjectRepository(ContractAuditEntity)\r\n    private contractAuditRepository: Repository<ContractAuditEntity>,\r\n\r\n    @InjectRepository(ContractEntity)\r\n    private contractRepository: Repository<ContractEntity>,\r\n\r\n    @InjectRepository(OwnerRoleRelationEntity)\r\n    private ownerRoleRelationRepository: Repository<OwnerRoleRelationEntity>,\r\n\r\n    private requestInvestorCredentialsService: RequestInvestorCredentialsService,\r\n  ) {}\r\n\r\n  /**\r\n   * Realiza a auditoria de um contrato\r\n   * @param data Dados da auditoria\r\n   * @param auditorId ID do auditor\r\n   * @returns Objeto com informações da auditoria realizada\r\n   */\r\n  async auditContract(data: AuditContractDto, auditorId: string) {\r\n    // Verificar se o contrato existe\r\n    const contract = await this.contractRepository.findOne({\r\n      where: { id: data.contractId },\r\n      relations: {\r\n        ownerRoleRelation: true,\r\n        investor: true,\r\n      },\r\n    });\r\n\r\n    if (!contract) {\r\n      throw new NotFoundException('Contrato não encontrado');\r\n    }\r\n\r\n    if (contract.status !== ContractStatusEnum.AWAITING_AUDIT) {\r\n      throw new UnprocessableEntityException(\r\n        'Contrato não está aguardando análise da auditoria',\r\n      );\r\n    }\r\n\r\n    // Verificar se o auditor existe e é um admin ou superadmin\r\n    const auditor = await this.ownerRoleRelationRepository.findOne({\r\n      where: [\r\n        {\r\n          ownerId: auditorId,\r\n          role: { name: In([RolesEnum.SUPERADMIN, RolesEnum.ADMIN]) },\r\n        },\r\n        {\r\n          businessId: auditorId,\r\n          role: { name: In([RolesEnum.SUPERADMIN, RolesEnum.ADMIN]) },\r\n        },\r\n      ],\r\n      relations: { role: true },\r\n    });\r\n\r\n    if (!auditor) {\r\n      throw new NotFoundException('Auditor não encontrado');\r\n    }\r\n\r\n    // Validar dados da decisão\r\n    if (\r\n      data.decision === AuditDecisionEnum.REJECTED &&\r\n      (!data.rejectionReasons ||\r\n        Object.keys(data.rejectionReasons).length === 0)\r\n    ) {\r\n      throw new BadRequestException(\r\n        'É necessário fornecer pelo menos um motivo para a rejeição do contrato',\r\n      );\r\n    }\r\n\r\n    // Criar registro de auditoria\r\n    const audit = this.contractAuditRepository.create({\r\n      contractId: data.contractId,\r\n      auditorId: auditor.id,\r\n      decision: data.decision,\r\n      comments: data.comments,\r\n      rejectionReasons:\r\n        data.decision === AuditDecisionEnum.REJECTED\r\n          ? data.rejectionReasons\r\n          : null,\r\n    });\r\n\r\n    // Atualizar status do contrato com base na decisão\r\n    if (data.decision === AuditDecisionEnum.APPROVED) {\r\n      contract.status = ContractStatusEnum.ACTIVE;\r\n    } else {\r\n      contract.status = ContractStatusEnum.REJECTED_BY_AUDIT;\r\n    }\r\n\r\n    // Salvar as alterações\r\n    await this.contractRepository.update(contract.id, {\r\n      status: contract.status,\r\n    });\r\n    const savedAudit = await this.contractAuditRepository.save(audit);\r\n\r\n    if (savedAudit.decision === AuditDecisionEnum.APPROVED) {\r\n      await this.requestInvestorCredentialsService.perform({\r\n        contractId: data.contractId,\r\n      });\r\n    }\r\n\r\n    return {\r\n      id: savedAudit.id,\r\n      contractId: data.contractId,\r\n      decision: data.decision,\r\n      comments: data.comments,\r\n      rejectionReasons: data.rejectionReasons,\r\n      createdAt: savedAudit.createdAt,\r\n    };\r\n  }\r\n}\r\n"]}