"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1711630449251 = void 0;
class Migrations1711630449251 {
    constructor() {
        this.name = 'Migrations1711630449251';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "billet" ADD "barcode" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "billet" ADD "updated_at" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "billet" ADD "created_at" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "billet" ADD "debtor_name" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "billet" ADD "debtor_document" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "billet" ADD "deleted_at" TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "billet" ALTER COLUMN "due_date" DROP DEFAULT`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "billet" ALTER COLUMN "due_date" SET DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "billet" DROP COLUMN "deleted_at"`);
        await queryRunner.query(`ALTER TABLE "billet" DROP COLUMN "debtor_document"`);
        await queryRunner.query(`ALTER TABLE "billet" DROP COLUMN "debtor_name"`);
        await queryRunner.query(`ALTER TABLE "billet" DROP COLUMN "created_at"`);
        await queryRunner.query(`ALTER TABLE "billet" DROP COLUMN "updated_at"`);
        await queryRunner.query(`ALTER TABLE "billet" DROP COLUMN "barcode"`);
    }
}
exports.Migrations1711630449251 = Migrations1711630449251;
//# sourceMappingURL=1711630449251-migrations.js.map