"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1743801861779 = void 0;
class Migrations1743801861779 {
    constructor() {
        this.name = 'Migrations1743801861779';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "owner" ADD "rg" character varying NOT NULL DEFAULT '0000000'`);
        await queryRunner.query(`ALTER TABLE "owner" ADD "occupation" character varying NOT NULL DEFAULT ''`);
        await queryRunner.query(`ALTER TABLE "owner" ADD "nationality" character varying NOT NULL DEFAULT 'brasileira'`);
        await queryRunner.query(`ALTER TABLE "owner" ADD "issuingAgency" character varying NOT NULL DEFAULT 'SSP'`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "is_debenture" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "contract_number" integer NOT NULL DEFAULT '1'`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "contract_number"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "is_debenture"`);
        await queryRunner.query(`ALTER TABLE "owner" DROP COLUMN "issuingAgency"`);
        await queryRunner.query(`ALTER TABLE "owner" DROP COLUMN "nationality"`);
        await queryRunner.query(`ALTER TABLE "owner" DROP COLUMN "occupation"`);
        await queryRunner.query(`ALTER TABLE "owner" DROP COLUMN "rg"`);
    }
}
exports.Migrations1743801861779 = Migrations1743801861779;
//# sourceMappingURL=1743801861779-migrations.js.map