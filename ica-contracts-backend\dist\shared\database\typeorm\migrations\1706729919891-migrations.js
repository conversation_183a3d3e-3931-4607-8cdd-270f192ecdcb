"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1706729919891 = void 0;
class Migrations1706729919891 {
    constructor() {
        this.name = 'Migrations1706729919891';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "owner" ALTER COLUMN "phone" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "owner" ALTER COLUMN "mother_name" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "owner" ALTER COLUMN "dt_birth" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "owner" ALTER COLUMN "pep" DROP NOT NULL`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "owner" ALTER COLUMN "pep" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "owner" ALTER COLUMN "dt_birth" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "owner" ALTER COLUMN "mother_name" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "owner" ALTER COLUMN "phone" SET NOT NULL`);
    }
}
exports.Migrations1706729919891 = Migrations1706729919891;
//# sourceMappingURL=1706729919891-migrations.js.map