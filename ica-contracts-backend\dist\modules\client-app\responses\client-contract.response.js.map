{"version": 3, "file": "client-contract.response.js", "sourceRoot": "/", "sources": ["modules/client-app/responses/client-contract.response.ts"], "names": [], "mappings": "", "sourcesContent": ["export interface IClientContractAddendumResponse {\r\n  id: string | number;\r\n  type: 'additive';\r\n  date: string;\r\n  invested: number;\r\n  contract: string;\r\n}\r\n\r\nexport interface IClientContractResponse {\r\n  id: string | number;\r\n  type: 'starter';\r\n  date: string;\r\n  invested: number;\r\n  contract: string;\r\n  addendums?: IClientContractAddendumResponse[];\r\n}\r\n"]}