{"version": 3, "file": "1736188204075-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1736188204075-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAuDnC,CAAC;IArDQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,oFAAoF,CACrF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wFAAwF,CACzF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,uEAAuE,CACxE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,uEAAuE,CACxE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,sEAAsE,CACvE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,0DAA0D,CAC3D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,yLAAyL,CAC1L,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,+JAA+J,CAChK,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,uEAAuE,CACxE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,uEAAuE,CACxE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,2DAA2D,CAC5D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,uEAAuE,CACxE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,+JAA+J,CAChK,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,yLAAyL,CAC1L,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,yEAAyE,CAC1E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,sEAAsE,CACvE,CAAC;IACJ,CAAC;CACF;AAxDD,0DAwDC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1736188204075 implements MigrationInterface {\r\n  name = 'Migrations1736188204075';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner\" ADD CONSTRAINT \"UQ_4ba355186545479193a3ed22ba0\" UNIQUE (\"cpf\")`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"business\" ADD CONSTRAINT \"UQ_d871b59fcb850c9870d9d69c472\" UNIQUE (\"cnpj\")`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"report\" DROP CONSTRAINT \"FK_266246ede9f575c5ef3c1c079ec\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"report\" DROP CONSTRAINT \"FK_1ee07d7eee68a3ea0a9a73e1454\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"report\" ALTER COLUMN \"owner_role_relation\" SET NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"report\" ALTER COLUMN \"file_id\" SET NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"report\" ADD CONSTRAINT \"FK_266246ede9f575c5ef3c1c079ec\" FOREIGN KEY (\"owner_role_relation\") REFERENCES \"owner_role_relation\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"report\" ADD CONSTRAINT \"FK_1ee07d7eee68a3ea0a9a73e1454\" FOREIGN KEY (\"file_id\") REFERENCES \"files\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"report\" DROP CONSTRAINT \"FK_1ee07d7eee68a3ea0a9a73e1454\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"report\" DROP CONSTRAINT \"FK_266246ede9f575c5ef3c1c079ec\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"report\" ALTER COLUMN \"file_id\" DROP NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"report\" ALTER COLUMN \"owner_role_relation\" DROP NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"report\" ADD CONSTRAINT \"FK_1ee07d7eee68a3ea0a9a73e1454\" FOREIGN KEY (\"file_id\") REFERENCES \"files\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"report\" ADD CONSTRAINT \"FK_266246ede9f575c5ef3c1c079ec\" FOREIGN KEY (\"owner_role_relation\") REFERENCES \"owner_role_relation\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"business\" DROP CONSTRAINT \"UQ_d871b59fcb850c9870d9d69c472\"`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"owner\" DROP CONSTRAINT \"UQ_4ba355186545479193a3ed22ba0\"`,\r\n    );\r\n  }\r\n}\r\n"]}