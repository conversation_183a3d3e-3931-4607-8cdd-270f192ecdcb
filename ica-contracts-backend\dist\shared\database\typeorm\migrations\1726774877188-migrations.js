"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1726774877188 = void 0;
class Migrations1726774877188 {
    constructor() {
        this.name = 'Migrations1726774877188';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "income" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "income_day" integer, "payment_date" TIMESTAMP, "amount" integer, "percentage" integer, "number_contracts" integer, "number_clients" integer, "contract_type" character varying, "role" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "owner_role_relation" uuid, CONSTRAINT "PK_29a10f17b97568f70cee8586d58" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "income" ADD CONSTRAINT "FK_2542605a11a32fcc24bd4c14a58" FOREIGN KEY ("owner_role_relation") REFERENCES "owner_role_relation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "income" DROP CONSTRAINT "FK_2542605a11a32fcc24bd4c14a58"`);
        await queryRunner.query(`DROP TABLE "income"`);
    }
}
exports.Migrations1726774877188 = Migrations1726774877188;
//# sourceMappingURL=1726774877188-migrations.js.map