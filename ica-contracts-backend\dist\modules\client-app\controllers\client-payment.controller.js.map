{"version": 3, "file": "client-payment.controller.js", "sourceRoot": "/", "sources": ["modules/client-app/controllers/client-payment.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAQwB;AACxB,0EAAgE;AAGhE,kEAAmE;AAKnE,+EAA0E;AAInE,IAAM,uBAAuB,GAA7B,MAAM,uBAAuB;IAClC,YAA6B,oBAA0C;QAA1C,yBAAoB,GAApB,oBAAoB,CAAsB;IAAG,CAAC;IAGrE,AAAN,KAAK,CAAC,WAAW,CACJ,GAA6B,EAC/B,KAA6B;QAEtC,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;QAC3B,OAAO,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IACtE,CAAC;IAGK,AAAN,KAAK,CAAC,cAAc,CACL,SAAiB;QAE9B,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;QACnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,0BAAiB,EAAE,CAAC;gBACvC,MAAM,KAAK,CAAC;YACd,CAAC;YACD,MAAM,IAAI,0BAAiB,CAAC,0BAA0B,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;CACF,CAAA;AAzBY,0DAAuB;AAI5B;IADL,IAAA,YAAG,GAAE;IAEH,WAAA,IAAA,gBAAO,GAAE,CAAA;IACT,WAAA,IAAA,cAAK,GAAE,CAAA;;6CAAQ,2CAAsB;;0DAIvC;AAGK;IADL,IAAA,YAAG,EAAC,KAAK,CAAC;IAER,WAAA,IAAA,cAAK,EAAC,IAAI,CAAC,CAAA;;;;6DAUb;kCAxBU,uBAAuB;IAFnC,IAAA,mBAAU,EAAC,iBAAiB,CAAC;IAC7B,IAAA,kBAAS,EAAC,6BAAY,CAAC;qCAE6B,6CAAoB;GAD5D,uBAAuB,CAyBnC", "sourcesContent": ["import {\r\n  Controller,\r\n  Get,\r\n  Param,\r\n  Query,\r\n  UseGuards,\r\n  Request,\r\n  NotFoundException,\r\n} from '@nestjs/common';\r\nimport { JwtAuthGuard } from 'src/shared/guards/jwt-auth.guard';\r\nimport { IRequestUser } from 'src/shared/interfaces/request-user.interface';\r\n\r\nimport { ClientPaymentsQueryDto } from '../dto/client-payment.dto';\r\nimport {\r\n  IClientPaymentsListResponse,\r\n  IClientPaymentDetailResponse,\r\n} from '../responses/client-payment.response';\r\nimport { ClientPaymentService } from '../services/client-payment.service';\r\n\r\n@Controller('client/payments')\r\n@UseGuards(JwtAuthGuard)\r\nexport class ClientPaymentController {\r\n  constructor(private readonly clientPaymentService: ClientPaymentService) {}\r\n\r\n  @Get()\r\n  async getPayments(\r\n    @Request() req: { user: { id: string } },\r\n    @Query() query: ClientPaymentsQueryDto,\r\n  ): Promise<IClientPaymentsListResponse> {\r\n    const userId = req.user.id;\r\n    return this.clientPaymentService.getPaymentsByUserId(userId, query);\r\n  }\r\n\r\n  @Get(':id')\r\n  async getPaymentById(\r\n    @Param('id') paymentId: string,\r\n  ): Promise<IClientPaymentDetailResponse> {\r\n    try {\r\n      return await this.clientPaymentService.getPaymentById(paymentId);\r\n    } catch (error) {\r\n      if (error instanceof NotFoundException) {\r\n        throw error;\r\n      }\r\n      throw new NotFoundException('Pagamento não encontrado');\r\n    }\r\n  }\r\n}\r\n"]}