{"version": 3, "file": "create-account-celcoin-webhook.service.js", "sourceRoot": "/", "sources": ["modules/webhook/services/create-account-celcoin-webhook.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,6CAAmD;AACnD,+BAA+B;AAC/B,4FAAkF;AAClF,6FAAoF;AACpF,+FAAsF;AACtF,mFAAyE;AACzE,qCAAqC;AAK9B,IAAM,kCAAkC,GAAxC,MAAM,kCAAkC;IAC7C,YAEU,SAAoC,EAEpC,UAAsC,EAEtC,UAA6B;QAJ7B,cAAS,GAAT,SAAS,CAA2B;QAEpC,eAAU,GAAV,UAAU,CAA4B;QAEtC,eAAU,GAAV,UAAU,CAAmB;IACpC,CAAC;IACJ,KAAK,CAAC,OAAO,CAAC,IAAoC;QAChD,IAAI,IAAI,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;YAChC,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CACzB;gBACE,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY;aACnC,EACD;gBACE,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO;gBACjC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM;gBAChC,MAAM,EAAE,uCAAiB,CAAC,WAAW;aACtC,CACF,CAAC;YAEF,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;gBAC3C,SAAS,EAAE;oBACT,KAAK,EAAE,IAAI;oBACX,QAAQ,EAAE;wBACR,qBAAqB,EAAE;4BACrB,KAAK,EAAE,IAAI;yBACZ;qBACF;iBACF;gBACD,KAAK,EAAE;oBACL,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY;iBACnC;aACF,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;gBACjD,KAAK,EAAE;oBACL,SAAS,EAAE,OAAO,CAAC,EAAE;oBACrB,IAAI,EAAE,KAAK;iBACZ;aACF,CAAC,CAAC;YAEH,MAAM,EAAE,GAAG,IAAI,GAAG,CAAC,EAAE,CAAC;gBACpB,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc;gBACvC,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc;gBAC3C,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU;aAC/B,CAAC,CAAC;YAEH,MAAM,QAAQ,GACZ,OAAO,CAAC,IAAI,KAAK,UAAU;gBACzB,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG;gBACnB,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC;YAE1D,MAAM,KAAK,GAAG,MAAM,EAAE;iBACnB,SAAS,CAAC;gBACT,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS;gBAC7B,GAAG,EAAE,YAAY,CAAC,KAAK;aACxB,CAAC;iBACD,OAAO,EAAE,CAAC;YAEb,MAAM,IAAI,GAAG,MAAM,EAAE;iBAClB,SAAS,CAAC;gBACT,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS;gBAC7B,GAAG,EAAE,YAAY,CAAC,IAAI;aACvB,CAAC;iBACD,OAAO,EAAE,CAAC;YAEb,MAAM,IAAI,CAAC,UAAU;iBAClB,aAAa,CAAC;gBACb,cAAc,EAAE,QAAQ;gBACxB,QAAQ,EAAE,YAAY,CAAC,IAAI;gBAC3B,KAAK,EAAE,KAAK,CAAC,IAAc;gBAC3B,KAAK,EAAE,IAAI,CAAC,IAAc;gBAC1B,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;aAC3D,CAAC;iBACD,KAAK,CAAC,KAAK,IAAI,EAAE;gBAChB,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,EAAE;oBACtC,MAAM,EAAE,uCAAiB,CAAC,QAAQ;iBACnC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEL,IAAI,OAAO,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;gBAChC,MAAM,cAAc,GAAG,MAAM,EAAE;qBAC5B,SAAS,CAAC;oBACT,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS;oBAC7B,GAAG,EAAE,YAAY,CAAC,cAAc;iBACjC,CAAC;qBACD,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC;oBAClC,cAAc,EAAE,OAAO,CAAC,QAAQ,CAAC,IAAI;oBACrC,QAAQ,EAAE,iBAAiB;oBAC3B,KAAK,EAAE,cAAc,CAAC,IAAc;oBACpC,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,IAAI;iBAC5B,CAAC,CAAC;gBACH,IAAI,YAAY,CAAC,QAAQ,EAAE,CAAC;oBAC1B,MAAM,QAAQ,GAAG,MAAM,EAAE;yBACtB,SAAS,CAAC;wBACT,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS;wBAC7B,GAAG,EAAE,YAAY,CAAC,QAAQ;qBAC3B,CAAC;yBACD,OAAO,EAAE,CAAC;oBACb,MAAM,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC;wBAClC,cAAc,EAAE,OAAO,CAAC,QAAQ,CAAC,IAAI;wBACrC,QAAQ,EAAE,aAAa;wBACvB,KAAK,EAAE,QAAQ,CAAC,IAAc;wBAC9B,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,IAAI;qBAC5B,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,EAAE;gBAC5C,IAAI,EAAE,IAAI;aACX,CAAC,CAAC;QACL,CAAC;IACH,CAAC;CACF,CAAA;AApHY,gFAAkC;6CAAlC,kCAAkC;IAD9C,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,8BAAa,CAAC,CAAA;IAE/B,WAAA,IAAA,0BAAgB,EAAC,gCAAc,CAAC,CAAA;IAEhC,WAAA,IAAA,eAAM,EAAC,uCAAiB,CAAC,CAAA;qCAHP,oBAAU;QAET,oBAAU;QAEV,uCAAiB;GAP5B,kCAAkC,CAoH9C", "sourcesContent": ["import { Inject, Injectable } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport * as AWS from 'aws-sdk';\r\nimport { KYCCelcoinService } from 'src/apis/celcoin/services/kyc-celcoin.service';\r\nimport { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';\r\nimport { DocumentEntity } from 'src/shared/database/typeorm/entities/document.entity';\r\nimport { AccountStatusEnum } from 'src/shared/enums/account-status.enum';\r\nimport { Repository } from 'typeorm';\r\n\r\nimport { CreateAccountCelcoinWebhookDto } from '../dto/create-acount-celcoin-webhook.dto';\r\n\r\n@Injectable()\r\nexport class CreateAccountCelcoinWebhookService {\r\n  constructor(\r\n    @InjectRepository(AccountEntity)\r\n    private accountDb: Repository<AccountEntity>,\r\n    @InjectRepository(DocumentEntity)\r\n    private documentDb: Repository<DocumentEntity>,\r\n    @Inject(KYCCelcoinService)\r\n    private apiCelcoin: KYCCelcoinService,\r\n  ) {}\r\n  async perform(data: CreateAccountCelcoinWebhookDto) {\r\n    if (data.status === 'CONFIRMED') {\r\n      await this.accountDb.update(\r\n        {\r\n          externalId: data.body.onboardingId,\r\n        },\r\n        {\r\n          number: data.body.account.account,\r\n          branch: data.body.account.branch,\r\n          status: AccountStatusEnum.KYC_PENDING,\r\n        },\r\n      );\r\n\r\n      const account = await this.accountDb.findOne({\r\n        relations: {\r\n          owner: true,\r\n          business: {\r\n            ownerBusinessRelation: {\r\n              owner: true,\r\n            },\r\n          },\r\n        },\r\n        where: {\r\n          externalId: data.body.onboardingId,\r\n        },\r\n      });\r\n\r\n      const sendDocument = await this.documentDb.findOne({\r\n        where: {\r\n          accountId: account.id,\r\n          sent: false,\r\n        },\r\n      });\r\n\r\n      const s3 = new AWS.S3({\r\n        accessKeyId: process.env.AWS_ACCESS_KEY,\r\n        secretAccessKey: process.env.AWS_SECRET_KEY,\r\n        region: process.env.AWS_REGION,\r\n      });\r\n\r\n      const document =\r\n        account.type === 'physical'\r\n          ? account.owner.cpf\r\n          : account.business.ownerBusinessRelation[0].owner.cpf;\r\n\r\n      const front = await s3\r\n        .getObject({\r\n          Bucket: process.env.S3_BUCKET,\r\n          Key: sendDocument.front,\r\n        })\r\n        .promise();\r\n\r\n      const back = await s3\r\n        .getObject({\r\n          Bucket: process.env.S3_BUCKET,\r\n          Key: sendDocument.back,\r\n        })\r\n        .promise();\r\n\r\n      await this.apiCelcoin\r\n        .sendDocuments({\r\n          documentnumber: document,\r\n          filetype: sendDocument.type,\r\n          front: front.Body as Buffer,\r\n          verse: back.Body as Buffer,\r\n          cnpj: account.business ? account.business.cnpj : undefined,\r\n        })\r\n        .catch(async () => {\r\n          await this.accountDb.update(account.id, {\r\n            status: AccountStatusEnum.REPROVED,\r\n          });\r\n        });\r\n\r\n      if (account.type === 'business') {\r\n        const socialContract = await s3\r\n          .getObject({\r\n            Bucket: process.env.S3_BUCKET,\r\n            Key: sendDocument.socialContract,\r\n          })\r\n          .promise();\r\n        await this.apiCelcoin.sendDocuments({\r\n          documentnumber: account.business.cnpj,\r\n          filetype: 'CONTRATO_SOCIAL',\r\n          front: socialContract.Body as Buffer,\r\n          cnpj: account.business.cnpj,\r\n        });\r\n        if (sendDocument.cardCNPJ) {\r\n          const cardCnpj = await s3\r\n            .getObject({\r\n              Bucket: process.env.S3_BUCKET,\r\n              Key: sendDocument.cardCNPJ,\r\n            })\r\n            .promise();\r\n          await this.apiCelcoin.sendDocuments({\r\n            documentnumber: account.business.cnpj,\r\n            filetype: 'CARTAO_CNPJ',\r\n            front: cardCnpj.Body as Buffer,\r\n            cnpj: account.business.cnpj,\r\n          });\r\n        }\r\n      }\r\n\r\n      await this.documentDb.update(sendDocument.id, {\r\n        sent: true,\r\n      });\r\n    }\r\n  }\r\n}\r\n"]}