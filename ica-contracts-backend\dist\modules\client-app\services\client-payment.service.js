"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClientPaymentService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const date_fns_1 = require("date-fns");
const income_payment_scheduled_entity_1 = require("../../../shared/database/typeorm/entities/income-payment-scheduled.entity");
const typeorm_2 = require("typeorm");
let ClientPaymentService = class ClientPaymentService {
    constructor(incomePaymentScheduledRepository) {
        this.incomePaymentScheduledRepository = incomePaymentScheduledRepository;
    }
    async getPaymentsByUserId(userId, filters) {
        const whereClause = {
            contract: { investor: { id: userId } },
        };
        if (filters?.startDate && filters?.endDate) {
            whereClause.scheduledDate = (0, typeorm_2.Raw)((alias) => `DATE(${alias}) BETWEEN :startDate AND :endDate`, {
                startDate: filters.startDate,
                endDate: filters.endDate,
            });
        }
        else if (filters?.startDate) {
            whereClause.scheduledDate = (0, typeorm_2.MoreThanOrEqual)(filters.startDate);
        }
        else if (filters?.endDate) {
            whereClause.scheduledDate = (0, typeorm_2.LessThanOrEqual)(filters.endDate);
        }
        const payments = await this.incomePaymentScheduledRepository.find({
            where: whereClause,
            relations: {
                contract: {
                    signataries: true,
                },
                paymentsDistribution: {
                    payment: true,
                },
            },
            order: {
                scheduledDate: 'DESC',
            },
        });
        if (payments.length === 0) {
            return {
                payments: [],
                total: 0,
                totalPaid: 0,
                totalScheduled: 0,
            };
        }
        let clientPayments = payments.map((payment) => {
            const isPaid = payment.paymentsDistribution?.some((pd) => pd.payment);
            const status = isPaid ? 'PAID' : 'SCHEDULED';
            const paymentResponse = {
                id: payment.id,
                scheduledDate: (0, date_fns_1.format)(new Date(payment.scheduledDate), 'dd/MM/yyyy'),
                amount: parseFloat(payment.amount),
                status,
                paidDate: isPaid && payment.paymentsDistribution[0]?.payment?.createdAt
                    ? (0, date_fns_1.format)(new Date(payment.paymentsDistribution[0].payment.createdAt), 'dd/MM/yyyy')
                    : undefined,
            };
            return { payment: paymentResponse, status };
        });
        if (filters?.status) {
            clientPayments = clientPayments.filter((p) => p.status === filters.status);
        }
        const totalPaid = clientPayments.filter((p) => p.status === 'PAID').length;
        const totalScheduled = clientPayments.filter((p) => p.status === 'SCHEDULED').length;
        const total = clientPayments.length;
        const page = filters?.page && filters.page > 0 ? filters.page : 1;
        const limit = filters?.limit && filters.limit > 0 ? filters.limit : 10;
        const startIndex = (page - 1) * limit;
        const endIndex = page * limit;
        const paginatedPayments = clientPayments
            .slice(startIndex, endIndex)
            .map((p) => p.payment);
        return {
            payments: paginatedPayments,
            total,
            totalPaid,
            totalScheduled,
        };
    }
    async getPaymentById(paymentId) {
        const payment = await this.incomePaymentScheduledRepository.findOne({
            where: { id: paymentId },
            relations: {
                contract: {
                    signataries: true,
                },
                paymentsDistribution: {
                    payment: true,
                },
            },
        });
        if (!payment) {
            throw new common_1.NotFoundException('Pagamento não encontrado');
        }
        const isPaid = payment.paymentsDistribution?.some((pd) => pd.payment);
        const status = isPaid ? 'PAID' : 'SCHEDULED';
        const signatory = payment.contract?.signataries[0];
        const investedAmount = signatory?.investmentValue || 0;
        return {
            id: payment.id,
            scheduledDate: (0, date_fns_1.format)(new Date(payment.scheduledDate), 'dd/MM/yyyy'),
            amount: parseFloat(payment.amount),
            status,
            paidDate: isPaid && payment.paymentsDistribution[0]?.payment?.createdAt
                ? (0, date_fns_1.format)(new Date(payment.paymentsDistribution[0].payment.createdAt), 'dd/MM/yyyy')
                : undefined,
            contractId: payment.contract.id,
            contractType: 'Contrato de Investimento',
            investedAmount,
            bank: signatory?.bank || '',
            agency: signatory?.agency || '',
            account: signatory?.account || '',
            pixKey: '',
        };
    }
};
exports.ClientPaymentService = ClientPaymentService;
exports.ClientPaymentService = ClientPaymentService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(income_payment_scheduled_entity_1.IncomePaymentScheduledEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], ClientPaymentService);
//# sourceMappingURL=client-payment.service.js.map