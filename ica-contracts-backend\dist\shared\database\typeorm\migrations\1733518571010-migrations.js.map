{"version": 3, "file": "1733518571010-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1733518571010-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAqBnC,CAAC;IAnBQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,yJAAyJ,CAC1J,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,4cAA4c,CAC7c,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wKAAwK,CACzK,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,yEAAyE,CAC1E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;QACjD,MAAM,WAAW,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAC;IACvE,CAAC;CACF;AAtBD,0DAsBC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1733518571010 implements MigrationInterface {\r\n  name = 'Migrations1733518571010';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `CREATE TYPE \"public\".\"addendum_status_enum\" AS ENUM('DRAFT', 'SENT_FOR_SIGNATURE', 'PENDING_INVESTOR_SIGNATURE', 'FULLY_SIGNED', 'CANCELED', 'EXPIRED')`,\r\n    );\r\n    await queryRunner.query(\r\n      `CREATE TABLE \"addendum\" (\"id_addendum\" SERIAL NOT NULL, \"status\" \"public\".\"addendum_status_enum\" NOT NULL DEFAULT 'DRAFT', \"application_date\" date NOT NULL, \"addendum_value\" numeric(15,2) NOT NULL, \"yield_rate\" numeric(5,2) NOT NULL, \"reason\" text, \"notes\" text, \"created_at\" TIMESTAMP NOT NULL DEFAULT now(), \"updated_at\" TIMESTAMP NOT NULL DEFAULT now(), \"contract_id\" uuid NOT NULL, CONSTRAINT \"PK_831c7c3e13213217a199e22fbc1\" PRIMARY KEY (\"id_addendum\"))`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"addendum\" ADD CONSTRAINT \"FK_5e577f2ba8c17a9b5420dc8dbc0\" FOREIGN KEY (\"contract_id\") REFERENCES \"contract\"(\"id\") ON DELETE NO ACTION ON UPDATE NO ACTION`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"addendum\" DROP CONSTRAINT \"FK_5e577f2ba8c17a9b5420dc8dbc0\"`,\r\n    );\r\n    await queryRunner.query(`DROP TABLE \"addendum\"`);\r\n    await queryRunner.query(`DROP TYPE \"public\".\"addendum_status_enum\"`);\r\n  }\r\n}\r\n"]}