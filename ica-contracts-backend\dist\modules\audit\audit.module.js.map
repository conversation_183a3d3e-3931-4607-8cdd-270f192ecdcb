{"version": 3, "file": "audit.module.js", "sourceRoot": "/", "sources": ["modules/audit/audit.module.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAAwC;AACxC,6CAAgD;AAChD,wGAAiG;AACjG,4FAAsF;AACtF,kHAA0G;AAC1G,4GAAqG;AACrG,8DAAwD;AACxD,8EAA0E;AAE1E,qEAAiE;AACjE,8EAAyE;AACzE,sGAA+F;AAiBxF,IAAM,WAAW,GAAjB,MAAM,WAAW;CAAG,CAAA;AAAd,kCAAW;sBAAX,WAAW;IAfvB,IAAA,eAAM,EAAC;QACN,OAAO,EAAE;YACP,4BAAY;YACZ,uBAAa,CAAC,UAAU,CAAC;gBACvB,2CAAmB;gBACnB,gCAAc;gBACd,oDAAuB;gBACvB,+CAAqB;aACtB,CAAC;YACF,wCAAkB;SACnB;QACD,WAAW,EAAE,CAAC,kCAAe,CAAC;QAC9B,SAAS,EAAE,CAAC,6CAAoB,EAAE,mEAA8B,CAAC;QACjE,OAAO,EAAE,CAAC,6CAAoB,EAAE,mEAA8B,CAAC;KAChE,CAAC;GACW,WAAW,CAAG", "sourcesContent": ["import { Modu<PERSON> } from '@nestjs/common';\r\nimport { TypeOrmModule } from '@nestjs/typeorm';\r\nimport { ContractAuditEntity } from 'src/shared/database/typeorm/entities/contract-audit.entity';\r\nimport { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';\r\nimport { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';\r\nimport { ContractAdvisorEntity } from 'src/shared/database/typeorm/entities/contract-advisor.entity';\r\nimport { SharedModule } from 'src/shared/shared.module';\r\nimport { NotificationModule } from '../notifications/notification.module';\r\n\r\nimport { AuditController } from './controllers/audit.controller';\r\nimport { AuditContractService } from './services/audit-contract.service';\r\nimport { GetContractAuditHistoryService } from './services/get-contract-audit-history.service';\r\n\r\n@Module({\r\n  imports: [\r\n    SharedModule,\r\n    TypeOrmModule.forFeature([\r\n      ContractAuditEntity,\r\n      ContractEntity,\r\n      OwnerRoleRelationEntity,\r\n      ContractAdvisorEntity,\r\n    ]),\r\n    NotificationModule,\r\n  ],\r\n  controllers: [AuditController],\r\n  providers: [AuditContractService, GetContractAuditHistoryService],\r\n  exports: [AuditContractService, GetContractAuditHistoryService],\r\n})\r\nexport class AuditModule {} "]}