"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddDurationInMonthseColumnToContract1750267202855 = void 0;
class AddDurationInMonthseColumnToContract1750267202855 {
    constructor() {
        this.name = 'AddDurationInMonthseColumnToContract1750267202855';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "contract" ADD "duration_in_months" integer`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "duration_in_months"`);
    }
}
exports.AddDurationInMonthseColumnToContract1750267202855 = AddDurationInMonthseColumnToContract1750267202855;
//# sourceMappingURL=1750267202855-migrations.js.map