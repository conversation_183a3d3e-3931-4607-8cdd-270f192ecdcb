"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1734517914354 = void 0;
class Migrations1734517914354 {
    constructor() {
        this.name = 'Migrations1734517914354';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "notification" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "title" character varying, "message" character varying, "viewed" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "owner_role_relation" uuid, CONSTRAINT "PK_705b6c7cdf9b2c2ff7ac7872cb7" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "notification" ADD CONSTRAINT "FK_227bce406a187fd48c06b102fe0" FOREIGN KEY ("owner_role_relation") REFERENCES "owner_role_relation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "notification" DROP CONSTRAINT "FK_227bce406a187fd48c06b102fe0"`);
        await queryRunner.query(`DROP TABLE "notification"`);
    }
}
exports.Migrations1734517914354 = Migrations1734517914354;
//# sourceMappingURL=1734517914354-migrations.js.map