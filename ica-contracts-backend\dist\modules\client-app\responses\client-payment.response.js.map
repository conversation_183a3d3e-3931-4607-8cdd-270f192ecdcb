{"version": 3, "file": "client-payment.response.js", "sourceRoot": "/", "sources": ["modules/client-app/responses/client-payment.response.ts"], "names": [], "mappings": "", "sourcesContent": ["export interface IClientPaymentResponse {\r\n  id: string;\r\n  scheduledDate: string;\r\n  amount: number;\r\n  status: 'PAID' | 'SCHEDULED';\r\n  paidDate?: string;\r\n}\r\n\r\nexport interface IClientPaymentsListResponse {\r\n  payments: IClientPaymentResponse[];\r\n  total: number;\r\n  totalPaid: number;\r\n  totalScheduled: number;\r\n}\r\n\r\nexport interface IClientPaymentDetailResponse {\r\n  id: string;\r\n  scheduledDate: string;\r\n  amount: number;\r\n  status: 'PAID' | 'SCHEDULED';\r\n  paidDate?: string;\r\n  contractId: string;\r\n  contractType: string;\r\n  investedAmount: number;\r\n  bank?: string;\r\n  agency?: string;\r\n  account?: string;\r\n  pixKey?: string;\r\n}\r\n"]}