{"version": 3, "file": "1727359159796-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1727359159796-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IA2CnC,CAAC;IAzCQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,4DAA4D,CAC7D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,yEAAyE,CAC1E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,6EAA6E,CAC9E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,sEAAsE,CACvE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,uEAAuE,CACxE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,uEAAuE,CACxE,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,sEAAsE,CACvE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,sEAAsE,CACvE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,qEAAqE,CACtE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,4EAA4E,CAC7E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,wEAAwE,CACzE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,2DAA2D,CAC5D,CAAC;IACJ,CAAC;CACF;AA5CD,0DA4CC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1727359159796 implements MigrationInterface {\r\n  name = 'Migrations1727359159796';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pre_register\" ALTER COLUMN \"rg\" DROP NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pre_register\" ALTER COLUMN \"investment_term\" DROP NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pre_register\" ALTER COLUMN \"investment_modality\" DROP NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pre_register\" ALTER COLUMN \"observations\" DROP NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pre_register\" ALTER COLUMN \"purchase_with\" DROP NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pre_register\" ALTER COLUMN \"amount_quotes\" DROP NOT NULL`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pre_register\" ALTER COLUMN \"amount_quotes\" SET NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pre_register\" ALTER COLUMN \"purchase_with\" SET NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pre_register\" ALTER COLUMN \"observations\" SET NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pre_register\" ALTER COLUMN \"investment_modality\" SET NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pre_register\" ALTER COLUMN \"investment_term\" SET NOT NULL`,\r\n    );\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"pre_register\" ALTER COLUMN \"rg\" SET NOT NULL`,\r\n    );\r\n  }\r\n}\r\n"]}