"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1732274435887 = void 0;
class Migrations1732274435887 {
    constructor() {
        this.name = 'Migrations1732274435887';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "contract" ADD "broker_participation_percentage" numeric(5,2)`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "advisor_participation_percentage" numeric(5,2)`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "advisor_participation_percentage"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "broker_participation_percentage"`);
    }
}
exports.Migrations1732274435887 = Migrations1732274435887;
//# sourceMappingURL=1732274435887-migrations.js.map