{"version": 3, "file": "pix-claim-open-celcoin-webhook.service.js", "sourceRoot": "/", "sources": ["modules/webhook/services/pix-claim-open-celcoin-webhook.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6CAAmD;AACnD,6FAAmF;AACnF,+EAAqE;AACrE,mFAAwE;AACxE,mDAA2C;AAC3C,qCAAqC;AAK9B,IAAM,iCAAiC,GAAvC,MAAM,iCAAiC;IAC5C,YAEmB,gBAA0C;QAA1C,qBAAgB,GAAhB,gBAAgB,CAA0B;IAC1D,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,KAAwB;QACpC,eAAM,CAAC,IAAI,CAAC,iDAAiD,EAAE,KAAK,CAAC,CAAC;QAEtE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;YACjD,KAAK,EAAE;gBACL,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG;gBACnB,OAAO,EAAE,EAAE,MAAM,EAAE,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE;aAClE;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO;QACT,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,CAAC,aAAa;YACnC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC;YAClC,CAAC,CAAC,EAAE,CAAC;QAEP,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAChC,EAAE,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,EACjB;YACE,MAAM,EAAE,sCAAgB,CAAC,IAAI;YAC7B,WAAW,EAAE,mCAAe,CAAC,IAAI;YACjC,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,GAAG,QAAQ,EAAE,WAAW,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC;SACxE,CACF,CAAC;IACJ,CAAC;CACF,CAAA;AAjCY,8EAAiC;4CAAjC,iCAAiC;IAD7C,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,6BAAY,CAAC,CAAA;qCACI,oBAAU;GAHpC,iCAAiC,CAiC7C", "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { PixKeyEntity } from 'src/shared/database/typeorm/entities/pix-key.entity';\r\nimport { ClaimStatusEnum } from 'src/shared/enums/claim-status.enum';\r\nimport { PixKeyStatusEnum } from 'src/shared/enums/pix-key-status.enum';\r\nimport { logger } from 'src/shared/logger';\r\nimport { Repository } from 'typeorm';\r\n\r\nimport { IPixDictClaimOpen } from '../dto/pix-claim-open-celcoin-webhook.dto';\r\n\r\n@Injectable()\r\nexport class PixClaimOpenCelcoinWebhookService {\r\n  constructor(\r\n    @InjectRepository(PixKeyEntity)\r\n    private readonly pixKeyRepository: Repository<PixKeyEntity>,\r\n  ) {}\r\n\r\n  async perform(input: IPixDictClaimOpen) {\r\n    logger.info('PixClaimOpenCelcoinWebhookService.perform() -> ', input);\r\n\r\n    const pixKey = await this.pixKeyRepository.findOne({\r\n      where: {\r\n        key: input.body.key,\r\n        account: { number: input.body.claimerAccount.account.toString() },\r\n      },\r\n    });\r\n\r\n    if (!pixKey) {\r\n      return;\r\n    }\r\n\r\n    const metadata = pixKey.claimMetadata\r\n      ? JSON.parse(pixKey.claimMetadata)\r\n      : {};\r\n\r\n    await this.pixKeyRepository.update(\r\n      { id: pixKey.id },\r\n      {\r\n        status: PixKeyStatusEnum.OPEN,\r\n        claimStatus: ClaimStatusEnum.OPEN,\r\n        claimMetadata: JSON.stringify({ ...metadata, openWebhook: input.body }),\r\n      },\r\n    );\r\n  }\r\n}\r\n"]}