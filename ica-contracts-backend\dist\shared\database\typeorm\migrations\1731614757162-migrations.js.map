{"version": 3, "file": "1731614757162-migrations.js", "sourceRoot": "/", "sources": ["shared/database/typeorm/migrations/1731614757162-migrations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,uBAAuB;IAApC;QACE,SAAI,GAAG,yBAAyB,CAAC;IAanC,CAAC;IAXQ,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,KAAK,CACrB,4EAA4E,CAC7E,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,KAAK,CACrB,kEAAkE,CACnE,CAAC;IACJ,CAAC;CACF;AAdD,0DAcC", "sourcesContent": ["import { MigrationInterface, QueryRunner } from 'typeorm';\r\n\r\nexport class Migrations1731614757162 implements MigrationInterface {\r\n  name = 'Migrations1731614757162';\r\n\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"broker_goal\" ADD \"adminOwnerRoleRelationId\" character varying`,\r\n    );\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.query(\r\n      `ALTER TABLE \"broker_goal\" DROP COLUMN \"adminOwnerRoleRelationId\"`,\r\n    );\r\n  }\r\n}\r\n"]}