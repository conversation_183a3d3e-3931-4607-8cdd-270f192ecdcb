"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations1713814827909 = void 0;
class Migrations1713814827909 {
    constructor() {
        this.name = 'Migrations1713814827909';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "statistic" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "investors" integer NOT NULL, "total_applied" numeric(10,2) NOT NULL, "jsonb" character varying NOT NULL, "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, CONSTRAINT "PK_e3e6fd496e1988019d8a46749ae" PRIMARY KEY ("id"))`);
    }
    async down(queryRunner) {
        await queryRunner.query(`DROP TABLE "statistic"`);
    }
}
exports.Migrations1713814827909 = Migrations1713814827909;
//# sourceMappingURL=1713814827909-migrations.js.map