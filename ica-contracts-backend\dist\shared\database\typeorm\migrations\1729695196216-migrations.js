"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations************* = void 0;
class Migrations************* {
    constructor() {
        this.name = 'Migrations*************';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "pre_register" ADD "bank" character varying`);
        await queryRunner.query(`ALTER TABLE "pre_register" ADD "agency" character varying`);
        await queryRunner.query(`ALTER TABLE "pre_register" ADD "account" character varying`);
        await queryRunner.query(`ALTER TABLE "pre_register" ADD "pix" character varying`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "pre_register" DROP COLUMN "pix"`);
        await queryRunner.query(`ALTER TABLE "pre_register" DROP COLUMN "account"`);
        await queryRunner.query(`ALTER TABLE "pre_register" DROP COLUMN "agency"`);
        await queryRunner.query(`ALTER TABLE "pre_register" DROP COLUMN "bank"`);
    }
}
exports.Migrations************* = Migrations*************;
//# sourceMappingURL=*************-migrations.js.map