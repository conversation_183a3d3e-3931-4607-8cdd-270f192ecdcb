"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migrations************* = void 0;
class Migrations************* {
    constructor() {
        this.name = 'Migrations*************';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "favorite_ted" DROP CONSTRAINT "FK_a9320fa074e03dc370b5cd2d773"`);
        await queryRunner.query(`ALTER TABLE "favorite_ted" DROP CONSTRAINT "REL_a9320fa074e03dc370b5cd2d77"`);
        await queryRunner.query(`ALTER TABLE "favorite_ted" ADD CONSTRAINT "FK_a9320fa074e03dc370b5cd2d773" FOREIGN KEY ("account_id") REFERENCES "account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "favorite_ted" DROP CONSTRAINT "FK_a9320fa074e03dc370b5cd2d773"`);
        await queryRunner.query(`ALTER TABLE "favorite_ted" ADD CONSTRAINT "REL_a9320fa074e03dc370b5cd2d77" UNIQUE ("account_id")`);
        await queryRunner.query(`ALTER TABLE "favorite_ted" ADD CONSTRAINT "FK_a9320fa074e03dc370b5cd2d773" FOREIGN KEY ("account_id") REFERENCES "account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
}
exports.Migrations************* = Migrations*************;
//# sourceMappingURL=*************-migrations.js.map